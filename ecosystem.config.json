{"apps": [{"name": "nso-dev", "script": "dist/server/server.mjs", "max_memory_restart": "250M", "max_restarts": 10, "env": {"PORT": 4300}}, {"name": "nso-test", "script": "dist/server/server.mjs", "max_memory_restart": "500M", "exec_mode": "cluster", "instances": "4", "cwd": "/content/apps/nsofe/app", "max_restarts": 10, "env": {"PORT": 30070, "HTTPS_PROXY": "http://pkg-trendency:<EMAIL>:3128"}, "out_file": "/content/logs/nsofe/out.log", "err_file": "/content/logs/nsofe/err.log", "log_type": "json", "time": true, "merge_logs": true}, {"name": "nso-prod", "script": "dist/server/server.mjs", "max_memory_restart": "500M", "exec_mode": "cluster", "instances": "10", "cwd": "/content/apps/nsofe/app", "max_restarts": 10, "env": {"PORT": 30070, "HTTPS_PROXY": "http://trendency-prd:<EMAIL>:3128"}, "out_file": "/content/logs/nsofe/out.log", "err_file": "/content/logs/nsofe/err.log", "log_type": "json", "time": true, "merge_logs": true}]}