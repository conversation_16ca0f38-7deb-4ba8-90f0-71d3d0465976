import { Observable, tap } from 'rxjs';
import { HttpEvent, HttpHandlerFn, HttpRequest, HttpResponse } from '@angular/common/http';
import { environment } from '../../../environments/environment';
import { ApiResult } from '@trendency/kesma-ui';

export function elasticIndexWarningInterceptor(req: HttpRequest<unknown>, next: HttpHandlerFn): Observable<HttpEvent<any>> {
  return next(req).pipe(
    tap((event) => {
      if (!environment.production && event instanceof HttpResponse && event.body) {
        const res = event.body as ApiResult<any>;
        if (res.meta && res.meta['es'] === false) {
          console.error('[ !!! ES INDEX MISS !!! ]' + 'The request made to the following url was missed the ES index. Please correct this mistake!', req);
        }
      }
    })
  );
}
