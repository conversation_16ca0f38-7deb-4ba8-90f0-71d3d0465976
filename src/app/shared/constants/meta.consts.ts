export const defaultMetaInfo = {
  title: 'Nemzeti Sport Online',
  robots: 'index, follow, max-image-preview:large',
  //eslint-disable-next-line max-len
  description: 'Magyarország vezető sportportálja: a legfrissebb sporteredményekkel, hírekkel.',
  ogTitle: 'Nemzeti Sport Online',
  ogImageWidth: '1200',
  ogImageHeight: '600',
  ogLocale: 'hu_HU',
  //eslint-disable-next-line max-len
  ogDescription: 'Nemzeti Sport Online',
  ogSiteName: 'Nemzeti Sport',
  ogType: 'website',
};

export const galleryMetaInfo = {
  title: 'Galéria - Nemzeti Sport',
  robots: 'index, follow, max-image-preview:large',
  ogType: 'gallery',
};
export const weatherMetaInfo = {
  title: 'Napi időjárás, előrejelzés',
  description:
    // eslint-disable-next-line max-len
    'Budapest és Pest megyei települések részletes időjárási adatai, hőmérséklet, csapadék, légnyomás értékek. Mindennap frissülő országos időjárás előrejelzés.',
  keywords: 'időjárás, napi előrejelzés, csapadék, hőmérséklet',
};

/**
 * These additional meta infos can be used for categories which needs unique metadata for SEO.
 * Currently, there are no information about this for NSO.
 * This part is inherited from Metropol, you can check it for more info.
 */
export const categoriesMetaInfo: Record<string, { title: string; description: string; keywords?: string }> = {
  //'aktualis' : {
  //  title: 'Aktuális: friss hírek, információk',
  //eslint-disable-next-line max-len
  //  description: `Folyamatosan frissülő hírek, információk, események Magyarországon és a világban a Metropol Aktuális rovatában.Kövesd és olvasd el nálunk a nap legfontosabb történéseit.`,
  //  keywords: 'friss hírek, információk, időjárás, események'
  //},
};
