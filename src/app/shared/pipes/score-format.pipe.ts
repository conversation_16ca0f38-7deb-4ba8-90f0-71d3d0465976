import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'scoreFormat',
  pure: true,
})
export class ScoreFormatPipe implements PipeTransform {
  transform(score: number): string {
    return this.transformScoreValue(score);
  }

  transformScoreValue(score: number): string {
    let value = '';
    switch (score) {
      case 0:
      case 3:
      case 6:
      case 8:
        value = `${score}-ra`;
        break;
      default:
        value = `${score}-re`;
        break;
    }

    return value;
  }
}
