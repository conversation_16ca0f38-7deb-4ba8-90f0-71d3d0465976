export type MinuteByminuteDefinitions = Readonly<{
  timelineTitle?: string;
  homeTeamName?: string;
  awayTeamName?: string;
  homeTeamImg?: string;
  awaTeamImg?: string;
  posLeft?: string;
  posRight?: string;
  timeLineData?: readonly MinuteByMinuteTimeLineData[];
}>;

export type MinuteByMinuteTimeLineData = Readonly<{
  icon: string;
  time: number;
  percent: string;
  pos: string;
  player: string;
  playerName?: string;
  onSubstitutedPlayerName?: string;
}>;
