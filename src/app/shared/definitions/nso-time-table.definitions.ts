import { Competition, Facilities, LeagueTeam } from './nso-full-page-league-table.definitions';

export type ChampionshipSchedule = Readonly<{
  readonly assistants?: Assistants[];
  readonly awayLineupForm?: string;
  readonly awayScore: string;
  readonly awayTeam: LeagueTeam;
  readonly competition: Competition;
  readonly facility?: Facilities;
  readonly homeLineupForm?: string;
  readonly homeScore: string;
  readonly homeTeam: LeagueTeam;
  readonly id: string;
  readonly information: string;
  readonly phase?: Phase;
  readonly players?: Player[];
  readonly playoffGroup?: string;
  readonly referees?: Referee[];
  readonly round?: string;
  readonly scheduleDate: ScheduleDate;
  readonly scheduleStatus: string;
  readonly scheduleTime: string;
  readonly tvStation: TvStation;
  readonly visitors: number;
  readonly slug: string;
  readonly link?: string | any[];
}>;

export type Assistants = Readonly<{
  readonly firstName: string;
  readonly id: string;
  readonly lastName: string;
}>;

export type TvStation = Readonly<{
  readonly id: string;
  readonly logo: string;
  readonly officialPage: string;
  readonly title: string;
}>;

export type ScheduleDate = Readonly<{
  readonly timezone: string;
  readonly timezone_type: number;
}> & {
  date: Date;
};

export type Phase = Readonly<{
  readonly id: string;
  readonly name: string;
}>;

export type Referee = Readonly<{
  readonly id: string;
  readonly fistName: string;
  readonly lastName: string;
}>;

export type Player = Readonly<{
  readonly birthDate: ScheduleDate;
  readonly competitionTeamId: string;
  readonly competitionTeamName: string;
  readonly competitionTeamPlayerId: string;
  readonly fieldPosition: string;
  readonly firstName: string;
  readonly height: number;
  readonly jersey: string;
  readonly lastName: string;
  readonly nationality: string;
  readonly placeOfBirth: string;
  readonly playerId: string;
  readonly position: string;
  readonly publicName: string;
  readonly scheduleTeamPlayerId: string;
  readonly weight: number;
}>;

export type TimeTableDefinitions = Readonly<{
  date?: string | Date;
  name1?: string;
  name2?: string;
  image1?: string;
  image2?: string;
  time?: string;
  local?: string;
  channel?: string;
}>;
