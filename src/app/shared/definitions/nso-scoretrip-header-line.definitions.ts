export type ScoreTripHeaderLineDefinitions = Readonly<{
  img1: string;
  club1: string;
  color1: string;
  score1: number;
  img2: string;
  club2: string;
  color2: string;
  score2: number;
  matchDate: Date;
  scoreTripTitle: ScoreTripTitleDefinitions;
  isLive?: boolean;
  isFuture?: boolean;
}>;

export type ScoreTripTitleDefinitions = Readonly<{
  championship: string;
  stage?: string;
  round: string;
  playoffGroup?: string;
}>;
