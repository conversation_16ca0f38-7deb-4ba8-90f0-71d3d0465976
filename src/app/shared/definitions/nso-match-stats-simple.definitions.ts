export type MatchStatsSimpleDefinitions = Readonly<{
  teamA: Team;
  teamB: Team;
  statistics: TeamStatistic[];
}>;

type Team = Readonly<{
  teamName: string;
  teamLogo: string;
  shortName: string;
}>;

type TeamStatistic = Readonly<{
  statisticValueTeamA: number;
  statisticType: StatisticTypes;
  statisticValueTeamB: number;
}>;

export enum StatisticTypes {
  BALLPOSSESSION = 'Labdabirtoklás',
  SHOTS = 'Lövések',
  SHOTSONTARGET = 'Kapura lövések',
  PASSES = 'Passzok',
  SUCCESSFULPASSES = 'Sikeres passzok',
  CORNERS = 'Szögletek',
  SAVES = 'Védések',
  FAULTS = 'Szabálytalanságok',
}
