import { Player } from './nso-time-table.definitions';

export interface FootballPitchData {
  awayTeam: FootballPitchTeam;
  homeTeam: FootballPitchTeam;
}

export interface FootballPitchTeam {
  lineUp: string;
  players: Player[];
}

export interface FootballPitchFormation {
  formationName: string;
  positions: FootballPitchPosition[];
}

export interface FootballPitchPosition {
  field: string;
  posY: string;
  posX: string;
}
