export interface TelekomVivicittaListResponse<T> {
  data: T[];
  meta?: TelekomVivicittaMeta;
}

export interface TelekomVivicittaMeta {
  limitable: TelekomVivicittaLimitable;
}

export interface TelekomVivicittaLimitable {
  pageCurrent: number;
  pageMax: number;
  rowAllCount: number;
  rowFrom: number;
  rowOnPageCount: number;
}

export interface TelekomVivicittaListRequest {
  q?: string; // search term
  limit?: number;
  page?: number;
}

export interface TelekomVivicittaArticle {
  title: string;
  lead: string;
  Publishdate: string;
  slug: string;
  leadImage: TelekomVivicittaImage;
  url: string;
}

export interface TelekomVivicittaImage {
  thumbnail: string;
  alt: string;
}
