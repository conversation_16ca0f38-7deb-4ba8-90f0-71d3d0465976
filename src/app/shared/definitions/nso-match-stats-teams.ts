import { TeamDetails } from './nso-match-card.definitions';

export type MatchStatsTeams = Readonly<{
  teamName: string;
  hasTitle: boolean;
  matches: Array<MatchStats>;
}>;

export type MatchStats = Readonly<{
  result: MatchResult;
  teamA: TeamDetails;
  teamB: TeamDetails;
  championshipName: string;
}>;

export enum MatchResult {
  VICTORY = 'győzelem',
  DRAW = 'döntetlen',
  DEFEAT = 'vereség',
}

export type ScoreHighLightDirectiveData = Readonly<{
  data: MatchStats;
  teamName: string | undefined;
  score: number | undefined;
}>;
