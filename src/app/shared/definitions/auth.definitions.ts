export interface BackendUserResponse {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  userName: string;
  newsletter: Date | null;
  passwordLastSave: Date;
}

export enum SocialProvider {
  FACEBOOK = 'facebook',
  GOOGLE = 'google',
}

export interface LoginFormData {
  email: string;
  password: string;
}

export interface BackendUserLoginRequest {
  emailOrUserName: string;
  password: string;
  recaptcha: string;
}

export interface BackendUserLoginResponse {
  token: string;
}

export interface BackendSocialLoginResponse extends BackendUserLoginResponse {
  registrationFinished: boolean;
}

export interface BackendAllowedLoginMethodsResponse {
  email: boolean;
  [SocialProvider.FACEBOOK]: boolean;
  [SocialProvider.GOOGLE]: boolean;
}

export interface RegistrationFormData {
  username: string;
  email?: string;
  password?: string;
  newsletter: boolean;
  terms: boolean;
}

export interface BackendUserRegisterRequest {
  userName: string;
  email?: string;
  plainPassword?: string;
  acceptTerms: boolean;
  newsletter: boolean;
  recaptcha?: string;
}
