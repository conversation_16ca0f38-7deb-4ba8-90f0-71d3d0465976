import { Competition } from './nso-full-page-league-table.definitions';

export enum LatestResultsType {
  MatchesWithScore, // V2 lehet, hogy kelleni fog, de most nem szabad kitenni kiválasztható komponensként.
  Matches,
}
export interface MatchData {
  date: Date;
  matches: Array<MatchDetails>;
}

export interface MatchDetails {
  teamA: MatchTeamDetails;
  teamB: MatchTeamDetails;
}

export interface MatchTeamDetails {
  icon: string;
  name: string;
  slug: string;
  competition: Competition;
  isWinner?: boolean;
  score?: number;
}
