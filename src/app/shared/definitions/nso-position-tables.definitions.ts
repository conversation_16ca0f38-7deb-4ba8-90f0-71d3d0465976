export type PlayerPositionType = Readonly<{
  playerType: PositionType;
  playerTypeName: string;
  players: FieldPlayer[] | GoalkeeperPlayer[] | SubstitionedPlayer[];
}>;

export type PlayerBase = {
  name: string;
  /** Goals */
  g: number;
  /** Fouls committed */
  fc: number;
} & Actions;

export type FieldPlayer = {
  name: string;
  /** Goals */
  g: number;
  /** Assists */
  a?: number;
  /** Shots on target */
  st?: number;
  /** Fouls committed */
  fc: number;
  /** Fouls suffered */
  fs?: number;
} & PlayerBase;

export type GoalkeeperPlayer = {
  /** Saves */
  sv?: number;
} & PlayerBase;

export type SubstitionedPlayer = FieldPlayer & { hasYellowCard?: boolean };

type Actions = {
  actions: PlayerActions[];
  hasSubbed: boolean;
  substitionInfo?: SubstitionInfo;
};

type SubstitionInfo = Readonly<{
  date: string;
  newPlayer: string;
}>;

export enum PositionType {
  GOALKEEPER = 1,
  OTHER = 2,
}

export enum PlayerActions {
  GOAL = 'goal',
  YELLOWCARD = 'yellow card',
  REDCARD = 'red card',
}
