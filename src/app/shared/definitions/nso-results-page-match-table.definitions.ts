export type ResultsPageMatchTable = Readonly<{
  matchDate: Date;
  matches: ResultsPageMatch[];
}>;

export type ResultsPageMatch = Readonly<{
  teamA: TeamMatchTable;
  teamB: TeamMatchTable;
  isLive: boolean;
  hasEnded: boolean;
  channel?: string;
  startDate?: Date | string;
  info?: string;
  link?: string | any[];
}>;

export type TeamMatchTable = {
  name: string;
  teamLogo: string;
  score?: number;
  liveScore?: number;
};

export enum MatchCount {
  FIRST = 1,
  SECOND = 2,
}
