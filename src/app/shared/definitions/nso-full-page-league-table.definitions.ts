export type LeagueTable = Readonly<{
  position: number;
  team: LeagueTeam;
  all: number;
  win: number;
  draw: number;
  lose: number;
  goal: string;
  goal_difference: number;
  point: number;
  last_5_schedule: Schedule[];
  next_schedule: Schedule;
}> & {
  teamFormType: MatchFormEnum[];
};

export type TeamsLeageTable = Readonly<{
  teams: LeagueTable[];
}>;

export type LeagueTeam = Readonly<{
  logo?: string;
  title: string;
  team?: Team;
  id: string;
}>;

export type Competition = Readonly<{
  column?: Column;
  id: string;
  logo?: string;
  position?: number;
  publicTitle?: string;
  season?: Season[];
  slug: string;
  tabellaStatus?: boolean;
  title: string;
}>;

export type Schedule = Readonly<{
  homeScore: string;
  awayScore: string;
  competition: Competition;
  homeTeam: LeagueTeam;
  awayTeam: LeagueTeam;
}>;

export enum MatchFormEnum {
  WIN = 'GY',
  DRAW = 'D',
  DEFEAT = 'V',
}

export type Team = Readonly<{
  columns?: Column[];
  facebook?: string;
  facilities?: Facilities;
  id: string;
  instagram?: string;
  logo?: string;
  name: string;
  officialPage: string;
  shortName?: string;
  slug: string;
  sport: string;
  twitter?: string;
}>;

export type Facilities = Readonly<{
  id: string;
  title: string;
}>;

export type Column = Readonly<{
  id: string;
  slug: string;
  title: string;
}>;

export type Season = Readonly<{
  id: string;
  liveSport: LiveSport;
  seasonEnd: number;
  seasonStart: number;
}>;

export type LiveSport = Readonly<{
  id: string;
  title: string;
}>;
