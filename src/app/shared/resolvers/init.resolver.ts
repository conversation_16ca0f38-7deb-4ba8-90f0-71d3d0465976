import { Injectable } from '@angular/core';
import { InitResolverData, InitResponse, SimplifiedMenuTree } from '@trendency/kesma-ui';
import { forkJoin, Observable, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { ApiService } from '../services/api.service';
import { PortalConfigService } from '../services/portal-config.service';

@Injectable({ providedIn: 'root' })
export class InitResolver {
  constructor(
    private readonly apiService: ApiService,
    private readonly portalConfigService: PortalConfigService
  ) {}

  resolve(): Observable<InitResolverData> {
    return forkJoin([
      this.apiService.init().pipe(
        map(({ data }) => {
          this.portalConfigService.setConfig(data.portalConfigs);
          return data;
        }),
        catchError(() => {
          return of({} as InitResponse);
        })
      ) as Observable<InitResponse>,
      this.apiService.getMenu().pipe(
        catchError(() => {
          return of({} as SimplifiedMenuTree);
        })
      ) as Observable<SimplifiedMenuTree>,
    ]).pipe(map<[InitResponse, SimplifiedMenuTree], InitResolverData>(([init, menu]) => ({ init, menu })));
  }
}
