import { Inject, Injectable, makeStateKey, Optional, TransferState } from '@angular/core';
import { ActivatedRouteSnapshot, Router, UrlTree } from '@angular/router';
import { UtilService, REQUEST } from '@trendency/kesma-core';
import { Request } from 'express';

/**
 * We need to use TransferState between the SSR and browser as the browser cannot access the HTTP headers which are
 * used to make the request for the current page.
 */
const MOBILE_APP_GUARD_STATE_KEY = makeStateKey<boolean>('IS_NSO_MOBILE_APP');

@Injectable({
  providedIn: 'root',
})
export class MobileAppGuard {
  constructor(
    @Optional() @Inject(REQUEST) private readonly req: Request,
    private readonly router: Router,
    private readonly utils: UtilService,
    private readonly transferState: TransferState
  ) {}
  canActivate(route: ActivatedRouteSnapshot): boolean | UrlTree {
    if (this.checkMobileApp() || route.queryParams['forceMobileApp']) {
      return true;
    }
    return this.router.createUrlTree(['/404']);
  }

  checkMobileApp(): boolean {
    if (this.utils.isBrowser()) {
      return this.transferState.get<boolean>(MOBILE_APP_GUARD_STATE_KEY, false);
    }

    const hasHeader = this.req?.headers?.['portal'] === 'nso';
    if (hasHeader) {
      this.transferState.set<boolean>(MOBILE_APP_GUARD_STATE_KEY, true);
    }
    return hasHeader;
  }
}
