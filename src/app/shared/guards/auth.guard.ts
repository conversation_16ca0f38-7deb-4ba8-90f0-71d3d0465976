import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { Observable, of } from 'rxjs';
import { map } from 'rxjs/operators';
import { UtilService } from '@trendency/kesma-core';
import { AuthService } from '../services';

@Injectable({ providedIn: 'root' })
export class AuthGuard {
  constructor(
    private readonly router: Router,
    private readonly authService: AuthService,
    private readonly utilService: UtilService
  ) {}

  canActivate(): Observable<boolean> {
    if (!this.utilService.isBrowser()) {
      return of(false);
    }

    return this.authService.isAuthenticated().pipe(
      map((response: boolean) => {
        if (!response) {
          this.router.navigate([`/bejelentkezes`]);
        }
        return response;
      })
    );
  }
}
