import { DestroyRef, inject, Injectable } from '@angular/core';
import { NavigationStart, Router, Scroll } from '@angular/router';
import { asyncScheduler, finalize, Observable, of, timer } from 'rxjs';
import { filter, observeOn, switchMap, takeUntil } from 'rxjs/operators';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

@Injectable({
  providedIn: 'root',
})
export class ScrollPositionService {
  private readonly destroyRef = inject(DestroyRef);
  private readonly router = inject(Router);

  setupScrollPositionListener(): void {
    const html = document.querySelector('html')!;
    const windowHeight = window.innerHeight;
    const navStart$ = this.router.events.pipe(filter((e) => e instanceof NavigationStart)) as Observable<NavigationStart>;

    (this.router.events.pipe(filter((e) => e instanceof Scroll)) as Observable<Scroll>)
      .pipe(
        filter((event: Scroll) => !!event.position),
        observeOn(asyncScheduler),
        switchMap((event: Scroll) => {
          const hasPos = event.position;
          if (!hasPos) {
            return of(null);
          }
          html.style.minHeight = `${hasPos[1] + windowHeight}px`;
          window.scrollTo(...hasPos);
          return timer(10_000).pipe(
            finalize(() => (html.style.minHeight = '')),
            takeUntil(navStart$)
          );
        }),
        takeUntilDestroyed(this.destroyRef)
      )
      .subscribe();
  }
}
