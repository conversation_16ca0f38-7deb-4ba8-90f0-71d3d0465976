import { inject, Injectable } from '@angular/core';
import {
  ApiResponseMetaList,
  ApiResult,
  BackendMultiVoteData,
  backendMultiVoteDataToVoteData,
  MultiVoteDataWithVotedId,
  MultiVoteService,
} from '@trendency/kesma-ui';
import { ReqService } from '@trendency/kesma-core';
import { Observable, of } from 'rxjs';
import { catchError, map, shareReplay } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class MultiVotingCacheService {
  readonly multiVoteCache: Record<string, Observable<MultiVoteDataWithVotedId | undefined>> = {};

  private readonly reqService = inject(ReqService);
  private readonly multiVoteService = inject(MultiVoteService);

  setMultiVote(voteId: string, endDate?: string): void {
    this.multiVoteCache[voteId] = this.getMultiVoteData(voteId, endDate);
  }

  private getMultiVoteData(voteId: string, endDate?: string): Observable<MultiVoteDataWithVotedId | undefined> {
    return this.reqService.get<ApiResult<BackendMultiVoteData, ApiResponseMetaList>>(`/multi-vote/multi-vote-information/${voteId}`).pipe(
      map(({ data }) =>
        this.multiVoteService.getVoteData(
          backendMultiVoteDataToVoteData({
            ...data,
            id: voteId,
            endDate,
          })
        )
      ),
      shareReplay({ bufferSize: 1, refCount: true }),
      catchError(() => {
        return of(undefined);
      })
    );
  }
}
