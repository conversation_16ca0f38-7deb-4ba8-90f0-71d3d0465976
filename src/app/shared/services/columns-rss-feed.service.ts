import { Inject, Injectable, Renderer2, RendererFactory2 } from '@angular/core';
import { Column } from '@trendency/kesma-ui';
import { environment } from '../../../environments/environment';
import { EnvironmentApiUrl } from '@trendency/kesma-core/lib/definitions/environment.definitions';
import { DOCUMENT } from '@angular/common';
import { ColumnRssData } from '../constants/common.consts';

@Injectable({ providedIn: 'root' })
export class ColumnsRssFeedService {
  private readonly renderer: Renderer2;

  constructor(
    @Inject(DOCUMENT) private readonly document: Document,
    private readonly rendererFactory: RendererFactory2
  ) {
    this.renderer = this.rendererFactory.createRenderer(null, null);
  }

  addRSS(): void {
    for (const column of ColumnRssData) {
      this.create(column as Column);
    }
  }

  private create(column: Column): void {
    const linkElement = this.renderer.createElement('link');
    this.renderer.setAttribute(linkElement, 'rel', 'alternate');
    this.renderer.setAttribute(linkElement, 'type', 'application/rss+xml');
    this.renderer.setAttribute(linkElement, 'title', column?.title ?? '');
    this.renderer.setAttribute(linkElement, 'href', this.getColumnRssFeedUrl(column?.slug ?? ''));
    this.renderer.setAttribute(linkElement, `data-column-rss${column?.slug ? '-' + column?.slug : ''}`, '');

    this.renderer.appendChild(this.headElement, linkElement);
  }

  private getColumnRssFeedUrl(slug: string): string {
    const urlParts = [`${(environment.apiUrl as EnvironmentApiUrl).clientApiUrl || environment.apiUrl}/rss/nso`, ...(slug ? [slug] : []), 'articles'];
    return urlParts.join('/');
  }

  private get headElement(): HTMLHeadElement {
    return this.document.head || this.document.getElementsByTagName('head')?.[0];
  }
}
