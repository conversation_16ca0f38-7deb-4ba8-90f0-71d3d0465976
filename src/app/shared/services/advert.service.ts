import { DOCUMENT } from '@angular/common';
import { inject, Inject, Injectable, Injector, runInInjectionContext, Signal } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { UtilService } from '@trendency/kesma-core';
import { ScriptLoaderService } from '@trendency/kesma-ui';
import { BehaviorSubject, interval, Subject, takeUntil, timer } from 'rxjs';
import { filter, map, take, tap } from 'rxjs/operators';
import { IosAppService } from './ios-app.service';

const NATIV_ZONA_JS_SRC = 'https://related.hu/js/v2/widget.js';

@Injectable({
  providedIn: 'root',
})
export class AdvertService {
  constructor(
    private readonly injector: Injector,
    private readonly utilsService: UtilService,
    private readonly scriptLoaderService: ScriptLoaderService,
    @Inject(DOCUMENT) private readonly document: Document
  ) {}

  readonly #iosAppService: IosAppService = inject(IosAppService);
  isIos: Signal<boolean> = this.#iosAppService.isIosApp;
  destroyInterval$ = new Subject<void>();
  readonly enableAdsSubject = new BehaviorSubject<boolean>(true);

  disableAds(): void {
    this.enableAdsSubject.next(false);
    this.advertInterval();
  }

  enableAds(): void {
    this.enableAdsSubject.next(true);
    this.destroyInterval$.next();
    this.advertInterval();
  }

  isAdEnabled(): boolean {
    return this.enableAdsSubject.getValue();
  }

  callAdvertScriptOnNavigation(): void {
    if (!this.utilsService.isBrowser()) {
      return;
    }
    setTimeout(() => {
      if (this.isAdEnabled()) {
        window.console.warn('>> calling __adsConfig');

        // Related.hu ads
        this.scriptLoaderService.deleteScriptBySrc(NATIV_ZONA_JS_SRC);
        this.removeRtlElements();
        this.addRtlWidget();
        this.scriptLoaderService.loadScript(NATIV_ZONA_JS_SRC, true, false);
        // Related.hu ads

        this.reloadAds();
      } else {
        window.console.warn('>> clearing ads');

        // Related.hu ads

        this.removeRtlElements();
        // Related.hu ads
        this.clearAds();
      }
    }, 1000);
  }

  advertInterval(): void {
    if (!this.utilsService.isBrowser()) {
      return;
    }

    if (this.isAdEnabled()) {
      this.removeClassFromElements();

      console.warn('%c>> enableAds', 'color: yellow; background:red; font-weight: bold;');

      return;
    }

    const advertCleaner$ = interval(500).pipe(takeUntil(timer(20000)), takeUntil(this.destroyInterval$));
    advertCleaner$.subscribe(() => {
      if (!this.isAdEnabled()) {
        console.warn('%c>> disableAds', 'color: green; background:black; font-weight: bold;');
        this.addClassToElements();
        this.clearAds();
      }
    });
  }

  // Related.hu ads
  addClassToElements(className = 'ads_show_ad_title', customClass = 'hide-ad'): void {
    const elements = this.document.getElementsByClassName(className);

    for (let i = 0; i < elements?.length; i++) {
      elements[i].classList.add(customClass);
    }
  }
  addRtlWidget(): void {
    const parentElement = this.document.querySelector('.rtl-container');

    if (parentElement) {
      const newDiv = this.document.createElement('div');
      newDiv.classList.add('rltdwidget');
      newDiv.setAttribute('data-widget-id', '6710261');
      parentElement.appendChild(newDiv);
    }
  }
  // Related.hu ads

  removeRtlElements(className = 'rltdwidget', customAttribute = 'data-widget-id'): void {
    this.scriptLoaderService.deleteScriptBySrc(NATIV_ZONA_JS_SRC);

    const elementsByClass = this.document.getElementsByClassName(className);
    const elementsByAttribute = this.document.querySelectorAll(`[${customAttribute}]`);

    Array.from(elementsByClass).forEach((element) => element.remove());

    elementsByAttribute.forEach((element) => element.remove());
  }

  removeClassFromElements(className = 'ads_show_ad_title', customClass = 'hide-ad'): void {
    const elements = this.document.getElementsByClassName(className);
    for (let i = 0; i < elements.length; i++) {
      elements[i].classList.remove(customClass);
    }
  }

  clearAds(): void {
    if (!this.utilsService.isBrowser() || window?.__adsConfig === undefined) return;
    window.__adsConfig?.clearAds();
  }

  reloadAds(): void {
    if (!this.utilsService.isBrowser() || window?.__adsConfig === undefined) return;

    window.__adsConfig?.spaHardReset();
    window.__adsConfig?.reinsertStrossle();
  }

  destroyIosAdvert(): void {
    const googletag = (window.googletag = window.googletag || ({ cmd: [] } as any)) as any;
    googletag.destroySlots();
  }

  removeIosAdElementWithDelay(): void {
    if (!this.utilsService.isBrowser() || !this.isIos()) {
      return;
    }

    timer(1500, 500)
      .pipe(
        map(() => this.document.getElementById('Nso_leaderboard_top_1')),
        tap((adElement: HTMLElement | null) => console.log('AD ELEMENT FOR REMOVAL', adElement?.id)),
        filter((adElement: HTMLElement | null): adElement is HTMLElement => !!adElement),
        take(1)
      )
      .subscribe((adElement: HTMLElement) => {
        adElement.remove();
        console.log('AD ELEMENT REMOVED');
      });
  }

  loadStrossleScript(): void {
    if (!this.utilsService.isBrowser() || !this.document) {
      return;
    }
    setTimeout(() => {
      runInInjectionContext(this.injector, () => {
        const activatedRoute = inject(ActivatedRoute, { optional: true });
        if (activatedRoute?.snapshot?.firstChild?.data?.['skipSsrConditionalElements']) {
          console.warn('>> skipping strossle script');
          return;
        }
        const todayDate = new Date().toISOString().slice(0, 10).replace(/-/g, '');

        const strossleSrc = `https://cdn-alpha.adsinteractive.com/nemzetisport.hu.js?v=${todayDate}`;

        const scriptElement = this.scriptLoaderService.loadScript(strossleSrc, true, false);

        // Related.hu ads
        this.scriptLoaderService.loadScript(NATIV_ZONA_JS_SRC, true, false);
        // Related.hu ads
        scriptElement.addEventListener('load', () => {
          console.warn('>> strossle loaded successfully');
          window.adsDoNotServeAds = false;
        });
      });
    }, 500);
  }
}
