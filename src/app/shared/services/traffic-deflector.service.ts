import { inject, Injectable } from '@angular/core';
import { catchError, map, Observable, of } from 'rxjs';
import { environment } from 'src/environments/environment';
import { ExternalBrandingBoxArticle, PersonalizedRecommendationApiResponse } from '@trendency/kesma-ui';
import { mapTrafficDeflectorArticlesToBrandingBoxArticle } from '../utils';
import { CleanHttpService } from './clean-http.service';

@Injectable({
  providedIn: 'root',
})
export class TrafficDeflectorService {
  private readonly httpService = inject(CleanHttpService);

  private get deflectorApiUrl(): string {
    return environment.type === 'beta' ? 'https://terelo.app.content.private/api' : (environment.personalizedRecommendationApiUrl as string);
  }

  getTrafficDeflectorData(traffickingPlatforms: string, articleLimit: number): Observable<ExternalBrandingBoxArticle[] | undefined> {
    return this.httpService
      .get<PersonalizedRecommendationApiResponse>(`${this.deflectorApiUrl}/recommendation`, {
        params: {
          traffickingPlatforms,
          utmSource: 'nemzetisport.hu',
          withoutPos: '1',
        },
      })
      .pipe(
        map((data) => data?.[traffickingPlatforms]?.map(mapTrafficDeflectorArticlesToBrandingBoxArticle)?.slice(0, articleLimit)),
        catchError(() => {
          return of(undefined);
        })
      );
  }
}
