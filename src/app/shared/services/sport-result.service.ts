import { Injectable } from '@angular/core';
import { backendDateToDate, ReqService } from '@trendency/kesma-core';
import { ApiResult, ChampionshipSchedule, SingleElimination } from '@trendency/kesma-ui';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { CompetitionSummary, ScheduleByCompetitions } from '../../shared/definitions/sport-results.definitions';

@Injectable({
  providedIn: 'root',
})
export class SportResultService {
  constructor(private readonly reqService: ReqService) {}

  getSchedulesGroupedByRound(competitionSlug: string): Observable<SingleElimination> {
    return this.reqService.get<{ data: SingleElimination }>(`/sport/schedule/groupped-by-round/${competitionSlug}`).pipe(map(({ data }) => data));
  }

  getCompetitionSummary(competitionSlug: string, phaseId?: string): Observable<CompetitionSummary[]> {
    const params = phaseId ? { phaseId } : {};

    return this.reqService.get<{ data: CompetitionSummary[] }>(`sport/competition/${competitionSlug}/summary`, { params }).pipe(map(({ data }) => data));
  }

  getScheduleByCompetition(competitionSlug: string): Observable<ChampionshipSchedule[]> {
    return this.reqService.get<ApiResult<ScheduleByCompetitions>>(`/sport/schedule/by-competition/${competitionSlug}`).pipe(
      map(
        ({ data: { schedules } }) =>
          schedules.map((schedule) => ({
            ...schedule,
            scheduleDate: { ...schedule.scheduleDate, date: backendDateToDate(schedule.scheduleDate.date.toString()) },
          })) as ChampionshipSchedule[]
      )
    );
  }

  getScheduleByDate(competitionSlug: string, date: string): Observable<ChampionshipSchedule[]> {
    return this.reqService.get<ApiResult<ScheduleByCompetitions>>(`/sport/schedule/by-date/${competitionSlug}/${date}`).pipe(
      map(
        ({ data: { schedules } }) =>
          schedules.map((schedule) => ({
            ...schedule,
            scheduleDate: { ...schedule.scheduleDate, date: backendDateToDate(schedule.scheduleDate.date.toString()) },
          })) as ChampionshipSchedule[]
      )
    );
  }
}
