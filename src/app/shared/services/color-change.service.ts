import { Injectable, signal } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class ColorChangeService {
  private readonly DEFAULT_RED_TO_YELLOW = 'var(--kui-red-400)';
  private readonly DEFAULT_WHITE_TO_GRAY = 'var(--kui-white)';
  private readonly DEFAULT_WHITE_TO_BLACK = 'var(--kui-white)';
  private readonly DEFAULT_WHITE_TO_TURQUOISE = 'var(--kui-white)';
  private readonly DEFAULT_WHITE_TO_DARK_YELLOW = 'var(--kui-white)';
  private readonly DEFAULT_GRAY_TO_LIGHTGRAY = 'var(--kui-gray-550)';

  isCsupasport = signal<boolean>(false);
  isHatsofuves = signal<boolean>(false);
  redToYellow = signal<string>(this.DEFAULT_RED_TO_YELLOW);
  whiteToGray = signal<string>(this.DEFAULT_WHITE_TO_GRAY);
  whiteToBlack = signal<string>(this.DEFAULT_WHITE_TO_BLACK);
  whiteToTurquoise = signal<string>(this.DEFAULT_WHITE_TO_TURQUOISE);
  whiteToDarkYellow = signal<string>(this.DEFAULT_WHITE_TO_DARK_YELLOW);
  grayToLightGray = signal<string>(this.DEFAULT_GRAY_TO_LIGHTGRAY);

  setIsCsupasport(bool: boolean): void {
    this.isCsupasport.set(bool ?? false);
  }
  setIsHatsoFuves(bool: boolean): void {
    this.isHatsofuves.set(bool ?? false);
  }
  setRedToYellow(color?: string): void {
    this.redToYellow.set(color ?? this.DEFAULT_RED_TO_YELLOW);
  }

  setWhiteToGray(color?: string): void {
    this.whiteToGray.set(color ?? this.DEFAULT_WHITE_TO_GRAY);
  }

  setWhiteToBlack(color?: string): void {
    this.whiteToBlack.set(color ?? this.DEFAULT_WHITE_TO_BLACK);
  }

  setWhiteToTurquoise(color?: string): void {
    this.whiteToTurquoise.set(color ?? this.DEFAULT_WHITE_TO_TURQUOISE);
  }

  setWhiteToDarkYellow(color?: string): void {
    this.whiteToDarkYellow.set(color ?? this.DEFAULT_WHITE_TO_DARK_YELLOW);
  }

  setGrayToLightGray(color?: string): void {
    this.grayToLightGray.set(color ?? this.DEFAULT_GRAY_TO_LIGHTGRAY);
  }

  setAllPropertiesDefault(): void {
    this.isCsupasport.set(false);
    this.isHatsofuves.set(false);
    this.redToYellow.set(this.DEFAULT_RED_TO_YELLOW);
    this.whiteToBlack.set(this.DEFAULT_WHITE_TO_BLACK);
    this.whiteToDarkYellow.set(this.DEFAULT_WHITE_TO_DARK_YELLOW);
    this.grayToLightGray.set(this.DEFAULT_GRAY_TO_LIGHTGRAY);
    this.whiteToTurquoise.set(this.DEFAULT_WHITE_TO_TURQUOISE);
    this.whiteToGray.set(this.DEFAULT_WHITE_TO_GRAY);
  }
}
