import { inject, Injectable, signal, WritableSignal } from '@angular/core';
import { UtilService } from '@trendency/kesma-core';

@Injectable({
  providedIn: 'root',
})
export class IosAppService {
  readonly utilsService: UtilService = inject(UtilService);
  readonly isIosApp: WritableSignal<boolean> = signal(false);

  runIosAppDetector(): void {
    this.isIosApp.set(this.#detectIfIosApp());
  }

  #detectIfIosApp(): boolean {
    if (!this.utilsService.isBrowser()) {
      return false;
    }

    const navigator = window.navigator;

    const standalone = (navigator as any).standalone;
    const userAgent = navigator.userAgent.toLowerCase();
    const safari = /safari/.test(userAgent);
    const ios = /iphone|ipod|ipad/.test(userAgent);

    return ios ? !standalone && !safari : /\bwv\b/.test(userAgent);
  }
}
