import { ReqService, UtilService } from '@trendency/kesma-core';
import { environment } from '../../../environments/environment';
import { Injectable } from '@angular/core';
import { EnvironmentApiUrl } from '@trendency/kesma-core/lib/definitions/environment.definitions';
import { Observable } from 'rxjs';
import { ApiResponseMetaList, ApiResult, BackendComment, FollowedColumn, ReactionsData } from '@trendency/kesma-ui';
import { BackendUserResponse, Judgement, RegistrationFormData } from '../definitions';
import { ProfileEditFormData } from '../../feature/profile/profile.definitions';
import { profileEditFormDataToBackendRequest } from '../../feature/profile/profile.utils';
import { CommentType } from '../../feature/article-page/api/article-comments.definitions';
import { registrationFormDataToBackendRequest } from '../utils';

@Injectable({
  providedIn: 'root',
})
export class SecureApiService {
  constructor(
    private readonly reqService: ReqService,
    private readonly utilService: UtilService
  ) {}

  get secureApiUrl(): string {
    if (typeof environment.secureApiUrl === 'string') {
      return environment.secureApiUrl;
    }

    const { clientApiUrl, serverApiUrl } = environment.secureApiUrl as EnvironmentApiUrl;
    return this.utilService.isBrowser() ? clientApiUrl : serverApiUrl;
  }

  getCurrentUser(): Observable<BackendUserResponse> {
    return this.reqService.get(`${this.secureApiUrl}/portal-user`);
  }

  editCurrentUser(formData: ProfileEditFormData): Observable<void> {
    return this.reqService.post(`${this.secureApiUrl}/portal-user/save`, profileEditFormDataToBackendRequest(formData));
  }

  finishRegister(formData: RegistrationFormData): Observable<void> {
    return this.reqService.post(`${this.secureApiUrl}/register-finish`, registrationFormDataToBackendRequest(formData));
  }

  logout(): Observable<void> {
    return this.reqService.get(`${this.secureApiUrl}/logout`);
  }

  deleteAccount(passwordOld: string): Observable<void> {
    return this.reqService.post(`${this.secureApiUrl}/portal-user/delete-account`, { password: passwordOld });
  }

  submitCommentFor(id: string, type: CommentType = 'article', text: string, isUpdate = false): Observable<ApiResult<never>> {
    if (isUpdate) {
      return this.reqService.patch<ApiResult<never>>(`${this.secureApiUrl}/comments/${type}/${id}/update`, { text });
    }
    return this.reqService.post<ApiResult<never>>(`${this.secureApiUrl}/comments/${type}/${id}/create`, { text });
  }

  getCommentsFor(
    id: string,
    params: Record<string, unknown>,
    userId: string,
    type: CommentType = 'article'
  ): Observable<ApiResult<BackendComment[], ApiResponseMetaList>> {
    const urlSuffix = type === 'article' ? 'comments' : 'answers';
    return this.reqService.get<ApiResult<BackendComment[], ApiResponseMetaList>>(
      `${this.secureApiUrl}/comments/portal-user/${userId}/${type}/${id}/${urlSuffix}`,
      { params }
    );
  }

  getMyVotesFor(id: string, params: object, userId: string, type: CommentType = 'article'): Observable<ApiResult<ReactionsData, ApiResponseMetaList>> {
    return this.reqService.get<ApiResult<ReactionsData, ApiResponseMetaList>>(`${this.secureApiUrl}/comments/portal-user/${userId}/${type}/${id}/my-votes`, {
      params,
    });
  }

  voteComment(id: string, vote: 'like' | 'dislike' | 'clear-like-dislike' | 'report'): Observable<ApiResult<never>> {
    return this.reqService.post<ApiResult<never>>(`${this.secureApiUrl}/comments/comment/${id}/${vote}`, {});
  }

  getFollowedColumns(): Observable<ApiResult<FollowedColumn[], ApiResponseMetaList>> {
    return this.reqService.get(`${this.secureApiUrl}/followed-columns/list`);
  }

  setFollowedColumn(columnId: string): Observable<void> {
    return this.reqService.post(`${this.secureApiUrl}/followed-columns/save/${columnId}`, null);
  }

  deleteFollowedColumn(columnId: string): Observable<void> {
    return this.reqService.delete(`${this.secureApiUrl}/followed-columns/delete/${columnId}`);
  }

  getJudgement(articleId: string): Observable<ApiResult<Judgement>> {
    return this.reqService.get<ApiResult<never>>(`${this.secureApiUrl}/articles/article/${articleId}/has-judgement`);
  }

  postReaction(articleId: string): Observable<ApiResult<never>> {
    return this.reqService.post<ApiResult<never>>(`${this.secureApiUrl}/articles/article/${articleId}/like`, {});
  }
}
