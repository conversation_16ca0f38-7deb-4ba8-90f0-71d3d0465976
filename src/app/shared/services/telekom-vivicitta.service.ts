import { Injectable } from '@angular/core';
import { environment } from '../../../environments/environment';
import { EnvironmentApiUrl, UtilService } from '@trendency/kesma-core';
import { Observable } from 'rxjs';
import { HttpHeaders } from '@angular/common/http';
import { CleanHttpService } from './clean-http.service';
import { TelekomVivicittaArticle, TelekomVivicittaListRequest, TelekomVivicittaListResponse } from '../definitions';

@Injectable({
  providedIn: 'root',
})
export class TelekomVivicittaService {
  constructor(
    protected readonly httpService: CleanHttpService,
    private readonly utilsService: UtilService
  ) {}

  get vivicittaApiUrl(): string {
    if (typeof environment.vivicittaApi === 'string') {
      return environment.vivicittaApi as string;
    }

    const { clientApiUrl, serverApiUrl } = environment.vivicittaApi as EnvironmentApiUrl;
    return this.utilsService.isBrowser() ? clientApiUrl : serverApiUrl;
  }

  get vivicittaApiHeaders(): HttpHeaders | undefined {
    if (!environment.vivicittaApiToken) {
      return undefined;
    }

    return new HttpHeaders({
      Authorization: 'Basic ' + environment.vivicittaApiToken,
      'Access-Control-Allow-Origin': '*',
    });
  }

  getArticleList(request?: TelekomVivicittaListRequest): Observable<TelekomVivicittaListResponse<TelekomVivicittaArticle>> {
    let params: { [param: string]: string | number } = {
      page: request?.page ?? 1,
      limit: request?.limit ?? 20,
    };

    params = request?.q ? { ...params, q: request.q } : params;

    return this.httpService.get<TelekomVivicittaListResponse<TelekomVivicittaArticle>>(`${this.vivicittaApiUrl}/articles`, {
      headers: this.vivicittaApiHeaders,
      params,
    });
  }

  getArticle(slug: string): Observable<TelekomVivicittaArticle> {
    return this.httpService.get<TelekomVivicittaArticle>(`${this.vivicittaApiUrl}/article-nso/${slug}`, { headers: this.vivicittaApiHeaders });
  }
}
