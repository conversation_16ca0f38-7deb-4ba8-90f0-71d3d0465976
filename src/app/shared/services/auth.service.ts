import { Inject, Injectable } from '@angular/core';
import { BehaviorSubject, Observable, of, throwError } from 'rxjs';
import { catchError, map, switchMap } from 'rxjs/operators';
import { StorageService, UtilService } from '@trendency/kesma-core';
import { ApiService } from './api.service';
import { SecureApiService } from './secure-api.service';
import { environment } from '../../../environments/environment';
import { DOCUMENT } from '@angular/common';
import { User } from '@trendency/kesma-ui';

import { BackendSocialLoginResponse, BackendUserLoginResponse, BackendUserResponse, LoginFormData, SocialProvider } from '../definitions';

@Injectable({
  providedIn: 'root',
})
export class AuthService {
  readonly currentUserSubject: BehaviorSubject<User | undefined> = new BehaviorSubject<User | undefined>(undefined);

  readonly SOCIAL_REDIRECT_URL: string = encodeURI(environment.siteUrl + '/regisztracio/veglegesites');

  constructor(
    private readonly apiService: ApiService,
    private readonly secureApiService: SecureApiService,
    private readonly storageService: StorageService,
    @Inject(DOCUMENT) private readonly document: Document,
    private readonly utilsService: UtilService
  ) {}

  get currentUser(): User | undefined {
    return this.currentUserSubject.value;
  }

  authenticate(formData: LoginFormData, recaptchaToken: string): Observable<boolean> {
    return this.apiService.login(formData, recaptchaToken).pipe(
      switchMap((response: BackendUserLoginResponse) => {
        this.setToken(response.token);
        return this.isAuthenticated();
      }),
      catchError((error) => {
        this.clearTokens();
        return throwError(error);
      })
    );
  }

  invalidate(): Observable<boolean> {
    return this.secureApiService.logout().pipe(
      map(() => {
        this.clearTokens();
        return true;
      }),
      catchError(() => {
        this.clearTokens();
        return of(true);
      })
    );
  }

  isAuthenticated(): Observable<boolean> {
    if (this.currentUser) {
      return of(true);
    } else {
      const token = this.getToken();
      if (token) {
        return this.secureApiService.getCurrentUser().pipe(
          map((user: BackendUserResponse) => {
            this.currentUserSubject.next(this.mapBackendUserResponseToUser(user));
            return true;
          }),
          catchError(() => {
            this.clearTokens();
            return of(false);
          })
        );
      } else {
        this.clearTokens();
        return of(false);
      }
    }
  }

  getToken(): string | undefined {
    if (this.utilsService.isBrowser()) {
      return this.storageService.getCookie('token', this.document.cookie);
    }

    return undefined;
  }

  setToken(token: string): void {
    this.storageService.setCookie('token', token);
  }

  clearTokens(): void {
    this.storageService.setCookie('token', null, -1);
    this.currentUserSubject.next(undefined);
  }

  getSocialProviderAuthUrl(provider: SocialProvider): string {
    let providerUrl = '';
    let clientId = '';
    const redirectUrl = this.SOCIAL_REDIRECT_URL;

    switch (provider) {
      case SocialProvider.FACEBOOK:
        providerUrl = `https://www.facebook.com/v16.0/dialog/oauth`;
        clientId = environment.facebookAppId ?? '';
        break;
      case SocialProvider.GOOGLE:
        providerUrl = `https://accounts.google.com/o/oauth2/v2/auth`;
        clientId = environment.googleClientId ?? '';
        break;
    }

    return `${providerUrl}` + `?client_id=${clientId}` + `&redirect_uri=${redirectUrl}` + `&response_type=code` + `&scope=email` + `&state=${provider}`;
  }

  authenticateWithSocialProvider(provider: SocialProvider, code: string): Observable<boolean> {
    let providerAuthRequest$: Observable<BackendSocialLoginResponse>;

    switch (provider) {
      case SocialProvider.FACEBOOK:
        providerAuthRequest$ = this.apiService.loginWithFacebook(code, this.SOCIAL_REDIRECT_URL);
        break;
      case SocialProvider.GOOGLE:
        providerAuthRequest$ = this.apiService.loginWithGoogle(code, this.SOCIAL_REDIRECT_URL);
        break;
    }

    return providerAuthRequest$.pipe(
      switchMap((response: BackendSocialLoginResponse) => {
        this.setToken(response.token);
        return response.registrationFinished ? this.isAuthenticated() : of(false);
      }),
      catchError((error) => {
        this.clearTokens();
        return throwError(error);
      })
    );
  }

  mapBackendUserResponseToUser(backendUser: BackendUserResponse): User {
    return {
      uid: backendUser.id,
      email: backendUser.email,
      lastName: backendUser.lastName,
      firstName: backendUser.firstName,
      username: backendUser.userName,
      newsletter: backendUser.newsletter,
      passwordLastSave: backendUser.passwordLastSave,
    };
  }
}
