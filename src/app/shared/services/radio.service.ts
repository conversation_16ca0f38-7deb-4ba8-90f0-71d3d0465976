import { Injectable } from '@angular/core';
import { RadioStoreService } from '@trendency/kesma-ui';
import { Observable, take } from 'rxjs';

const SPORT_RADIO = 'sport';
export const DEFAULT_VOLUME = 0.5;

@Injectable({
  providedIn: 'root',
})
export class RadioService {
  isPlaying$: Observable<boolean>;

  constructor(private readonly radioStore: RadioStoreService) {}

  radioClick(): void {
    if (this.radioStore.players[SPORT_RADIO]) {
      this.radioStore.playersState[SPORT_RADIO].pipe(take(1)).subscribe((res) => (res ? this.stopSport() : this.playSport()));
    } else {
      this.radioStore.createPlayer(SPORT_RADIO);
      this.isPlaying$ = this.radioStore.playersState[SPORT_RADIO];
      this.playSport();
    }
  }

  volumeChange(volume: number): void {
    this.radioStore.setPlayerVolume(SPORT_RADIO, volume);
  }

  private playSport(): void {
    this.radioStore.playPlayer(SPORT_RADIO, 'https://icast.connectmedia.hu/4657/mr11.mp3');
  }

  private stopSport(): void {
    this.radioStore.pausePlayer(SPORT_RADIO);
  }
}
