import { Injectable } from '@angular/core';
import { PortalConfigSetting } from '@trendency/kesma-ui';
import { PortalConfigService } from './portal-config.service';

@Injectable({
  providedIn: 'root',
})
export class EbService {
  /**
   * Nincs enetpulse! Égetni kell a slugot, backenddel egyeztetve.
   * @private
   */
  private readonly slug = 'europa-bajnoksag-1';

  constructor(private readonly portalConfigService: PortalConfigService) {}

  getSlug(): string {
    return this.slug;
  }

  isEnableFootballEbElements(): boolean {
    return this.portalConfigService.isConfigSet(PortalConfigSetting.ENABLE_FOOTBALL_EB_ELEMENTS);
  }
}
