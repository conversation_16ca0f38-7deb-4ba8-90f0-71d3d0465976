import { ChangeDetectorRef, Directive, HostBinding } from '@angular/core';

@Directive({
  selector: '[nsoNsoTextInput]',
  host: {
    '[class]': '"nso-input"',
  },
})
export class NsoTextInputDirective {
  @HostBinding('class.nso-input') nsoInputClass = true;

  constructor(private readonly changeRef: ChangeDetectorRef) {}

  removeInputClass(): void {
    this.nsoInputClass = false;
    this.changeRef.detectChanges();
  }
}
