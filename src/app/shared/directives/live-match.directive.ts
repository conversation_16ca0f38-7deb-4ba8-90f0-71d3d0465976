import { AfterViewInit, Directive, ElementRef, Input, OnD<PERSON>roy, Renderer2 } from '@angular/core';
import { ResultsPageMatch } from '../definitions';
import { UtilService } from '@trendency/kesma-core';

@Directive({
  selector: '[nsoLiveMatchDirective]',
})
export class LiveMatchDirective implements AfterViewInit, OnDestroy {
  @Input('nsoLiveMatchDirective') data?: ResultsPageMatch[];

  constructor(
    private readonly elementRef: ElementRef,
    private readonly utilsService: UtilService,
    private readonly renderer2: Renderer2
  ) {}

  ngAfterViewInit(): void {
    if (this.utilsService.isBrowser() && this.hasLiveMatch()) {
      const children = this.elementRef.nativeElement.querySelector('tbody')?.children as HTMLCollection[];
      Array.from(children)?.forEach((collection, index) => {
        if (this.data?.[index]?.isLive) {
          this.renderer2.addClass(collection, 'live-border');
        }
        if (!this.data?.[index + 1]?.isLive) {
          this.renderer2.addClass(collection, 'last');
        }
      });
    }
  }

  hasLiveMatch(): boolean {
    const isLive = this.data?.some((match) => match?.isLive) || false;
    if (!isLive) {
      const thead: HTMLElement = this.elementRef.nativeElement.getElementsByTagName('thead')[0];
      thead.classList.add('hidden');
    }
    return isLive;
  }

  ngOnDestroy(): void {
    this.renderer2.destroy();
  }
}
