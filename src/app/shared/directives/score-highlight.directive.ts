import { Directive, HostBinding, Input, OnChanges } from '@angular/core';
import { ScoreHighLightDirectiveData } from '../definitions';
import { TeamDetails } from '../definitions';

@Directive({
  selector: '[nsoScoreHighlight]',
})
export class ScoreHighlightDirective implements OnChanges {
  @HostBinding('class')
  class = '';
  elementClass = this.class;
  teamA?: TeamDetails;
  teamB?: TeamDetails;

  @Input('nsoScoreHighlight') teamStat?: ScoreHighLightDirectiveData;

  ngOnChanges(): void {
    this.teamA = this.teamStat?.data?.teamA;
    this.teamB = this.teamStat?.data?.teamB;
    this.setHighLightedScore();
  }

  setHighLightedScore(): void {
    if (this.teamA?.score !== undefined && this.teamB?.score !== undefined) {
      if (this.teamA?.score > this.teamB?.score && this.teamA?.teamName === this.teamStat?.teamName && this.teamA?.score === this.teamStat?.score) {
        this.class = 'score-win';
      } else if (this.teamB?.score > this.teamA?.score && this.teamB?.teamName === this.teamStat?.teamName && this.teamB?.score === this.teamStat?.score) {
        this.class = 'score-win';
      } else if (this.teamB?.score > this.teamA?.score && this.teamB?.teamName !== this.teamStat?.teamName && this.teamB?.score === this.teamStat?.score) {
        this.class = 'score-loose';
      } else if (this.teamA?.score > this.teamB?.score && this.teamA?.teamName !== this.teamStat?.teamName && this.teamA?.score === this.teamStat?.score) {
        this.class = 'score-loose';
      }
    }
  }
}
