import {
  Article,
  ArticleBody,
  ArticleBodyType,
  BackendArticle,
  backendDateToDate,
  backendVotingDataToVotingData,
  ExternalBrandingBoxArticle,
  getPrimaryColumnColorComboByColumnTitleColor,
  PersonalizedRecommendationArticle,
} from '@trendency/kesma-ui';

export const backendArticlesToArticles = (article: BackendArticle): Article => {
  const { lastUpdated, publishDate: pubDate, dossier } = article;
  let publishDate: Date | undefined;
  let year: number = 0;
  let month: number = 0;
  if (pubDate) {
    publishDate = backendDateToDate(typeof pubDate === 'string' ? pubDate : pubDate.date) ?? undefined;
    year = publishDate?.getUTCFullYear() ?? 0;
    month = publishDate?.getUTCMonth() ?? 0;
  }

  const last: Date | undefined = lastUpdated ? (backendDateToDate(lastUpdated) ?? undefined) : undefined;
  const body: ArticleBody[] = article.body.map((element: ArticleBody) => {
    if (element.type === ArticleBodyType.Voting) {
      const votingValue = backendVotingDataToVotingData(element.details[0]?.value);
      const detail = { ...element.details[0], value: votingValue };
      return { ...element, details: [detail] };
    }
    return element;
  });
  return {
    ...article,
    publicAuthor: article?.publicAuthor ?? 'Nemzeti Sport',
    dossier: dossier?.[0],
    lastUpdated: last,
    publishDate,
    year,
    month,
    preTitle: article?.preTitle,
    tag: article?.tags?.[0],
    columnSlug: article.primaryColumn?.slug,
    columnTitle: article?.primaryColumn?.title,
    primaryColumnColorCombo: article?.primaryColumn?.titleColor ? getPrimaryColumnColorComboByColumnTitleColor(article?.primaryColumn?.titleColor) : undefined,
    body,
  };
};

export const mapTrafficDeflectorArticlesToBrandingBoxArticle = (
  personalizedRecommendationArticle: PersonalizedRecommendationArticle
): ExternalBrandingBoxArticle => ({
  title: personalizedRecommendationArticle?.title,
  lead: personalizedRecommendationArticle?.head,
  imageUrl: personalizedRecommendationArticle?.image,
  url: personalizedRecommendationArticle?.url,
});
