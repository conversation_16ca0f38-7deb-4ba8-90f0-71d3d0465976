import { LayoutStruct } from '@trendency/kesma-ui';

export function addHomeAds(layoutApiData: any): any {
  return {
    content: layoutApiData.content,
    struct: [
      ...((layoutApiData?.struct ?? []) as LayoutStruct[]).map((structItem: LayoutStruct, i: number): LayoutStruct[] => {
        if (i === 1) {
          return {
            ...structItem,
            elements: structItem?.elements?.map((element_a, i) => {
              if (i === 0) {
                return { ...element_a, advert_id: 'kezdooldal_tartalom' };
              }
              if (i === 1) {
                return { ...element_a, elements: modifyNestedStructElementArray(element_a.elements, 'home') };
              }
              return element_a;
            }),
          } as any;
        }
        return structItem as any;
      }),
    ],
  };
}

export function addCategoryAds(layoutApiData: any): any {
  return {
    content: layoutApiData?.content,
    struct: [
      ...((layoutApiData?.struct ?? []) as LayoutStruct[]).map((structItem: LayoutStruct, i: number): LayoutStruct[] => {
        if (i === 0) {
          return {
            ...structItem,
            elements: structItem?.elements?.map((element_a, i) => {
              if (i === 0) {
                return { ...element_a, advert_id: 'rovat_tartalom' };
              }
              if (i === 1) {
                return { ...element_a, elements: modifyNestedStructElementArray(element_a.elements, 'category') };
              }
              return element_a;
            }),
          } as any;
        }
        return structItem as any;
      }),
    ],
  };
}

export function addSidebarAds(layoutApiData: any): any {
  return {
    content: layoutApiData.content,
    struct: [
      {
        ...(layoutApiData?.struct ?? [])[0],
        elements: [
          {
            ...layoutApiData?.struct[0]?.elements[0],
            elements: modifyNestedStructElementArray(layoutApiData?.struct[0]?.elements[0]?.elements),
          },
        ],
      } as any,
    ],
  };
}

function advertStructElement(name: string): any[] {
  return [
    {
      configurable: false,
      id: name,
      type: 'content',
      contentLength: 1,
      previewImage: '/assets/images/layout-frames/vg/ad-desktop-medium_rectangle_1_top.png',
      contentType: 'google-ad',
      hideMobile: false,
      medium: 'desktop',
      bannerName: 'medium_rectangle_1_top',
      withBlockTitle: false,
      mobileOrder: null,
      columnCount: 12,
      iterator: [0],
    },
  ];
}

export function modifyNestedStructElementArray(originalArray: any[], pageType?: 'category' | 'home'): any[] {
  const arrayLength = originalArray.length;

  const divider = 4;

  if (!arrayLength) {
    return originalArray;
  }

  if (pageType === 'category') {
    const flattenedArray = findItemsByType(originalArray, 'content');

    const sliceSize = Math.floor(flattenedArray.length / divider);

    const firstSlice = flattenedArray.slice(0, sliceSize);

    const secondSlice = flattenedArray.slice(sliceSize, 2 * sliceSize);
    const thirdSlice = flattenedArray.slice(2 * sliceSize, 3 * sliceSize);
    const fourthSlice = flattenedArray.slice(3 * sliceSize);

    return advertStructElement('jobbsav_1').concat(
      firstSlice,
      secondSlice,
      advertStructElement('jobbsav_2'),
      thirdSlice,
      fourthSlice,
      advertStructElement('jobbsav_4')
    );
  }

  const sliceSize = Math.floor(arrayLength / divider);

  const firstSlice = originalArray.slice(0, sliceSize);
  const secondSlice = originalArray.slice(sliceSize, 2 * sliceSize);
  const thirdSlice = originalArray.slice(2 * sliceSize, 3 * sliceSize);
  const fourthSlice = originalArray.slice(3 * sliceSize);

  if (pageType === 'home') {
    return advertStructElement('jobbsav_1').concat(
      firstSlice,
      secondSlice,
      advertStructElement('jobbsav_2'),
      thirdSlice,
      advertStructElement('jobbsav_3'),
      fourthSlice,
      advertStructElement('jobbsav_4')
    );
  }

  // Combine the modified slices for sidebar
  return firstSlice.concat(advertStructElement('jobbsav_2'), secondSlice, thirdSlice, fourthSlice, advertStructElement('jobbsav_4'));
}
const findItemsByType = (array: any[], targetType: string, resultArray: any[] = []): any[] => {
  for (const element of array) {
    if (element.type === targetType) {
      resultArray.push(element);
    }

    if (element.elements && Array.isArray(element.elements)) {
      findItemsByType(element.elements, targetType, resultArray);
    }
  }

  return resultArray;
};
