/**
 * This key is used to remove elements from the DOM with this attribute.
 * Warning: This is only executed when the app is initialized. Afterwards it will have no effect.
 * The attribute will be also removed from the element, regardless that it is removed or not.
 * Example usage: <script type="text/javascript" data-skip-conditionally>.....</script>
 */
export const SCRIPT_CONDITIONAL_REMOVE_DATA_KEY = 'data-skip-conditionally';
