import { ArticleSchema } from '@trendency/kesma-core';
import { Article } from '@trendency/kesma-ui';

export const getStructuredDataForArticle = (article: Article, currentUrl: string, siteUrl: string, params?: Record<string, any>): ArticleSchema =>
  article && {
    '@type': 'NewsArticle',
    headline: article.title,
    alternativeHeadline: article.alternativeTitle || article.lead || article.title,
    image: article.thumbnail,
    url: currentUrl,
    mainEntityOfPage: currentUrl,
    description: article.excerpt,
    dateModified: article.lastUpdated || article.publishDate,
    author: {
      '@type': 'Person',
      name: article.publicAuthor,
      jobTitle: 'Journalist',
      ...(params?.['hasAuthorPage'] && { url: `${siteUrl}/szerzo/${article.publicAuthor}` }),
    },
    datePublished: article.publishDate,
    publisher: {
      '@type': 'Organization',
      name: 'N.S. MÉDIA ÉS VAGYONKEZELŐ Kft.',
      logo: {
        '@type': 'ImageObject',
        height: '100',
        width: '100',
        url: `${siteUrl}/assets/images/ns-avatar.png`,
      },
    },
  };
