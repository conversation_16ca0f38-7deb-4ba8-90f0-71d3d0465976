import { BackendUserLoginRequest, BackendUserRegisterRequest, LoginFormData, RegistrationFormData } from '../definitions';

export function loginFormDataToBackendRequest(formData: LoginFormData, recaptchaToken: string): BackendUserLoginRequest {
  return {
    emailOrUserName: formData.email,
    password: formData.password,
    recaptcha: recaptchaToken,
  };
}

export function registrationFormDataToBackendRequest(formData: RegistrationFormData, recaptchaToken?: string): BackendUserRegisterRequest {
  return {
    userName: formData.username,
    email: formData.email,
    plainPassword: formData.password,
    newsletter: formData.newsletter,
    acceptTerms: formData.terms,
    recaptcha: recaptchaToken,
  };
}
