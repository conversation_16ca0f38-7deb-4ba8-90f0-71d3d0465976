import { ArticleCard, ArticleSearchResult, BackendArticleSearchResult, FakeBool, VideoCard } from '@trendency/kesma-ui';

function getBoolean(value?: boolean | FakeBool): boolean | undefined {
  return typeof value === 'string' ? value !== '0' : value;
}

export function backendArticlesSearchResultsToArticleSearchResArticles(article: BackendArticleSearchResult): ArticleSearchResult {
  const [year, month] = article.publishDate.split('-');
  return {
    ...article,
    length: parseInt(article.length, 10),
    year,
    month,
    columnTitleColor: '',
    contentType: article.contentType?.toString() || '',
    hideThumbnailFromBody: !!article.hideThumbnailFromBody,
  };
}

export const searchResultToArticleCard = ({
  id,
  title,
  slug,
  columnTitle,
  columnSlug,
  publishDate,
  tag,
  lead,
  thumbnail: thumbnailUrl,
  author: authorName,
  year: publishYear,
  month: publishMonth,
  contentType,
  preTitle,
  tags,
  regions,
  isVideo,
  thumbnailFocusedImages,
}: ArticleSearchResult): ArticleCard => ({
  id,
  title,
  preTitle,
  slug,
  category: {
    name: columnTitle,
    slug: columnSlug,
  },
  publishDate,
  publishYear,
  publishMonth,
  lead,
  thumbnail: {
    url: thumbnailUrl ?? '',
  },
  author: {
    name: authorName,
  },
  tags,
  regions,
  contentType,
  columnSlug,
  columnTitle,
  tag,
  isVideoType: getBoolean(isVideo),
  thumbnailFocusedImages,
});

export function searchResultToVideoCard({
  columnSlug,
  columnTitle,
  columnTitleColor,
  publishDate,
  title,
  length,
  thumbnail,
  slug,
  thumbnailFocusedImages,
}: ArticleSearchResult): VideoCard {
  return {
    title,
    length,
    thumbnail: { url: thumbnail as string, alt: '' },
    slug,
    category: { slug: columnSlug, name: columnTitle, color: columnTitleColor },
    publishDate: publishDate,
    thumbnailFocusedImages,
  };
}
