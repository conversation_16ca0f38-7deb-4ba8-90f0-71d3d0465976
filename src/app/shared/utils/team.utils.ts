import { buildMatchUrl } from '@trendency/kesma-ui';
import { MatchList, MatchResult, Schedule, Team, TeamPlayer } from '../../feature/team-page/team-end-page.definitions';
import { TeamLineUpsDefinition } from '../definitions';

export function mapToMatches(schedules: Schedule[], hasTitle: boolean = true, team?: Team): MatchList {
  const matches = schedules.map((schedule) => ({
    teamA: {
      teamName: schedule.homeTeam.title,
      score: +schedule.homeScore,
      teamLogo: schedule.homeTeam.logo || schedule?.homeTeam?.team?.logo,
    },
    teamB: {
      teamName: schedule.awayTeam.title,
      score: +schedule.awayScore,
      teamLogo: schedule.awayTeam.logo || schedule?.awayTeam?.team?.logo,
    },
    result: team ? getMatchResultForATeam(team, schedule) : getMatchResult(+schedule.homeScore, +schedule.awayScore),
    championshipName: schedule.competition.title,
    link: buildMatchUrl(schedule),
  }));

  return {
    teamName: team ? team.name : '',
    hasTitle: hasTitle,
    matches,
  };
}

export function getMatchResult(homeScore: number, awayScore: number): MatchResult {
  if (homeScore === awayScore) {
    return MatchResult.DRAW;
  }
  return homeScore > awayScore ? MatchResult.VICTORY : MatchResult.DEFEAT;
}

export function getMatchResultForATeam(team: Team, schedule: Schedule): MatchResult {
  if (schedule.homeTeam.team.id === team.id) {
    return getMatchResult(+schedule.homeScore, +schedule.awayScore);
  }
  return getMatchResult(+schedule.awayScore, +schedule.homeScore);
}

export function mapToPlayers(teamPlayers: TeamPlayer[]): TeamLineUpsDefinition[] {
  const teamLineUps: TeamLineUpsDefinition[] = [];

  if (!teamPlayers) {
    return [];
  }
  teamPlayers.map((player) => {
    const isIncludes = teamLineUps.find((p) => p?.positionName === player?.playerPosition);
    if (!isIncludes) {
      teamLineUps.push({
        positionName: player?.playerPosition,
        players: teamPlayers.filter((teamPlayer) => teamPlayer?.playerPosition === player?.playerPosition).map((p) => `${p?.lastName} ${p?.firstName}`),
      });
    }
  });

  return teamLineUps;
}
