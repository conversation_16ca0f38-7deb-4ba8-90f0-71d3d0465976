import { ArticleCard } from '@trendency/kesma-ui';
import { TelekomVivicittaArticle } from '../definitions';

export const telekomVivicittaTitle = 'Telekom Vivicittá';
export const telekomVivicittaUrl = 'https://vivicitta2025.nemzetisport.hu';
export const telekomVivicittaPrimaryColor = '#e20074';
export const telekomVivicittaSecondaryColor = '#eff2f4';

export function mapVivicittaArticleToArticleCard(data: TelekomVivicittaArticle): ArticleCard {
  return {
    title: data.title,
    slug: data.slug,
    lead: data.lead,
    publishDate: data.Publishdate ? new Date(`${data.Publishdate.replace(' ', 'T')}`) : undefined,
    category: {
      name: telekomVivicittaTitle,
    },
    customLink: {
      url: data.url,
    },
    primaryColumnColorCombo: {
      color: telekomVivicittaPrimaryColor,
      background: telekomVivicittaSecondaryColor,
    },
    thumbnail: {
      url: (data.leadImage?.thumbnail?.length ?? 0) > 0 ? data.leadImage?.thumbnail : '/assets/images/nemzetisport.png',
      alt: data.leadImage?.alt,
    },
  };
}
