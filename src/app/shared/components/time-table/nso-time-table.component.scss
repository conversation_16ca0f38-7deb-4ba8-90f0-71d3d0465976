@use 'shared' as *;

::ng-deep {
  .time-table {
    width: 100%;
    font-size: 16px;
    font-family: var(--kui-font-condensed);

    tr {
      a {
        display: block;
        color: var(--kui-black);
        padding: 24px;
      }
      border: 1px solid var(--kui-gray-200);
    }

    tbody tr:hover,
    tbody tr:focus-within {
      background: var(--kui-gray-50);
      outline: none;
    }

    td {
      &.mobile-last {
        @include media-breakpoint-down(md) {
          padding-right: 26px;
        }

        @media (max-width: 322px) {
          padding-right: 10px;
        }
      }

      &.not-on-mobile {
        @include media-breakpoint-down(md) {
          display: none;
        }
      }

      &.first a {
        padding-left: 26px;

        @media (max-width: 322px) {
          padding-left: 10px;
        }
      }

      &.last a {
        padding-right: 26px;
      }
    }
  }

  .time-cell {
    font-family: var(--kui-font-condensed);
    color: var(--kui-gray-550);
    font-size: 24px;
    line-height: 28px;
    font-weight: 700;
    text-transform: uppercase;
    @media (max-width: 322px) {
      padding-left: 5px;
      padding-right: 5px;
    }
  }

  .main-time-header {
    background: var(--kui-gray-100);
    padding: 24px;
    font-family: var(--kui-font-condensed);
    color: var(--kui-gray-550);
    font-size: 24px;
    line-height: 28px;
    font-weight: 700;
    text-transform: uppercase;
    border: 1px solid var(--kui-gray-200);

    @include media-breakpoint-down(md) {
      font-size: 22px;
      line-height: 130%;
    }
  }

  .left-padding {
    @include media-breakpoint-between(md, xl) {
      display: inline-block;
      padding-left: 10px;
    }
  }

  .bold-cell {
    font-weight: 700;
  }

  .table-logo {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background-position: center;
    background-size: cover;
    border: 1px solid var(--kui-gray-200);
    margin: 0 16px;

    @include media-breakpoint-down(md) {
      height: 32px;
      width: 32px;
    }

    @media (max-width: 322px) {
      margin-left: 10px;
      margin-right: 10px;
    }
  }

  .table-separator {
    width: 1px;
    height: 28px;
    background-color: var(--kui-gray-200);
    margin: 0 20px;
    display: inline-block;
  }

  .team-name {
    @include media-breakpoint-down(md) {
      display: none;
    }
  }

  .team-name-mobile {
    display: none;

    @include media-breakpoint-down(md) {
      display: block;
    }
  }
}
