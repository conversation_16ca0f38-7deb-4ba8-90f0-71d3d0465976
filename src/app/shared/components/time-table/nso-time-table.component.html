<ng-template #cellWithSeparator let-rowData="data"> <span class="table-separator"> </span> </ng-template>
<ng-template #logoTemplate1 let-rowData="data">
  <img
    class="table-logo"
    loading="lazy"
    [src]="rowData?.homeTeam?.logo || rowData?.homeTeam?.team?.logo || 'assets/images/nemzetisport.png'"
    [alt]="rowData?.homeTeam?.title || ''"
  />
</ng-template>
<ng-template #logoTemplate2 let-rowData="data">
  <img
    class="table-logo"
    loading="lazy"
    [src]="rowData?.awayTeam?.logo || rowData?.awayTeam?.team?.logo || 'assets/images/nemzetisport.png'"
    [alt]="rowData?.awayTeam?.title || ''"
  />
</ng-template>
<ng-template #matchTime let-rowData="data">
  {{ rowData?.scheduleDate?.date | dfnsFormat: 'HH:mm' }}
</ng-template>
<ng-template #tvStation let-rowData="data">
  <span>{{ rowData?.tvStation?.title ?? '-' }}</span>
</ng-template>
<ng-template #facility let-rowData="data">
  <span class="left-padding">{{ rowData?.facility?.title ?? '-' }}</span>
</ng-template>
<ng-template #teamNameHome let-rowData="data">
  <ng-container [ngTemplateOutlet]="teamName" [ngTemplateOutletContext]="{ team: rowData?.homeTeam?.title }"></ng-container>
</ng-template>
<ng-template #teamNameAway let-rowData="data">
  <ng-container [ngTemplateOutlet]="teamName" [ngTemplateOutletContext]="{ team: rowData?.awayTeam?.title }"></ng-container>
</ng-template>
<ng-template #appointmentHeader let-rowData="data">
  <span>{{ actualChampionshipDate | dfnsFormat: 'yyyy MMMM dd, EEEE' }}</span>
</ng-template>
<ng-template #teamName let-team="team">
  <span class="bold-cell team-name">{{ team ?? '-' }}</span>
  <span class="bold-cell team-name-mobile">{{ (team && team.slice(0, 3) | uppercase) ?? '-' }}</span>
</ng-template>

<kesma-data-table-generator [data]="data" [tableConfig]="tableConfig" [dataTableClass]="'time-table'"></kesma-data-table-generator>
