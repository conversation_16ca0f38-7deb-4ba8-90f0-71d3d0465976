import { ChangeDetectionStrategy, Component, AfterViewInit, TemplateRef, ViewChild, ChangeDetectorRef } from '@angular/core';
import { BaseComponent, dataTableConfig, DataTableGeneratorComponent } from '@trendency/kesma-ui';
import { ChampionshipSchedule } from '../../definitions';
import { FormatPipeModule } from 'ngx-date-fns';
import { NgTemplateOutlet, UpperCasePipe } from '@angular/common';

@Component({
  selector: 'nso-time-table',
  templateUrl: './nso-time-table.component.html',
  styleUrls: ['./nso-time-table.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [FormatPipeModule, NgTemplateOutlet, UpperCasePipe, DataTableGeneratorComponent],
})
export class NsoTimeTableComponent extends BaseComponent<ChampionshipSchedule[]> implements AfterViewInit {
  tableConfig: dataTableConfig[] = [];
  actualChampionshipDate?: Date;

  @ViewChild('logoTemplate1', {
    read: TemplateRef,
    static: false,
  })
  logoTemplate1?: TemplateRef<HTMLElement>;
  @ViewChild('logoTemplate2', {
    read: TemplateRef,
    static: false,
  })
  logoTemplate2?: TemplateRef<HTMLElement>;
  @ViewChild('teamNameHome', {
    read: TemplateRef,
    static: false,
  })
  teamNameHome?: TemplateRef<HTMLElement>;
  @ViewChild('teamNameAway', {
    read: TemplateRef,
    static: false,
  })
  teamNameAway?: TemplateRef<HTMLElement>;
  @ViewChild('cellWithSeparator', {
    read: TemplateRef,
    static: false,
  })
  cellWithSeparator?: TemplateRef<HTMLElement>;

  @ViewChild('appointmentHeader', {
    read: TemplateRef,
    static: false,
  })
  appointmentHeader?: TemplateRef<HTMLElement>;

  @ViewChild('facility', {
    read: TemplateRef,
    static: false,
  })
  facility?: TemplateRef<HTMLElement>;

  @ViewChild('tvStation', {
    read: TemplateRef,
    static: false,
  })
  tvStation?: TemplateRef<HTMLElement>;

  @ViewChild('matchTime', {
    read: TemplateRef,
    static: false,
  })
  matchTime?: TemplateRef<HTMLElement>;

  constructor(private readonly cd: ChangeDetectorRef) {
    super();
  }

  formatHeaderDate(): void {
    if (this.data?.length) {
      this.actualChampionshipDate = new Date(this.data[0]?.scheduleDate?.date);
      this.data?.map((match) => {
        match.scheduleDate.date = new Date(match?.scheduleDate?.date);
      });
    }
    this.setTableConfig();
  }

  setTableConfig(): void {
    this.tableConfig = [
      {
        hasHeader: true,
        headerColspan: 8,
        columnDataProperty: 'name1',
        headerClass: 'main-time-header',
        columnClass: 'first',
        customColumnTemplate: this.teamNameHome,
        customHeaderTemplate: this.appointmentHeader,
        rowLinkDataProperty: 'link',
      },
      {
        hasHeader: false,
        headerColspan: 1,
        columnDataProperty: 'image1',
        customColumnTemplate: this.logoTemplate1,
        rowLinkDataProperty: 'link',
      },
      {
        hasHeader: false,
        headerColspan: 1,
        columnDataProperty: 'scheduleTime',
        columnClass: 'time-cell',
        customColumnTemplate: this.matchTime,
        rowLinkDataProperty: 'link',
      },
      {
        hasHeader: false,
        headerColspan: 1,
        columnDataProperty: 'image2',
        customColumnTemplate: this.logoTemplate2,
        rowLinkDataProperty: 'link',
      },
      {
        hasHeader: false,
        headerColspan: 1,
        columnDataProperty: 'name2',
        columnClass: 'mobile-last',
        customColumnTemplate: this.teamNameAway,
        rowLinkDataProperty: 'link',
      },
      {
        hasHeader: false,
        headerColspan: 1,
        columnDataProperty: 'facility?.title',
        columnClass: 'bold-cell not-on-mobile',
        customColumnTemplate: this.facility,
        rowLinkDataProperty: 'link',
      },
      {
        hasHeader: false,
        headerColspan: 1,
        columnDataProperty: 'channel',
        columnClass: 'not-on-mobile',
        customColumnTemplate: this.cellWithSeparator,
        rowLinkDataProperty: 'link',
      },
      {
        hasHeader: false,
        headerColspan: 1,
        columnDataProperty: 'channel',
        columnClass: 'last not-on-mobile',
        customColumnTemplate: this.tvStation,
        rowLinkDataProperty: 'link',
      },
    ];
  }

  ngAfterViewInit(): void {
    this.formatHeaderDate();
    this.setTableConfig();
    this.cd.detectChanges();
  }
}
