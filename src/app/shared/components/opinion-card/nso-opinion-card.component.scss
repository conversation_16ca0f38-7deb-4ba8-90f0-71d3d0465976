@use 'shared' as *;

:host {
  display: block;
  width: 100%;

  &.style-WithBackground {
    border: 1px solid var(--kui-gray-200);
    background: var(--kui-gray-100);
    padding: 16px 24px;
    height: 100%;

    .opinion-card {
      &-column {
        font-family: var(--kui-font-condensed);
        font-size: 22px;
        line-height: 28.6px;
        font-weight: 700;
        color: var(--kui-gray-600);
        text-transform: uppercase;
        cursor: pointer;
      }

      &-divider {
        width: calc(100% + 48px);
        height: 1px;
        border-bottom: 1px solid var(--kui-gray-200);
        margin: 16px -24px;
      }

      &-quote {
        font-weight: 700;
        font-family: var(--kui-font-condensed);
        font-size: 80px;
        color: var(--kui-gray-250);
        display: block;
        height: 60px;

        &:last-of-type {
          text-align: right;
        }
      }

      &-thumbnail {
        width: 56px;
        height: 56px;
        border-radius: 50%;
        object-fit: cover;
        border: 1px solid var(--kui-gray-200);
        cursor: pointer;
      }

      &-multi-authors {
        display: flex;
        gap: 10px;
        align-items: center;
      }

      &-data {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
        gap: 8px;

        &.multi-authors {
          flex-wrap: wrap;
        }
      }

      &-author {
        font-weight: 700;
        font-size: 16px;
        line-height: 19.2px;
        color: var(--kui-red-400);
        font-family: var(--kui-font-primary);
      }

      &-title {
        font-family: var(--kui-font-condensed);
        font-weight: 700;
        font-size: 24px;
        line-height: 28.8px;
        text-transform: uppercase;
        color: var(--kui-gray-600);
        cursor: pointer;
      }

      &-lead {
        font-weight: 400;
        font-size: 16px;
        line-height: 25.6px;
        color: var(--kui-gray-550);
      }
    }
  }

  &.style-Transparent {
    .opinion-card {
      &-thumbnail,
      &-column,
      &-divider,
      &-quote {
        display: none;
      }

      &-author {
        font-weight: 700;
        font-size: 16px;
        line-height: 19.2px;
        font-family: var(--kui-font-primary);
        color: var(--kui-red-400);
      }

      &-title {
        font-family: var(--kui-font-condensed);
        font-size: 22px;
        line-height: 28.6px;
        font-weight: 700;
        color: var(--kui-gray-500);
        margin-bottom: 4px;
        cursor: pointer;
      }

      &-lead {
        font-family: var(--kui-font-primary);
        font-weight: 400;
        font-size: 16px;
        line-height: 25.6px;
        color: var(--kui-gray-500);
      }
    }
  }
}
