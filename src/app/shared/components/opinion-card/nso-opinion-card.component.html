<h2 class="opinion-card-column" [routerLink]="columnUrl">
  {{ data?.columnTitle }}
</h2>
<a [routerLink]="articleUrl">
  <div class="opinion-card-divider"></div>

  <span class="opinion-card-quote">“</span>
  <div class="opinion-card-data" [class.multi-authors]="data?.publicAuthorM2M?.length">
    <ng-container *ngIf="data?.publicAuthorM2M?.length; else notMultiAuthor">
      <ng-container *ngFor="let author of data?.publicAuthorM2M; trackBy: trackByFn">
        <div class="opinion-card-multi-authors">
          <img
            class="opinion-card-thumbnail"
            [src]="author?.avatar || 'assets/images/nemzetisport.png'"
            [alt]="author?.fullName || 'Vélemény avatár'"
            [routerLink]="articleUrl"
            loading="lazy"
          />

          <span class="opinion-card-author">{{ author?.fullName }}</span>
        </div>
      </ng-container>
    </ng-container>
    <ng-template #notMultiAuthor>
      <img
        class="opinion-card-thumbnail"
        [src]="data?.author?.avatarUrl || 'assets/images/nemzetisport.png'"
        [alt]="data?.author?.name || 'Vélemény avatár'"
        [routerLink]="articleUrl"
        loading="lazy"
      />

      <div>
        <span class="opinion-card-author">{{ data?.author?.name }}</span>
      </div>
    </ng-template>
  </div>
  <h3 class="opinion-card-title">{{ data?.title }}</h3>
  <p class="opinion-card-lead">{{ data?.lead }}</p>
  <span class="opinion-card-quote">“</span>
</a>
