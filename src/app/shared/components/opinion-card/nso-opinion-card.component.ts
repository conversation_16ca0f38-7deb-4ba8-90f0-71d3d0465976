import { ChangeDetectionStrategy, Component, HostBinding, Input } from '@angular/core';
import { ArticleCard, BaseComponent, buildArticleUrl, buildColumnUrl } from '@trendency/kesma-ui';
import { OpinionCardType } from './nso-opinion-card.definitions';
import { RouterLink } from '@angular/router';
import { NgForOf, NgIf } from '@angular/common';

@Component({
  selector: 'nso-opinion-card',
  templateUrl: './nso-opinion-card.component.html',
  styleUrls: ['./nso-opinion-card.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [RouterLink, NgIf, NgForOf],
})
export class NsoOpinionCardComponent extends BaseComponent<ArticleCard> {
  @HostBinding('class') hostClass = '';

  @Input() set styleID(styleID: OpinionCardType) {
    this.hostClass = `opinion-card style-${OpinionCardType[styleID]}`;
  }

  get articleUrl(): string[] {
    return this.data ? buildArticleUrl(this.data) : [];
  }

  get columnUrl(): string[] {
    return this.data ? buildColumnUrl(this.data) : [];
  }
}
