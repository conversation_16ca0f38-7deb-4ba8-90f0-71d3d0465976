import { ChangeDetectionStrategy, Component, inject, Input, OnInit } from '@angular/core';
import { AdultOverlayComponent, BaseComponent, GalleryData } from '@trendency/kesma-ui';
import { StorageService } from '@trendency/kesma-core';
import { RouterLink } from '@angular/router';
import { NgIf } from '@angular/common';
import { FormatPipeModule } from 'ngx-date-fns';

@Component({
  selector: 'nso-gallery-card',
  templateUrl: './nso-gallery-card.component.html',
  styleUrls: ['./nso-gallery-card.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [RouterLink, AdultOverlayComponent, NgIf, FormatPipeModule],
})
export class NsoGalleryCardComponent extends BaseComponent<GalleryData> implements OnInit {
  @Input() isInsideAdultArticleBody = false;
  @Input() showCount: boolean = true;
  @Input() showDate: boolean = true;

  isAdultChoice = false;

  private slug: string = '';

  private readonly storage = inject(StorageService);

  override ngOnInit(): void {
    if (!this.data) {
      return;
    }
    this.isAdultChoice = this.storage.getSessionStorageData('isAdultChoice') ?? false;
    this.slug = this.data.slug;
  }

  public get galleryLink(): string[] {
    return ['/', 'galeria', this.slug];
  }
}
