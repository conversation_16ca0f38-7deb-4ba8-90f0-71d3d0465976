<figure>
  <div class="gallery-wrapper" [routerLink]="galleryLink">
    @if (data?.isAdult && !isInsideAdultArticleBody && !isAdultChoice) {
      <kesma-adult-overlay>
        <img class="gallery-card-thumbnail" loading="lazy" [src]="data?.highlightedImageUrl" [alt]="data?.description" />
      </kesma-adult-overlay>
    } @else {
      <img class="gallery-card-thumbnail" loading="lazy" [src]="data?.highlightedImageUrl" [alt]="data?.description" />
    }
    <div class="gallery-indicator">
      <i class="icon icon-gallery"></i>
    </div>
  </div>
  <figcaption>
    <div class="gallery-card-data">
      <strong class="gallery-card-count" *ngIf="showCount"> {{ data?.count }} fotó </strong>
      <span class="gallery-card-date" *ngIf="showDate">
        {{ data?.publishDate | dfnsFormat: 'yyyy.MM.dd.' }}
      </span>
    </div>
    <h2 class="gallery-card-title" [routerLink]="galleryLink">{{ data?.title }}</h2>
  </figcaption>
</figure>
