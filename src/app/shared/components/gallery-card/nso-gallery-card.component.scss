@use 'shared' as *;

:host {
  display: block;
  margin-bottom: 20px;
  width: 100%;
  font-family: var(--kui-font-primary);
  color: var(--kui-gray-550);

  .gallery-card {
    &-thumbnail {
      aspect-ratio: 16 / 9;
      object-fit: cover;
      cursor: pointer;
      width: 100%;
    }

    &-data {
      margin: 16px 0 7.5px 0;
    }

    &-count {
      font-weight: 700;
      font-size: 16px;
      line-height: 19.2px;
      letter-spacing: 0.01em;

      &:after {
        content: '';
        display: inherit;
        border: 1px solid var(--kui-gray-200);
        margin: 0 10px 0 7px;
      }

      @include media-breakpoint-down(md) {
        font-size: 14px;
        line-height: 22.4px;
      }
    }

    &-date {
      font-weight: 400;
      font-size: 16px;
      line-height: 25.6px;

      @include media-breakpoint-down(md) {
        font-size: 14px;
        line-height: 22.4px;
      }
    }

    &-title {
      font-family: var(--kui-font-condensed);
      text-transform: uppercase;
      font-weight: 700;
      line-height: 28.6px;
      font-size: 22px;
      cursor: pointer;
    }
  }

  .gallery-wrapper {
    position: relative;

    .gallery-card-thumbnail {
      height: 100%;
      width: 100%;
      cursor: pointer;
      aspect-ratio: 16/9;
      object-fit: cover;
    }

    .gallery-indicator {
      position: absolute;
      top: 0;
      right: 0;
      cursor: pointer;
      background: var(--kui-red-400);
      padding: 10px;

      .icon-gallery {
        width: 25px;
        height: 23px;
      }
    }
  }
}
