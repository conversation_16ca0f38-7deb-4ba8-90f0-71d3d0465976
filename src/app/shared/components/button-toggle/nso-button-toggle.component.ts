import { ChangeDetectionStrategy, Component, EventEmitter, Input, On<PERSON><PERSON>roy, OnInit, Output, TemplateRef } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { BehaviorSubject, Subject, takeUntil } from 'rxjs';
import { AsyncPipe, NgForOf, NgIf, NgTemplateOutlet } from '@angular/common';

export type ButtonToggleValue<T> = { title?: string; templateRef?: TemplateRef<any>; value?: T };

@Component({
  selector: 'nso-button-toggle',
  templateUrl: './nso-button-toggle.component.html',
  styleUrls: ['./nso-button-toggle.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: NsoButtonToggleComponent,
      multi: true,
    },
  ],
  imports: [NgForOf, NgIf, <PERSON><PERSON><PERSON><PERSON><PERSON>, NgTemplateOutlet],
})
export class NsoButtonToggleComponent<T> implements On<PERSON><PERSON><PERSON>, On<PERSON><PERSON><PERSON>, ControlValueAccessor {
  private readonly destroy$ = new Subject<void>();
  /* Items that will be available to select */
  @Input() items: ButtonToggleValue<T>[] = [];

  /* Initial value if not using forms */
  @Input() value?: T;

  /*
  Whether the currently selected button can be 'unselected' (like a checkbox), or work like a radio button (toggle)
   */
  @Input() canCleared = false;

  @Output() selectedItemChange = new EventEmitter<ButtonToggleValue<T> | null>();
  @Output() valueChange = new EventEmitter<ButtonToggleValue<T> | T | null>();

  // ControlValueAccessor interface
  onChange?: (value: ButtonToggleValue<T> | null) => void;
  onTouched?: () => void;

  isDisabled$ = new BehaviorSubject<boolean>(false);
  value$ = new BehaviorSubject<ButtonToggleValue<T> | null>(null);

  constructor() {}

  ngOnInit(): void {
    if (this.value) {
      this.writeValue(this.value);
    }
    this.value$.pipe(takeUntil(this.destroy$)).subscribe((value: ButtonToggleValue<T> | null) => {
      // eslint-disable-next-line no-prototype-builtins
      const emittedValue = value?.hasOwnProperty('value') ? value.value : value;
      if (this.onTouched) this.onTouched();
      if (this.onChange) this.onChange(emittedValue ?? null);
      this.selectedItemChange.emit(value);
      this.valueChange.emit(emittedValue ?? null);
    });
  }
  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.isDisabled$.next(isDisabled);
  }

  writeValue(obj: T | ButtonToggleValue<T>): void {
    // Handle 2 scenarios:
    // - Process value field
    // - Check if the submitted value is the whole item
    if (this.items.includes(obj as ButtonToggleValue<T>)) {
      this.value$.next(obj as ButtonToggleValue<T>);
      return;
    }
    const item = this.items.find((it) => it?.value === (obj as T));
    if (item) {
      this.value$.next(item as ButtonToggleValue<T>);
      return;
    }
  }

  handleButtonClick(itemInstance: ButtonToggleValue<T>): void {
    //Change to the selected tab, or if clearing is enabled, try to uncheck the currently selected button.
    if (this.onTouched) this.onTouched();
    if (this.value$.value === itemInstance && this.canCleared) {
      this.value$.next(null);
      return;
    }
    if (this.value$.value !== itemInstance) {
      this.value$.next(itemInstance);
    }
  }
}
