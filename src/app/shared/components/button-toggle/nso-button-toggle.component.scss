@use 'shared' as *;

:host {
  display: block;
  button {
    color: var(--kui-gray-300);
    background-color: var(--kui-gray-100);
    font-family: var(--kui-font-condensed);
    padding: 13px 26px;
    font-size: 18px;
    font-weight: 700;
    text-transform: uppercase;
    transition: all 0.3s cubic-bezier(0.5, 0, 0.75, 0);
    &:disabled {
      background-color: var(--kui-gray-150);
    }
    &:first-of-type {
      border-top-left-radius: 5px;
      border-bottom-left-radius: 5px;
    }
    &:last-of-type {
      border-top-right-radius: 5px;
      border-bottom-right-radius: 5px;
    }
    &.active {
      background-color: var(--kui-red-400);
      color: var(--kui-white);
      &:disabled {
        opacity: 0.5;
      }
    }
  }
}
