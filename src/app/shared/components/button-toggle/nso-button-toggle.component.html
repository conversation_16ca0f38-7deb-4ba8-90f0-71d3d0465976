<ng-container *ngFor="let item of items">
  <button
    *ngIf="item"
    (click)="handleButtonClick(item)"
    [class.active]="(value$ | async) === item"
    [attr.aria-pressed]="(value$ | async) === item"
    [disabled]="isDisabled$ | async"
  >
    <ng-container *ngIf="item.templateRef" [ngTemplateOutlet]="item.templateRef"></ng-container>
    <ng-container *ngIf="!item.templateRef"> {{ item.title || '' }}</ng-container>
  </button>
</ng-container>
