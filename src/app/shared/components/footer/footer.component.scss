@use 'shared' as *;

:host {
  display: block;
  width: 100%;
}

.footer {
  font-family: var(--kui-font-primary);
  background-color: var(--kui-gray-550);
  width: 100%;
  padding: 60px 0 38px;
  display: flex;
  align-items: center;

  @include media-breakpoint-down(sm) {
    padding: 50px 0 32px;
  }

  &-wrapper {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding-left: 20px;
    padding-right: 20px;
    @media screen and (max-width: 1250px) {
      padding-left: 20px;
      padding-right: 20px;
    }
  }

  .footer-top {
    display: flex;
    justify-content: space-between;
    margin-bottom: 50px;
    align-items: center;

    @include media-breakpoint-down(sm) {
      margin-bottom: 15px;
    }

    .nso-logo {
      width: 76px;
      height: 34px;
    }
  }

  .mini-icon {
    width: 24px;
    height: 24px;
  }

  .social-icons-wrapper {
    width: 112px;
    display: flex;
    justify-content: space-between;

    a {
      max-height: 24px;
    }
  }

  .copyright {
    font-weight: 400;
    font-size: 16px;
    color: var(--kui-gray-300);

    @include media-breakpoint-down(sm) {
      text-align: center;
      font-size: 12px;
      font-weight: 400;
      line-height: 16px;
      display: block;
      margin-top: 24px;
    }
  }
}

.icon-logo-red {
  width: 76px;
  height: 34px;
}

.footer-lead {
  cursor: pointer;
  width: 100%;

  .no-link,
  a {
    color: var(--kui-red-400);
  }

  .no-link {
    cursor: default;
  }

  i {
    height: 24px;
    width: 24px;
    display: none;
  }
  @include media-breakpoint-down(sm) {
    margin: 10px 0;
    font-size: 24px;
    line-height: 28px;
    text-transform: uppercase;
    display: flex;
    justify-content: space-between;
    .no-link,
    a {
      color: var(--kui-white);
    }
    i {
      display: block;
      height: 24px;
      width: 24px;
    }
  }
}

.jump-top {
  display: flex;
  justify-content: flex-end;
  padding-bottom: 24px;
  cursor: pointer;

  @include media-breakpoint-down(sm) {
    display: none;
  }

  &-text {
    color: var(--kui-white);
    margin: auto 10px;
    font-weight: 700;
    font-size: 16px;
  }

  &-icon {
    width: 42px;
    height: 42px;
    border-radius: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    transform: rotate(180deg);
  }
}

.footer-menu {
  display: grid;
  width: 100%;

  grid-template-columns: repeat(1, 1fr);
  gap: 0;

  @include media-breakpoint-up(md) {
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
  }

  @include media-breakpoint-up(xl) {
    display: flex;
    justify-content: space-between;
    gap: 90px;
  }

  &-column {
    font-weight: 700;
    font-size: 16px;
    width: 100%;
    position: relative;
    break-inside: avoid-column;

    @include media-breakpoint-down(sm) {
      margin: 0 5px;
    }
  }

  &-content {
    overflow: hidden;
  }

  &-list {
    padding-bottom: 30px;
    cursor: pointer;

    @include media-breakpoint-down(sm) {
      max-height: 1vh;
      visibility: hidden;
      opacity: 0;
      transition:
        max-height 0.4s,
        opacity 0.5s linear;
      padding-bottom: 0;

      &.opened {
        visibility: visible;
        opacity: 1;
        max-height: 100vh;
      }
    }
  }
  &-item {
    margin: 10px 0;
  }
  &-link {
    color: var(--kui-white);
  }
}

.icon {
  width: 76px;
  height: 34px;

  @include media-breakpoint-down(sm) {
    width: 60px;
    height: 60px;
  }
}
