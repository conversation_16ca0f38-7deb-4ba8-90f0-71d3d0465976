<footer class="footer">
  <div class="footer-wrapper">
    <div class="footer-top">
      <kesma-icon name="nso-logo" [style.color]="redToYellow()" class="nso-logo"></kesma-icon>
      <div class="social-icons-wrapper">
        <a href="https://www.instagram.com/nso.hu/" target="_blank"><kesma-icon name="insta-mini" [style.color]="redToYellow()" [size]="24"></kesma-icon></a>
        <a href="https://www.facebook.com/nsonline" target="_blank"><kesma-icon name="facebook-mini" [style.color]="redToYellow()" [size]="24"></kesma-icon></a>
        <a href="https://www.youtube.com/@NemzetiSportOnline" target="_blank"
          ><kesma-icon name="youtube-mini" [style.color]="redToYellow()" [size]="24"></kesma-icon
        ></a>
      </div>
    </div>

    <div class="footer-menu">
      <div class="footer-menu-column" *ngFor="let col of data; let lastData = last">
        <div class="footer-menu-content" *ngFor="let menu of col.children; let lastChildren = last" (clickOutside)="closeMenu(menu.id)">
          <div class="footer-lead">
            <ng-container *ngIf="menu?.isCustomUrl; else link">
              <a [style.color]="redToYellow()" [href]="menu.link" target="_blank">{{ menu.title }}</a>
              <i *ngIf="menu.hasSubItems" (click)="toggleSubmenu(menu.id)" class="icon icon-chevron2"></i>
            </ng-container>
            <ng-template #link>
              <ng-container *ngIf="menu?.link?.length; else noLink">
                <a [style.color]="redToYellow()" [routerLink]="menu.link">{{ menu.title }}</a>
                <i *ngIf="menu.hasSubItems" (click)="toggleSubmenu(menu.id)" class="icon icon-chevron2"></i>
              </ng-container>
            </ng-template>
            <ng-template #noLink>
              <span class="footer-lead no-link" [style.color]="redToYellow()" (click)="toggleSubmenu(menu.id)">
                {{ menu.title }}
                <i *ngIf="menu.hasSubItems" class="icon icon-chevron2"></i>
              </span>
            </ng-template>
          </div>
          <ul class="footer-menu-list" [ngClass]="{ opened: menuStates[menu.id] }">
            <ng-container *ngFor="let item of menu.children; let lastItem = last">
              <li class="footer-menu-item">
                <ng-container *ngTemplateOutlet="!item.isCustomUrl ? normalLink : customUrl; context: { item: item }"></ng-container>
                <ng-template #normalLink let-item="item">
                  <a class="footer-menu-link" [routerLink]="item.link" [target]="item.target"> {{ item.title }}</a>
                </ng-template>
                <ng-template #customUrl let-item="item">
                  <a class="footer-menu-link" [href]="item.link" [target]="item.target"> {{ item.title }}</a>
                </ng-template>
              </li>
              <li *ngIf="lastData && lastChildren && lastItem" class="footer-menu-item">
                <a class="footer-menu-link" (click)="openCookieSettings()">Sütibeállítások</a>
              </li>
            </ng-container>
          </ul>
        </div>
      </div>
    </div>

    <div class="jump-top" (click)="onClickJumpToTop()">
      <span class="jump-top-text">Ugrás az oldal tetejére</span>
      <div class="jump-top-icon" [style.background]="redToYellow()">
        <kesma-icon [style.color]="whiteToGray()" name="icon-nso-arrow-down" [size]="24"></kesma-icon>
      </div>
      <!--  <i class="icon icon-chevron"></i> -->
    </div>
    <span class="copyright">A Nemzeti Sport Online kiadója a N.S. MÉDIA ÉS VAGYONKEZELŐ Kft. © Minden jog fenntartva</span>
  </div>
</footer>
