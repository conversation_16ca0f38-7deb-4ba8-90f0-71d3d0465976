import { Component, computed, inject, Input } from '@angular/core';
import { ClickOutsideDirective, IconComponent, SimplifiedMenuItem } from '@trendency/kesma-ui';
import { UtilService } from '@trendency/kesma-core';
import { Ng<PERSON>lass, NgForOf, NgIf, NgTemplateOutlet } from '@angular/common';
import { RouterLink } from '@angular/router';
import { ColorChangeService } from '../../services';

declare let __tcfapi: (command: string, version?: number, callback?: (response: any, success: boolean) => void, param?: any) => void;

@Component({
  selector: 'app-footer',
  templateUrl: './footer.component.html',
  styleUrls: ['./footer.component.scss'],
  imports: [NgForOf, ClickOutsideDirective, NgIf, RouterLink, NgClass, NgTemplateOutlet, IconComponent],
})
export class FooterComponent {
  private readonly colorChangeService = inject(ColorChangeService);

  @Input() data: SimplifiedMenuItem[] = [];
  menuStates: Record<string, boolean> = {};

  readonly redToYellow = computed(() => this.colorChangeService.redToYellow());
  readonly whiteToGray = computed(() => this.colorChangeService.whiteToGray());

  constructor(private readonly utils: UtilService) {}

  onClickJumpToTop(): void {
    if (!this.utils.isBrowser()) {
      return;
    }
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }

  toggleSubmenu(id: string): void {
    this.menuStates[id] = !this.menuStates[id];
  }

  closeMenu(id: string): void {
    this.menuStates[id] = false;
  }

  openCookieSettings(): void {
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    __tcfapi('displayConsentUi', 2, () => {}, true);
  }
}
