<section class="category-tabs">
  <div class="category-tabs-info" *ngIf="data?.logoImage || data?.name">
    <div *ngIf="data?.logoImage" class="category-tabs-logo" [ngStyle]="{ 'background-image': 'url(' + data?.logoImage + ')' }"></div>
    <h5 *ngIf="data?.name" class="category-tabs-title">{{ data?.name }}</h5>
  </div>
  <div class="category-tabs-tab-list">
    <ng-container *ngFor="let tabData of data?.list; let i = index">
      <a class="category-tabs-tab" [routerLink]="tabData.routerLink">
        {{ tabData.name }}
      </a>
    </ng-container>
  </div>
</section>
