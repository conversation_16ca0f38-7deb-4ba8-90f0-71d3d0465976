import { ChangeDetectionStrategy, Component } from '@angular/core';
import { BaseComponent } from '@trendency/kesma-ui';
import { CategoryTabsDefinitions } from '../../definitions';
import { Ng<PERSON>orOf, NgIf, Ng<PERSON>tyle } from '@angular/common';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'nso-category-tabs',
  templateUrl: './nso-category-tabs.component.html',
  styleUrls: ['./nso-category-tabs.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NgStyle, NgForOf, RouterLink],
})
export class NsoCategoryTabsComponent extends BaseComponent<CategoryTabsDefinitions> {}
