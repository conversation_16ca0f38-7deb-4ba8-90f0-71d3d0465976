@use 'shared' as *;

.category-tabs {
  display: flex;
  justify-content: flex-start;
  padding: 28px 15px;
  background: var(--kui-white);
  align-items: center;
  width: 100%;
  overflow: auto;
  box-shadow:
    0px 51px 112px rgba(182, 182, 182, 0.08),
    0px 15.375px 40.4676px rgba(182, 182, 182, 0.0550084),
    0px 6.38599px 21.0493px rgba(182, 182, 182, 0.0456943),
    0px 2.30969px 9.59641px rgba(182, 182, 182, 0.0338935);

  @include media-breakpoint-down(sm) {
    padding-left: 25px;
    padding-right: 25px;
  }

  &-tabs-info {
    width: 193px;
    justify-content: flex-start;
    align-items: center;
    display: flex;
  }

  &-logo {
    width: 32px;
    height: 32px;
    margin-right: 10px;
  }

  &-title {
    font-weight: bold;
    font-size: 16px;
    line-height: 120%;
    width: 150px;
    display: block;
    text-overflow: ellipsis;
    overflow: hidden;
    border-right: 1px solid var(--kui-gray-200);
    margin-right: 10px;
  }

  &-tab-list {
    display: flex;
    justify-content: flex-start;
  }

  &-tab {
    white-space: nowrap;
    font-weight: normal;
    font-size: 16px;
    line-height: 160%;
    padding: 0 10px;
    color: var(--kui-gray-550);

    &.active {
      font-weight: bold;
    }

    &:hover {
      color: red;
    }
  }
}
