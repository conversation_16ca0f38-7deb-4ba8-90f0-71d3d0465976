@use 'shared' as *;

:host {
  display: block;
  margin-bottom: 40px;
  color: var(--kui-gray-1);
}

.type {
  font-family: var(--kui-font-condensed);
  font-weight: 700;
  color: var(--kui-black);
  font-size: 24px;
  margin: 0 0 24px 20px;
}

.cards-container {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  margin-bottom: 40px;
}

.card {
  border: 1px solid var(--kui-gray-200);
  box-sizing: border-box;
  margin: 10px;
  padding: 10px 0 0 0;
  min-width: 410.25px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  @include media-breakpoint-down(md) {
    min-width: 100%;
    padding: 10px;
  }

  .team-logo {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    margin-right: 15px;
    background-position: center;
    background-size: cover;
  }

  .team {
    display: flex;
    justify-content: space-between;
    align-items: center;

    &.home {
      padding: 20px 20px 10px 20px;
    }

    &.away {
      padding: 10px 20px 20px 20px;
    }

    @include media-breakpoint-down(md) {
      padding: 10px;
    }

    &-container {
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
    }

    .team-name {
      font-weight: 400;
      font-size: 16px;
      line-height: 19px;
    }

    .team-name-advantage {
      font-weight: 700;
    }
  }

  .mobile-score {
    font-weight: 700;
    font-size: 22px;
    display: none;

    @include media-breakpoint-down(md) {
      display: block;
    }
  }

  .actual {
    font-family: var(--kui-font-primary);
    font-weight: 400;
    color: var(--kui-gray-550);
    font-size: 16px;
    line-height: 25px;
    background: var(--kui-gray-100);

    @include media-breakpoint-down(md) {
      font-weight: 700;
    }

    .status {
      padding: 10px;
      background: var(--kui-gray-100);
    }
  }

  .where {
    font-weight: 700;
    background: var(--kui-gray-100);
    padding: 10px;

    @include media-breakpoint-down(md) {
      display: none;
    }
  }
}
