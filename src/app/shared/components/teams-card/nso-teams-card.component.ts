import { ChangeDetectionStrategy, Component } from '@angular/core';
import { BaseComponent } from '@trendency/kesma-ui';
import { MatchSeries } from '../../definitions';
import { Ng<PERSON><PERSON>, NgForOf, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, NgTemplateOutlet } from '@angular/common';
import { FormatPipeModule } from 'ngx-date-fns';
import { ScoreFormatPipe } from '../../pipes';

@Component({
  selector: 'nso-teams-card',
  templateUrl: './nso-teams-card.component.html',
  styleUrls: ['./nso-teams-card.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgForOf, NgStyle, NgClass, NgIf, NgTemplateOutlet, FormatPipeModule, ScoreFormatPipe],
})
export class NsoTeamsCardComponent extends BaseComponent<MatchSeries> {}
