<h3 class="type">{{ data?.matchType }}</h3>
<section class="cards-container">
  <ng-container *ngFor="let match of data?.matches">
    <div class="card">
      <section class="team home">
        <div class="team-container">
          <div class="team-logo" [ngStyle]="{ 'background-image': 'url(' + match?.teamOne?.teamLogo + ')' }"></div>
          <p [ngClass]="{ 'team-name-advantage': match?.teamOne?.score! >= match?.teamTwo?.score! }" class="team-name">{{ match?.teamOne?.teamName }}</p>
        </div>
        <span class="mobile-score">{{ match?.teamOne?.score }}</span>
      </section>
      <section class="team away">
        <div class="team-container">
          <div class="team-logo" [ngStyle]="{ 'background-image': 'url(' + match?.teamTwo?.teamLogo + ')' }"></div>
          <p [ngClass]="{ 'team-name-advantage': match?.teamTwo?.score! >= match?.teamOne?.score! }" class="team-name">{{ match?.teamTwo?.teamName }}</p>
        </div>
        <span class="mobile-score">{{ match?.teamTwo?.score }}</span>
      </section>

      <section class="actual">
        <ng-container
          *ngIf="match?.hasRematch && match?.matchCount === 2"
          [ngTemplateOutlet]="outcome"
          [ngTemplateOutletContext]="{ match: match }"
        ></ng-container>
        <ng-container *ngIf="match?.hasRematch && match?.matchCount === 1" [ngTemplateOutlet]="firstMatch"> </ng-container>

        <p class="where">Kezdődik: {{ match?.matchDate | dfnsFormat: 'HH:mm' }}, {{ match?.channel }}</p>
      </section>
    </div>
  </ng-container>
</section>

<ng-template #outcome let-match="match">
  <ng-container *ngIf="match?.teamOne?.score > match?.teamTwo?.score">
    <p class="status">Második mérkőzés, {{ match?.teamOne?.teamName }} vezet {{ match?.teamOne?.score }} - {{ match?.teamTwo?.score | scoreFormat }}</p>
  </ng-container>
  <ng-container *ngIf="match?.teamOne?.score === match?.teamTwo?.score">
    <p class="status">Második mérkőzés, az első mérkőzés {{ match?.teamOne?.score }} - {{ match?.teamTwo?.score }} lett</p>
  </ng-container>
  <ng-container *ngIf="match?.teamOne?.score < match?.teamTwo?.score">
    <p class="status">Második mérkőzés, {{ match?.teamTwo?.teamName }} vezet {{ match?.teamTwo?.score }} - {{ match?.teamOne?.score | scoreFormat }}</p>
  </ng-container>
</ng-template>

<ng-template #firstMatch>
  <p class="status">Első mérkőzés</p>
</ng-template>
