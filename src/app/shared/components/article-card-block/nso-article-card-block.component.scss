@use 'shared' as *;

@mixin hide-mixin($hided-elements) {
  .article-column:nth-last-of-type(-n + #{$hided-elements}) .article-divider {
    display: none;
  }
}

:host {
  display: flex;
  flex-direction: column;
  gap: 16px;

  @include media-breakpoint-down(md) {
    gap: 0;
  }

  .article {
    &-row {
      display: flex;
      flex-flow: row wrap;
      gap: 24px;
    }

    &-column {
      flex: 1 1 auto;
      width: calc(33% - 24px);

      @include media-breakpoint-down(lg) {
        width: calc(50% - 24px);
      }

      @include media-breakpoint-down(md) {
        width: 100%;
      }
    }
  }

  &:not(.mobile) {
    @extend %hide-last-n-divider;
  }

  &.mobile {
    @include hide-mixin(1);

    .article {
      &-row {
        flex-direction: column;
      }

      &-column {
        width: 100%;
      }
    }
  }
}

%hide-last-n-divider {
  @include media-breakpoint-up(lg) {
    @include hide-mixin(3);
  }

  @include media-breakpoint-up(md) {
    @include hide-mixin(2);
  }

  @include media-breakpoint-down(md) {
    @include hide-mixin(1);
  }
}
