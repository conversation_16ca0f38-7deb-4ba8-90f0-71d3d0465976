import { ChangeDetectionStrategy, Component } from '@angular/core';
import { ArticleCard, BaseComponent } from '@trendency/kesma-ui';
import { ArticleCardType } from '../../definitions';
import { NsoArticleCardComponent } from '../article-card/nso-article-card.component';
import { NgForOf } from '@angular/common';

@Component({
  selector: 'nso-article-block',
  templateUrl: './nso-article-card-block.component.html',
  styleUrls: ['./nso-article-card-block.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NsoArticleCardComponent, NgForOf],
})
export class NsoArticleCardBlockComponent extends BaseComponent<ArticleCard[]> {
  readonly articleCardType = ArticleCardType;
}
