@use 'shared' as *;

$origo-sport-primary: #27aae1;
$origo-blue-200: #cedcff;

:host {
  display: flex;
  flex-direction: column;
  margin-bottom: 30px;
  gap: 16px;
  margin-top: -30px;

  @include media-breakpoint-down(md) {
    width: 100%;
  }
}

.article-header {
  background-color: $origo-sport-primary;
  cursor: pointer;
  padding: 8px;
}

.article-container {
  display: flex;
  flex-flow: row wrap;
  column-gap: 40px;
  row-gap: 16px;

  @include media-breakpoint-down(md) {
    flex-direction: column;
    flex-wrap: nowrap;
  }

  .origo-article {
    flex: 1 1 auto;
    width: calc(33% - 40px);
    padding-bottom: 16px;

    @include media-breakpoint-down(md) {
      width: 100%;
    }
    &.bordered {
      border-bottom: 1px solid $origo-blue-200;
    }

    &-anchor {
      height: 100%;
      display: flex;
      flex-direction: column;
      gap: 16px;
    }
  }

  &-image-with-title {
    display: flex;
    flex-direction: column;
    gap: 16px;

    &.row-layout {
      @include media-breakpoint-up(lg) {
        flex-direction: row-reverse;
        align-items: center;
        gap: 8px;
      }
    }
    img {
      width: 100%;
      aspect-ratio: 4/3;
      object-fit: cover;

      &.small-image {
        @include media-breakpoint-up(lg) {
          height: 115px;
          width: 50%;
        }
      }
    }
  }

  &-title {
    font-size: 20px;
    font-style: normal;
    font-weight: 700;
    line-height: 24px;

    &.small-title {
      @include media-breakpoint-up(lg) {
        font-size: 16px;
        line-height: 20px;
        letter-spacing: 0.08px;
        width: 50%;
      }
    }
  }

  &-lead {
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    letter-spacing: 0.08px;
  }

  &-title,
  &-lead {
    color: var(--kui-black);
  }
}
