import { ChangeDetectionStrategy, Component, inject, OnInit } from '@angular/core';
import { ExternalBrandingBoxArticle } from '@trendency/kesma-ui';
import { Observable } from 'rxjs';
import { AsyncPipe, NgForOf, NgIf } from '@angular/common';
import { TrafficDeflectorService } from '../../services';

@Component({
  selector: 'app-origo-sport-branding-box',
  templateUrl: './origo-sport-branding-box.component.html',
  styleUrls: ['./origo-sport-branding-box.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, AsyncPipe, NgForOf],
})
export class OrigoSportBrandingBoxComponent implements OnInit {
  trafficDeflectorService = inject(TrafficDeflectorService);
  data$: Observable<ExternalBrandingBoxArticle[] | undefined>;

  ngOnInit(): void {
    this.data$ = this.trafficDeflectorService.getTrafficDeflectorData('Origo for NSO', 6);
  }
}
