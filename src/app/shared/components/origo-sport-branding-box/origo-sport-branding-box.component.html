<div class="article-header">
  <a href="https://www.origosport.hu" target="_blank">
    <img src="/assets/images/origo-sport.svg" alt="Origo Sport" width="221" height="24" loading="lazy" />
  </a>
</div>

<div class="article-container" *ngIf="data$ | async as articles">
  <ng-container *ngFor="let article of articles; let i = index">
    <article class="origo-article" [class.bordered]="i < 3">
      <a [href]="article?.url" target="_blank" class="origo-article-anchor">
        <div class="article-container-image-with-title row-layout">
          <img [src]="article?.imageUrl || '/assets/images/origo-placeholder.svg'" class="small-image" loading="lazy" />
          <h3 class="article-container-title small-title">{{ article?.title }}</h3>
        </div>
        <span class="article-container-lead">{{ article?.lead }}</span>
      </a>
    </article>
  </ng-container>
</div>
