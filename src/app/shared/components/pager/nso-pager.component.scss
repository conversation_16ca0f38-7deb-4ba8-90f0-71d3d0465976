@use 'shared' as *;

:host {
  display: block;
  margin-bottom: 20px;

  .pager {
    position: relative;

    .button-arrow {
      @extend %flex-container;
      border-radius: 5px;

      .icon-prev {
        background-image: url('/assets/images/icons/left-arrow.svg');
      }
      .icon-next {
        background-image: url('/assets/images/icons/right-arrow.svg');
      }

      &.last,
      &.first {
        display: none;
      }

      &.next,
      &.prev {
        background-color: var(--kui-gray-100);
      }

      &.next {
        margin-left: 10px;
      }

      &.prev {
        margin-right: 10px;
      }

      .icon {
        width: 15px;
        height: 13px;
      }
    }

    .count-pager {
      @extend %flex-container;
      border-radius: 5px;
      border: 1px solid var(--kui-gray-300);
    }

    .separator {
      margin: 0 5px;
    }

    .count-number {
      font-weight: 400;
      font-size: 16px;
      font-family: var(--kui-font-primary);
    }
  }

  .disabled {
    background-color: var(--kui-gray-50);
  }
}

%flex-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
}
