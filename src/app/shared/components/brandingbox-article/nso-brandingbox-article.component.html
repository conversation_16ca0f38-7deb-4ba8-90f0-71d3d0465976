<ng-container *ngIf="data">
  <div class="main-item">
    <a *ngIf="data?.[0]" [routerLink]="buildArticleUrl(data?.[0])" class="main-item-link">
      <img [src]="data?.[0]?.thumbnail?.url || 'assets/images/nemzetisport.png'" [alt]="data?.[0]?.title" class="main-item-image" loading="lazy" />
    </a>

    <div class="details" [routerLink]="buildArticleUrl(data?.[0])">
      <a class="details-link" [routerLink]="buildArticleUrl(data?.[0])">
        <span class="generator">Utánpótlássport</span>
      </a>
      <a class="details-link" [routerLink]="buildArticleUrl(data?.[0])">
        <span class="title">{{ data?.[0]?.title }}</span>
      </a>
    </div>
  </div>

  <ul class="side-items-wrapper">
    <li *ngFor="let item of data | slice: 1; trackBy: trackByFn" class="side-item">
      <a *ngIf="item" [routerLink]="buildArticleUrl(item)" class="side-item-link">{{ item?.title }}</a>
    </li>
  </ul>

  <a href="https://www.utanpotlassport.hu/" target="_blank" class="navigate">Tovább az Utánpótlássport-ra</a>
</ng-container>
