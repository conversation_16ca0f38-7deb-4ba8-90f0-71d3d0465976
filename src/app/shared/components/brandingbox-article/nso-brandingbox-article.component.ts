import { Component, ChangeDetectionStrategy } from '@angular/core';
import { ArticleCard, BaseComponent, buildArticleUrl } from '@trendency/kesma-ui';
import { NgForOf, NgIf, SlicePipe } from '@angular/common';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'nso-brandingbox-article',
  templateUrl: './nso-brandingbox-article.component.html',
  styleUrls: ['./nso-brandingbox-article.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, RouterLink, NgForOf, SlicePipe],
})
export class NsoBrandingboxArticleComponent extends BaseComponent<ArticleCard[]> {
  readonly buildArticleUrl = buildArticleUrl;
}
