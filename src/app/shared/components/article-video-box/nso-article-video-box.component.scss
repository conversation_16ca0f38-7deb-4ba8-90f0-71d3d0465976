@use 'shared' as *;

:host {
  display: block;
  background-color: var(--kui-gray-550);
  padding: 40px 40px 80px 40px;
  width: 100%;
  position: relative;
  container-type: inline-size;

  @include media-breakpoint-down(md) {
    padding: 48px 32px;
  }

  @include media-breakpoint-down(md) {
    padding: 48px 32px;
  }

  .articles {
    margin-top: 32px;
    display: grid;
    grid-template-rows: 2fr;
    grid-template-columns: 2fr 1fr;
    gap: 24px;

    @include media-breakpoint-down(md) {
      grid-template-columns: 1fr;
      gap: 16px;

      // Can't use @extend inside @include.
      ::ng-deep {
        .icon-play {
          width: 32px;
          height: 32px;
        }
      }
    }

    @container (max-width: 400px) {
      grid-template-columns: 1fr;
      gap: 16px;

      // Can't use @extend inside @container.
      ::ng-deep {
        .icon-play {
          width: 32px;
          height: 32px;
        }
      }
    }

    &-main {
      ::ng-deep {
        nso-article-card {
          &,
          & > article,
          & .article-card-figure,
          & .article-card-thumbnail-box,
          & img {
            height: 100%;
          }
          & .article-card-title {
            padding: 0px 120px 16px 24px;

            @include media-breakpoint-down(lg) {
              padding: 0px 34px 8px 16px;
            }

            @container (max-width: 400px) {
              padding: 0px 34px 8px 16px;
              font-size: 14px;
              padding-inline: 16px;
              padding-bottom: 8px;
            }
          }
        }
      }
    }

    &-recommender {
      display: flex;
      flex-direction: column;
      @extend %icon-mobile;
      gap: 24px;

      @include media-breakpoint-down(md) {
        margin-bottom: 20px;
        gap: 16px;
      }

      @container (max-width: 400px) {
        margin-bottom: 20px;
        gap: 16px;
      }

      ::ng-deep {
        nso-article-card {
          &.style-FeaturedTitleInsideImg {
            .article-card {
              &-title {
                font-size: 14px;
                padding-inline: 16px;
                padding-bottom: 8px;

                @include media-breakpoint-down(lg) {
                  padding: 0px 34px 8px 16px;
                }

                @container (max-width: 400px) {
                  padding: 0px 34px 8px 16px;
                }
              }
            }
          }
        }
      }
    }
  }

  ::ng-deep {
    nso-block-title-row {
      .block-container > .block-title {
        color: var(--kui-white) !important;
        font-family: var(--kui-font-condensed) !important;
      }

      .heading-line-link > .heading-line-title {
        text-align: center;
        padding: 0;
      }

      @include media-breakpoint-down(md) {
        .heading-line-right > .heading-line-right-more {
          position: absolute;
          text-align: center;
          margin-bottom: 25px;
          border-bottom: none;
          left: 0;
          right: 0;
          bottom: 0;
        }
      }
    }

    nso-article-card {
      margin-bottom: 0;
    }
  }
}

%icon-mobile {
  ::ng-deep {
    .icon-play {
      width: 32px;
      height: 32px;
    }
  }
}
