<nso-block-title-row *ngIf="blockTitle" [data]="blockTitle"></nso-block-title-row>

<div class="articles">
  <div class="articles-main">
    <ng-container *ngIf="data?.[0] as bigArticle">
      <nso-article-card nso-article-card [styleID]="ArticleCardType.FeaturedTitleInsideImg" [data]="bigArticle"> </nso-article-card>
    </ng-container>
  </div>
  <div class="articles-recommender">
    <ng-container *ngFor="let article of data | slice: 1 : count">
      <nso-article-card nso-article-card [styleID]="ArticleCardType.FeaturedTitleInsideImg" [data]="article"> </nso-article-card>
    </ng-container>
  </div>
</div>
