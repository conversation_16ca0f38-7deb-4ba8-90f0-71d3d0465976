import { ChangeDetectionStrategy, Component, HostBinding, Input } from '@angular/core';
import { ArticleCard, BaseComponent, BlockTitle } from '@trendency/kesma-ui';
import { ArticleCardType } from '../../definitions';
import { NsoBlockTitleRowComponent } from '../block-title-row/nso-block-title-row.component';
import { NgForOf, NgIf, SlicePipe } from '@angular/common';
import { NsoArticleCardComponent } from '../article-card/nso-article-card.component';

@Component({
  selector: 'nso-article-video-box',
  templateUrl: 'nso-article-video-box.component.html',
  styleUrls: ['nso-article-video-box.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NsoBlockTitleRowComponent, NgIf, NsoArticleCardComponent, NgForO<PERSON>, SlicePipe],
})
export class NsoArticleVideoBoxComponent extends BaseComponent<ArticleCard[]> {
  @HostBinding('class') hostClass = '';

  @Input() blockTitle?: BlockTitle;

  /**
   * Number of displayed articles.
   */
  public count = 3;

  public ArticleCardType = ArticleCardType;
}
