<div class="links">
  <nso-simple-button [wide]="true" round="round" (click)="reloadHomePage()"><PERSON><PERSON><PERSON><PERSON> híreink</nso-simple-button>
  <nso-simple-button [wide]="true" round="round" routerLink="/nso-hirek"><PERSON>ss<PERSON> hírünk</nso-simple-button>
</div>
<div class="nso-sport-radio" [class.in-sidebar]="desktopWidth < 6">
  <div class="logo" [class.in-sidebar]="desktopWidth < 6">
    <img class="logo-brand" [class.in-sidebar]="desktopWidth < 6" src="assets/images/sport-radio/nemzeti-sport-radio-logo.svg" alt="" loading="lazy" />
    <span class="logo-frequency" [class.in-sidebar]="desktopWidth < 6">105,9 MHz</span>
  </div>
  <div class="controls" [class.in-sidebar]="desktopWidth < 6">
    <nso-simple-button
      [class.in-sidebar]="desktopWidth < 6"
      round="circle"
      color="primary"
      [icon]="playing ? 'pause' : 'play'"
      iconPosition="center"
      (click)="playPause()"
    ></nso-simple-button>
    <div *ngIf="!duration" class="live">Élő</div>
    <div class="timing">
      <p class="timing-text" [class.in-sidebar]="desktopWidth < 6">{{ timeString$ | async }}</p>
      <div class="progress-bar">
        <div class="progress-bar-progress" [style.width]="progress$ | async"></div>
      </div>
      <nso-simple-button
        class="volume-control"
        [class.in-sidebar]="desktopWidth < 6"
        round="circle"
        color="secondary"
        [icon]="volume ? 'sound' : 'mute'"
        iconPosition="center"
        (click)="volumeClick()"
        [styleInfo]="volumeStyleInfo"
      ></nso-simple-button>
    </div>
  </div>
</div>
