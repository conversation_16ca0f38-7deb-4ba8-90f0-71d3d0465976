@use 'shared' as *;

:host {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;

  .links {
    display: none;
    margin-bottom: 10px;
    gap: 10px;

    @include media-breakpoint-down(md) {
      display: flex;
    }
  }

  .nso-sport-radio {
    display: flex;
    max-height: 80px;
    justify-content: stretch;
    align-items: center;
    background-color: var(--kui-gray-550);
    color: var(--kui-white);

    &.in-sidebar {
      @include media-breakpoint-up(lg) {
        flex-direction: column;
        max-height: none;
      }
    }

    @include media-breakpoint-down(md) {
      flex-direction: column;
      max-height: none;
    }

    nso-simple-button {
      margin: 0;
      width: 44px;
      height: 44px;
      ::ng-deep {
        i.icon.icon-pause {
          margin-left: -3px;
        }
      }

      &.in-sidebar {
        @include media-breakpoint-up(lg) {
          transform: scale(0.5);
          margin-left: -12px;
        }
      }

      @include media-breakpoint-down(md) {
        transform: scale(0.5);
        margin-left: -12px;
      }
    }

    .live {
      display: flex;
      align-items: center;
      color: var(--kui-red-400);
      font-family: var(--kui-font-primary);
      font-size: 16px;
      font-weight: 700;

      &:before {
        content: '';
        display: block;
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background-color: var(--kui-red-400);
        margin: 0 10px 0 -16px;
      }
    }

    .volume-control {
      &.in-sidebar {
        @include media-breakpoint-up(lg) {
          display: none;
        }
      }

      @include media-breakpoint-down(md) {
        display: none;
      }
    }

    .logo {
      position: relative;
      display: flex;
      align-items: center;
      width: 210px;
      height: 80px;
      background-image: url('/assets/images/sport-radio/sport-radio-bg-desktop.webp');
      justify-content: center;
      background-size: 152px 80px;
      background-repeat: no-repeat;

      &.in-sidebar {
        @include media-breakpoint-up(lg) {
          width: 100%;
          height: 54px;
          background-image: url('/assets/images/sport-radio/sport-radio-bg.webp');
          background-size: 100% 54px;
          justify-content: left;
          padding: 8px;
        }
      }

      @include media-breakpoint-down(md) {
        width: 100%;
        height: 54px;
        background-image: url('/assets/images/sport-radio/sport-radio-bg.webp');
        background-size: 100% 54px;
        justify-content: left;
        padding: 8px;
      }

      &-brand {
        width: 106px;
        height: 63px;

        &.in-sidebar {
          @include media-breakpoint-up(lg) {
            width: 64px;
            height: 38px;
          }
        }

        @include media-breakpoint-down(md) {
          width: 64px;
          height: 38px;
        }
      }

      &-frequency {
        position: absolute;
        top: 8px;
        left: 8px;
        text-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.25);
        font-family:
          Alibaba Sans,
          var(--kui-font-primary);
        font-size: 10px;
        font-style: italic;
        font-weight: 800;
        line-height: normal;

        &.in-sidebar {
          @include media-breakpoint-up(lg) {
            position: static;
            margin-left: 12px;
          }
        }

        @include media-breakpoint-down(md) {
          position: static;
          margin-left: 12px;
        }
      }
    }

    .controls {
      width: 100%;
      display: flex;
      align-items: center;
      gap: 32px;
      margin: 16px 24px;

      &.in-sidebar {
        @include media-breakpoint-up(lg) {
          margin: 0;
          height: 56px;
          padding: 16px 24px;
          gap: 16px;
        }
      }

      @include media-breakpoint-down(md) {
        margin: 0;
        height: 56px;
        padding: 16px 24px;
        gap: 16px;
      }
    }

    .timing {
      display: flex;
      width: 100%;
      gap: 8px;
      align-items: center;
      font-family: var(--kui-font-condensed);
      font-size: 16px;
      font-weight: 600;

      &-text {
        &.in-sidebar {
          @include media-breakpoint-up(lg) {
            display: none;
          }
        }

        @include media-breakpoint-down(md) {
          display: none;
        }
      }

      & > * {
        width: fit-content;
      }

      .progress-bar {
        width: 100%;
        height: 4px;
        border-radius: 2px;
        background-color: var(--kui-gray-75);

        &-progress {
          height: 100%;
          border-radius: 2px;
          background-color: var(--kui-red-400);
          width: 0;
        }
      }
    }

    .icon {
      width: 15px;
      height: 15px;
    }
  }
}
