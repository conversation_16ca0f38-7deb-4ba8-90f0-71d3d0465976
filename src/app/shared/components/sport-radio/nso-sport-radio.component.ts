import { ChangeDetectionStrategy, Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { ButtonStyleInfo, Stopwatch } from '@trendency/kesma-ui';
import { map } from 'rxjs';
import { NsoSimpleButtonComponent } from '../simple-button/nso-simple-button.component';
import { AsyncPipe, NgIf } from '@angular/common';
import { RouterLink } from '@angular/router';

const DEFAULT_VOLUME = 0.5;

@Component({
  selector: 'nso-sport-radio',
  templateUrl: './nso-sport-radio.component.html',
  styleUrls: ['./nso-sport-radio.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NsoSimpleButtonComponent, NgIf, AsyncPipe, RouterLink],
})
export class NsoSportRadioComponent implements OnInit, OnDestroy {
  @Input() playing = false;
  @Output() playingChange = new EventEmitter<boolean>();

  @Input() duration = 0;
  @Input() volume = DEFAULT_VOLUME;
  @Output() volumeChange = new EventEmitter<number>();
  @Input() desktopWidth = 12;

  readonly stopWatch = new Stopwatch();

  /** Format: mm:ss */
  readonly timeString$ = this.stopWatch.time$.pipe(map((sec) => `${`0${Math.floor(sec / 60)}`.slice(-2)}:${`0${sec % 60}`.slice(-2)}`));

  readonly progress$ = this.stopWatch.time$.pipe(
    map((sec) => Math.min(sec / this.actualDuration, 1) * 100), // Between 0 and 100
    map((percent) => `${percent}%`)
  );

  mobileStyleInfo: ButtonStyleInfo = {
    icon: {
      width: '11px',
      height: '11px',
      'margin-bottom': '-1px',
    },
    button: {
      padding: '2px 6px',
    },
  };

  volumeStyleInfo: ButtonStyleInfo = {
    icon: {
      width: '24px',
      height: '24px',
    },
    button: {
      padding: '9px 11px',
    },
  };

  get actualDuration(): number {
    return this.duration || 60;
  }

  ngOnInit(): void {
    if (this.playing) {
      this.stopWatch.startCount();
    }
  }

  ngOnDestroy(): void {
    this.stopWatch.destroy();
  }

  reloadHomePage(): void {
    window.location.href = '/';
  }

  playPause(): void {
    this.playing = !this.playing;
    this.playingChange.emit(this.playing);
    if (this.playing) {
      this.stopWatch.startCount();
      return;
    }

    this.stopWatch.stop();
  }

  volumeClick(): void {
    this.volume = this.volume === 0 ? DEFAULT_VOLUME : 0;
    this.volumeChange.emit(this.volume);
  }
}
