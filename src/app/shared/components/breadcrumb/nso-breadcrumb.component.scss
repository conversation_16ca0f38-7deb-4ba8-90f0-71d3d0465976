@use 'shared' as *;

:host {
  display: block;

  .breadcrumb-container {
    display: flex;
    flex-direction: row;
    width: 100%;
    justify-content: space-between;
    align-items: center;
    padding: 0;

    @include media-breakpoint-down(sm) {
      flex-direction: column;
      align-items: flex-start;
    }

    ul {
      display: flex;
      align-items: center;
      height: 35px;

      li:first-of-type {
        color: #00000066;
      }

      li:nth-of-type(2) {
        color: #00000099;
      }

      li {
        color: #000;
        display: inline-flex;
        align-items: center;
      }

      li a {
        color: inherit;
      }
    }

    .breadcrumb-item {
      span {
        display: flex;
        align-items: center;
      }

      a {
        display: flex;
        align-items: center;
      }
    }

    .logout {
      color: var(--kui-red-400);
      font-weight: 700;
      text-decoration: underline;
      cursor: pointer;

      @include media-breakpoint-down(sm) {
        margin-top: 10px;
      }
    }
  }
}

.separator {
  .icon-arrow-right-red {
    width: 34px;
    cursor: pointer;
    height: 14px;
  }
}
