import { Component, Input } from '@angular/core';
import { BreadcrumbComponent } from '@trendency/kesma-ui';
import { RouterLink } from '@angular/router';
import { NgForOf, NgIf, NgTemplateOutlet } from '@angular/common';

@Component({
  selector: 'nso-breadcrumb',
  templateUrl: './nso-breadcrumb.component.html',
  styleUrls: ['../../../../../node_modules/@trendency/kesma-ui/src/lib/components/breadcrumb/breadcrumb.component.scss', './nso-breadcrumb.component.scss'],
  imports: [RouterLink, NgForOf, NgTemplateOutlet, NgIf],
})
export class NsoBreadcrumbComponent extends BreadcrumbComponent {
  @Input() isShowLogout = false;
}
