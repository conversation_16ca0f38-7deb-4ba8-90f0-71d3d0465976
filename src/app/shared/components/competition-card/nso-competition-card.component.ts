import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import { BaseComponent, buildCompetitionUrl } from '@trendency/kesma-ui';
import { Competition } from '../../definitions';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'nso-competition-card',
  templateUrl: './nso-competition-card.component.html',
  styleUrls: ['./nso-competition-card.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [RouterLink],
})
export class NsoCompetitionCardComponent extends BaseComponent<Competition> implements OnInit {
  competitionUrl?: string[];

  constructor() {
    super();
  }

  protected override setProperties(): void {
    this.competitionUrl = this.data ? buildCompetitionUrl(this.data) : [];
  }
}
