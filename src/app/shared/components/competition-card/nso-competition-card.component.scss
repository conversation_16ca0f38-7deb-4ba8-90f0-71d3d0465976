@use 'shared' as *;

:host {
  display: block;
  padding: 16px;
  &:nth-child(odd) {
    background-color: var(--kui-gray-100);
  }
  &:not(:last-child) {
    margin-bottom: 0;
    ::ng-deep {
      .competition-card {
        margin-bottom: 0;
      }
    }
  }
  .competition-card-link {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    align-items: center;
    gap: 16px;
  }
  .competition-card-thumbnail {
    aspect-ratio: 1;
    object-fit: cover;
    height: 48px;
    width: 48px;
    border-radius: 48px;
    border: 1px solid var(--kui-gray-200);
  }
  .competition-card-title {
    color: var(--kui-red-500);
    text-decoration: underline;
    text-underline-offset: 7px;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: 170%;
  }
}
