import { ChangeDetectionStrategy, Component, Input, OnInit } from '@angular/core';
import { VoteData, VotingComponent } from '@trendency/kesma-ui';
import { AsyncPipe, NgForOf, NgIf } from '@angular/common';
import { NsoSimpleButtonComponent } from '../simple-button/nso-simple-button.component';

export enum progress {
  SHOW_VOTE,
  SHOW_THANKS,
  SHOW_RESULTS,
}

@Component({
  selector: 'nso-voting',
  templateUrl: './nso-voting.component.html',
  styleUrls: ['./nso-voting.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, AsyncPipe, NgForOf, NsoSimpleButtonComponent],
})
export class NsoVotingComponent extends VotingComponent implements OnInit {
  @Input() desktopWidth = 12;
  currentStatus = progress.SHOW_VOTE;
  progressEnum = progress;

  constructor() {
    super();
  }

  get voteData(): VoteData {
    return this.vm.state.voteData;
  }

  get accVoteId(): string | undefined {
    return this.voteData.id;
  }

  get sum(): number {
    return this.voteData?.voteCountSum ?? this.voteData.answers?.reduce((acc, curr) => acc + (Number(curr?.voteCount) ?? 0), 0) ?? 0;
  }

  override ngOnInit(): void {
    super.ngOnInit();
    if (this.vm.state.showResults) {
      this.setCurrentStatus();
    }
  }

  override onVote(): void {
    this.setCurrentStatus();
    super.onVote();
    if (!this.accVoteId) {
      return;
    }
  }

  setCurrentStatus(): void {
    switch (this.currentStatus) {
      case progress.SHOW_VOTE:
        this.currentStatus = progress.SHOW_THANKS;
        break;
      case progress.SHOW_THANKS:
        if (this.voteData.isResultVisible) {
          this.currentStatus = progress.SHOW_RESULTS;
        }
        break;
    }
  }
}
