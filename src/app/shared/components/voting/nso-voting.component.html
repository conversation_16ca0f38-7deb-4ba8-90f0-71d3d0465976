<ng-container *ngIf="vm.state$ | async as state">
  <section class="header">
    <h1 class="header-title">SZAVAZÁS</h1>
    <div class="separator"></div>
    <h2 class="header-question">{{ state?.voteData?.question }}</h2>
  </section>

  <section class="body">
    <ul>
      <li *ngFor="let item of state?.voteData?.answers ?? []">
        <span class="input-item">
          <label class="form-control" [class.in-small-width]="desktopWidth < 6 && state.hasExpired">
            <input
              [id]="item?.id"
              [class.expired]="state.hasExpired"
              [name]="'answers-' + data?.id"
              type="radio"
              [value]="item?.id"
              [disabled]="state.showResults"
              [checked]="voteId === item?.id"
              (click)="setVoteId(item?.id)"
            />
            <span class="answer">
              <span>{{ item?.answer }}</span>
            </span>
          </label>
          <span
            *ngIf="((state.showResults || state.hasExpired) && state?.voteData?.isResultVisible) || currentStatus === progressEnum.SHOW_RESULTS"
            class="highlighted-vote-count"
          >
            {{ item?.voteCount }} szavazat
            <span *ngIf="item?.votePercentage">({{ item?.votePercentage }}%)</span>
          </span>
        </span>
      </li>
    </ul>

    <div class="thank-you" [class.in-view]="currentStatus === progressEnum.SHOW_THANKS">
      <p>KÖSZÖNJÜK, HOGY SZAVAZOTT!</p>
    </div>
  </section>

  <section class="footer">
    <ng-container *ngIf="!isExpired">
      <nso-simple-button
        [wide]="true"
        *ngIf="
          (currentStatus === progressEnum.SHOW_VOTE && !state.showResults) || (currentStatus === progressEnum.SHOW_THANKS && state?.voteData?.isResultVisible)
        "
        [disabled]="!this.voteId"
        round="round"
        color="primary"
        (click)="onVote()"
      >
        {{ currentStatus === progressEnum.SHOW_VOTE ? 'SZAVAZOK' : 'MEGNÉZEM A SZAVAZATOKAT' }}
      </nso-simple-button>
    </ng-container>

    <ng-container
      *ngIf="
        (((currentStatus === progressEnum.SHOW_VOTE && state.showResults) || state.hasExpired) && state?.voteData?.isResultVisible) ||
        currentStatus === progressEnum.SHOW_RESULTS
      "
    >
      <div class="separator"></div>
      <div class="result">
        <span class="result-text">Összes szavazat</span>
        <span class="result-number">{{ sum }}</span>
      </div>
    </ng-container>
  </section>
</ng-container>
