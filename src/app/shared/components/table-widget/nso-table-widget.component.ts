import { ChangeDetectionStrategy, Component, ViewChild, TemplateRef, OnInit, ChangeDetectorRef, Input } from '@angular/core';
import { BaseComponent, buildTeamUrl, dataTableConfig, DataTableGeneratorComponent } from '@trendency/kesma-ui';
import { LeagueTable } from '../../definitions';
import { NgIf, UpperCasePipe } from '@angular/common';
import { RouterLink } from '@angular/router';
import { NsoSpinnerComponent } from '../spinner/nso-spinner.component';

@Component({
  selector: 'nso-table-widget',
  templateUrl: './nso-table-widget.component.html',
  styleUrls: ['./nso-table-widget.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [UpperCasePipe, NgIf, RouterLink, NsoSpinnerComponent, DataTableGeneratorComponent],
})
export class NsoTableWidgetComponent extends BaseComponent<LeagueTable[] | undefined | null> implements OnInit {
  @Input() title = '';
  @Input() leagueThumbnailUrl = '';
  @Input() leagueSlug?: string;
  @ViewChild('logoAndNameTemplate', {
    read: TemplateRef,
    static: true,
  })
  logoAndNameTemplate?: TemplateRef<HTMLElement>;

  constructor(private readonly cd: ChangeDetectorRef) {
    super();
  }

  public tableConfig: dataTableConfig[] = [];

  override ngOnInit(): void {
    super.ngOnInit();
    this.tableConfig = [
      {
        hasHeader: true,
        headerTitle: 'CSAPAT',
        headerColspan: 2,
        columnDataProperty: 'position',
        columnClass: 'first bold-cell',
        headerClass: 'first-header destop-header-border',
      },
      {
        hasHeader: false,
        headerTitle: 'CSAPAT',
        headerColspan: 1,
        columnDataProperty: 'team.team.shortName',
        customColumnTemplate: this.logoAndNameTemplate,
        columnClass: 'bold-cell',
        headerClass: 'destop-header-border',
      },
      {
        hasHeader: true,
        headerTitle: 'M',
        headerColspan: 1,
        columnDataProperty: 'all',
        headerClass: 'destop-header-border',
      },
      {
        hasHeader: true,
        headerTitle: 'GY',
        headerColspan: 1,
        columnDataProperty: 'win',
        headerClass: 'destop-header-border',
      },

      {
        hasHeader: true,
        headerTitle: 'D',
        headerColspan: 1,
        columnDataProperty: 'draw',
        headerClass: 'destop-header-border',
      },
      {
        hasHeader: true,
        headerTitle: 'V',
        headerColspan: 1,
        columnDataProperty: 'lose',
        headerClass: 'destop-header-border',
      },
      {
        hasHeader: true,
        headerTitle: 'P',
        headerColspan: 1,
        columnDataProperty: 'point',
        columnClass: 'last bold-cell',
        headerClass: 'last destop-header-border',
      },
    ];
    this.cd.detectChanges();
  }

  generateTeamLink(tableItem: LeagueTable): string[] {
    if (tableItem?.team?.team) {
      return buildTeamUrl({ slug: this.leagueSlug ?? '' }, tableItem?.team?.team);
    } else {
      return ['/', '404'];
    }
  }
}
