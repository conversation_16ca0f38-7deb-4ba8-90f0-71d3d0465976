@use 'shared' as *;

:host {
  display: block;
  margin-bottom: 20px;
  width: 100%;

  ::ng-deep {
    .desktop-header-title {
      @include media-breakpoint-down(md) {
        display: none;
      }
    }
    .mobile-header-title {
      @include media-breakpoint-up(md) {
        display: none;
      }
    }
    .not-on-mobile {
      @include media-breakpoint-down(md) {
        display: none;
      }
    }
    .even {
      background-color: var(--kui-gray-100);
    }
    .destop-header-border {
      border-bottom: 1px solid var(--kui-gray-300);
      border-top: 1px solid var(--kui-gray-300);
      padding-top: 16px;
      padding-bottom: 10px;
      font-size: 14px;
    }
    .bold-cell {
      font-weight: 700;
    }
    .first-header {
      padding-left: 16px;
      text-align: left;
      border-left: 1px solid var(--kui-gray-300);
    }
    .first {
      padding-left: 16px;
      border-left: 1px solid var(--kui-gray-300);
    }
    .last {
      padding-right: 16px;
      border-right: 1px solid var(--kui-gray-300);
    }
    .time-table {
      width: 100%;
      font-size: 13px;
      font-family: var(--kui-font-primary);
      color: var(--kui-black);
      border-bottom: 1px solid var(--kui-gray-300);
      tr {
        @include media-breakpoint-down(md) {
          text-align: center;
        }
      }
      td {
        min-width: 20px;
        padding-top: 16px;
        padding-bottom: 16px;
      }
    }
    .logo-name-wrapper {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      cursor: pointer;
    }
    .club-logo {
      width: 20px;
      height: 20px;
      border-radius: 50%;
      background-position: center;
      background-size: cover;
      border: 1px solid var(--kui-gray-200);
      margin-right: 8px;
    }
    .club-name {
      display: flex;
      justify-content: flex-start;
      align-content: center;
      flex-wrap: wrap;
      font-size: 13px;
    }
  }

  .spinner-container {
    width: 100%;
    display: flex;
    align-content: center;
    margin: 10px 0;
  }

  .no-data-text {
    text-align: center;
    font-size: 16px;
  }

  .table-widget {
    width: 100%;
    background: var(--kui-white);

    &-header {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 17px 16px;
      background-color: var(--kui-purple-600);
    }

    &-title {
      font-weight: bold;
      font-size: 24px;
      line-height: 120%;
      color: var(--kui-white);
      text-transform: uppercase;
      font-family: var(--kui-font-condensed);
    }

    &-icon {
      width: 32px;
      height: 32px;
      background-size: cover;
      background-position: right center;
      margin-left: 15px;
    }

    .teams {
      &-header {
        font-weight: bold;
        font-size: 14px;
        line-height: 120%;
        display: flex;
        padding: 21px 16px 10px;
      }

      &-name {
        width: 107px;
      }

      &-columns-wrapper {
        width: calc(100% - 107px);
        display: flex;
        justify-content: space-between;
      }

      &-column {
        width: calc(20% - 3px);
        text-align: center;
      }
    }

    .team-list {
      &-team-row {
        display: flex;
        padding: 21px 16px;

        &:nth-child(even) {
          background: var(--kui-gray-100);
        }
      }

      &-position {
        width: 20px;
        font-weight: bold;
        font-size: 12px;
        line-height: 120%;
      }

      &-name {
        text-transform: uppercase;
        width: 87px;
        font-weight: bold;
        font-size: 13px;
        line-height: 120%;
        display: flex;
        align-items: center;
        justify-content: flex-start;
      }

      &-icon {
        width: 16px;
        height: 16px;
        margin-right: 4px;
        margin-left: 2px;
      }

      &-columns-wrapper {
        width: calc(100% - 87px - 20px);
        display: flex;
        justify-content: space-between;
      }

      &-column {
        width: calc(20% - 3px);
        font-weight: normal;
        font-size: 13px;
        line-height: 120%;
        text-align: center;

        &.sum {
          font-weight: bold;
        }
      }
    }
  }
}
