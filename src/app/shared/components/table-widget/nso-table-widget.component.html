<div class="table-widget">
  <div class="table-widget-header">
    <h5 class="table-widget-title">{{ title + ' tabella' | uppercase }}</h5>
    <img *ngIf="leagueThumbnailUrl" class="table-widget-icon" [src]="leagueThumbnailUrl" alt="Bajnokság logo" loading="lazy" />
  </div>
</div>

<ng-template #logoAndNameTemplate let-rowData="data">
  <div class="logo-name-wrapper" [routerLink]="generateTeamLink(rowData)">
    <img class="club-logo" [src]="rowData?.team?.team?.logo ?? '/assets/images/nemzetisport.png'" alt="Csapat logo" loading="lazy" />
    <div class="club-name">
      {{ rowData?.team?.team?.shortName ?? rowData?.team?.team?.name }}
    </div>
  </div>
</ng-template>

<ng-container *ngIf="!data">
  <div class="spinner-container">
    <nso-spinner></nso-spinner>
  </div>
</ng-container>

<ng-container *ngIf="data && data.length <= 1">
  <p class="no-data-text">Nincs tabella adat</p>
</ng-container>

<ng-container *ngIf="data && data.length > 0">
  <kesma-data-table-generator [data]="data" [tableConfig]="tableConfig" [dataTableClass]="'time-table'"></kesma-data-table-generator>
</ng-container>
