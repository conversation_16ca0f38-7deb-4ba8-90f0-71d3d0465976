<main class="tripbox">
  <div class="tripbox-top">
    <h3 class="tripbox-title">{{ data?.tripName }}</h3>
    <a [routerLink]="allResult" class="tripbox-all-result"><span class="tripbox-all-result-text">Összes eredmény</span></a>
  </div>
  <div class="tripbox-row" *ngIf="data && filteredMatches.length > 0">
    <div *ngFor="let item of filteredMatches.slice(0, data?.matchesNumber); let index = index" class="col-12 col-lg-4 tripbox-article">
      <ng-container *ngIf="item?.scheduleStatus === 'not_started' || item?.scheduleStatus === 'ended'; else liveMatch">
        <ng-container *ngTemplateOutlet="match; context: { item, index }"></ng-container>
      </ng-container>
      <ng-template #liveMatch>
        <a class="tripbox-anchor" [routerLink]="['/', 'merkozes', item?.slug, 'elo']" target="_blank">
          <ng-container *ngTemplateOutlet="match; context: { item, index }"></ng-container>
        </a>
      </ng-template>
    </div>
  </div>
  <a [routerLink]="allResult" class="tripbox-mobile">Összes eredmény</a>
</main>

<ng-template #match let-item="item" let-index="index">
  <div class="tripbox-event" [class.tripbox-event-last-row]="index >= filteredMatches.length - numberOfelementsInRow">
    <div>
      <div class="tripbox-date-who">
        <span class="tripbox-date">{{ item?.scheduleDate?.date | dfnsFormat: 'MMM d, EEEE' | titleCase }}</span>
        <span class="tripbox-separator" *ngIf="item?.tvStation?.title">|</span>
        <span class="tripbox-who">{{ item?.tvStation?.title ?? '' }}</span>
      </div>
      <div class="tripbox-teams">
        <div class="tripbox-team">
          <div class="tripbox-club">
            <img
              class="tripbox-image-team"
              [src]="item?.homeTeam?.logo || item?.homeTeam?.team?.logo || '/assets/images/nemzetisport.png'"
              alt="logo"
              loading="lazy"
            />
            <span class="tripbox-name">{{ item?.homeTeam?.title }}</span>
          </div>
          <span class="tripbox-result">{{ isLive(item) ? item?.homeScore || 0 : '' }}</span>
        </div>
        <div class="tripbox-team">
          <div class="tripbox-club">
            <img
              class="tripbox-image-team"
              [src]="item?.awayTeam?.logo || item?.awayTeam?.team?.logo || '/assets/images/nemzetisport.png'"
              alt="logo"
              loading="lazy"
            />
            <span class="tripbox-name">{{ item?.awayTeam?.title }}</span>
          </div>
          <span class="tripbox-result">{{ isLive(item) ? item?.awayScore || 0 : '' }}</span>
        </div>
      </div>
    </div>
    <div *ngIf="isLive(item)">
      <span class="tripbox-live-text">élő</span>
    </div>
  </div>
</ng-template>
