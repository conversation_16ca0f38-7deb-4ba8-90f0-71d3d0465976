import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { UtilService } from '@trendency/kesma-core';
import { BaseComponent } from '@trendency/kesma-ui';
import { distinctUntilChanged, fromEvent, map, startWith, Subject, takeUntil } from 'rxjs';
import { ChampionshipSchedule, TripBoxDefinitions } from '../../definitions';
import { RouterLink } from '@angular/router';
import { NgForOf, NgIf, NgTemplateOutlet } from '@angular/common';
import { FormatPipeModule } from 'ngx-date-fns';
import { TitleCasePipe } from '../../pipes';

@Component({
  selector: 'nso-trip-box',
  templateUrl: './nso-trip-box.component.html',
  styleUrls: ['./nso-trip-box.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [RouterLink, NgIf, NgForOf, NgTemplateOutlet, FormatPipeModule, TitleCasePipe],
})
export class NsoTripBoxComponent extends BaseComponent<TripBoxDefinitions> implements OnInit {
  destroy$ = new Subject<void>();
  numberOfelementsInRow = 3;
  filteredMatches: ChampionshipSchedule[] = [];
  allResult = '';

  constructor(
    private readonly utilsService: UtilService,
    private readonly cd: ChangeDetectorRef
  ) {
    super();
  }

  currentWindowWidth$ = fromEvent(window, 'resize').pipe(
    map(() => window.innerWidth),
    startWith(window.innerWidth)
  );

  protected override setProperties(): void {
    if (this.data && this.data.matches?.length > 0) {
      this.setMatchesArray();
    }
  }

  public isLive(championshipSchedule: ChampionshipSchedule): boolean {
    return championshipSchedule?.scheduleStatus !== 'not_started' && championshipSchedule?.scheduleStatus !== 'ended';
  }

  override ngOnInit(): void {
    this.allResult = `/bajnoksag/${this.data?.selectedChampion?.slug}/eredmenyek`;
    if (this.utilsService.isBrowser()) {
      this.currentWindowWidth$.pipe(distinctUntilChanged(), takeUntil(this.destroy$)).subscribe((width: number) => {
        if (width >= 768) {
          this.numberOfelementsInRow = 3;
        } else if (width <= 767 && width >= 576) {
          this.numberOfelementsInRow = 2;
        } else {
          this.numberOfelementsInRow = 1;
        }
        this.cd.markForCheck();
      });
    }
  }

  private setMatchesArray(): void {
    const filtered: ChampionshipSchedule[] = this.data?.matches?.filter((match: ChampionshipSchedule) => match?.scheduleDate?.date >= new Date()) ?? [];
    this.filteredMatches = [...filtered];
    if (this.data?.matchesNumber && filtered?.length < this.data?.matchesNumber) {
      for (let i = this.data.matches?.length - filtered.length - 1; i >= this.data.matches?.length - this.data.matchesNumber; i--) {
        if (this.data.matches[i]) {
          this.filteredMatches.push(this.data.matches[i]);
        }
      }
    }
    this.filteredMatches = this.filteredMatches.sort((a, b) => Number(a?.scheduleDate?.date) - Number(b?.scheduleDate?.date));
    this.setDate(this.filteredMatches);
  }

  private setDate(filteredMatches: ChampionshipSchedule[]): void {
    filteredMatches.map((item: ChampionshipSchedule) => {
      if (item?.scheduleDate) {
        item.scheduleDate.date = new Date(item?.scheduleDate?.date);
      }
    });
    this.cd.markForCheck();
  }
}
