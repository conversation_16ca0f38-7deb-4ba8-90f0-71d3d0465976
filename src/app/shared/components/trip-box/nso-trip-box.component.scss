@use 'shared' as *;

:host {
  width: 100%;
}

.tripbox {
  background: var(--kui-gray-550);
  padding: 40px;
  color: var(--kui-white);
  &-row {
    display: flex;
    column-gap: 24px;
    flex-wrap: wrap;
    @include media-breakpoint-down(sm) {
      display: block;
    }
  }
  &-article {
    width: 30%;
    padding: 7px;
    @include media-breakpoint-down(md) {
      width: 50%;
    }
    @include media-breakpoint-down(sm) {
      width: 100%;
      display: block;
    }
  }
  &-mobile {
    display: none;
    @include media-breakpoint-down(sm) {
      display: block;
      text-align: center;
      margin-top: 40px;
      font-weight: bold;
      font-size: 16px;
      line-height: 120%;
      color: var(--kui-red-400);
    }
  }
  &-top {
    display: flex;
    justify-content: space-between;
    padding-bottom: 32px;
  }
  &-title {
    font-weight: bold;
    font-size: 24px;
    line-height: 120%;
    font-family: var(--kui-font-condensed);
    text-transform: uppercase;
    color: var(--kui-white);

    @include media-breakpoint-down(sm) {
      margin: 0 auto;
    }
  }
  &-all-result {
    display: block;
    font-style: normal;
    font-weight: bold;
    font-size: 16px;
    line-height: 120%;
    color: var(--kui-red-400);
    span {
      border-bottom: 1px solid var(--kui-red-400);
    }

    @include media-breakpoint-down(sm) {
      display: none;
    }
  }

  &-event {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    border-top: 1px solid var(--kui-gray-450);
    padding-top: 16px;
    height: 100%;
    &-last-row {
      display: block;
      border-bottom: 1px solid var(--kui-gray-450);
    }
  }
  &-live-text {
    text-transform: uppercase;
    color: var(--kui-red-400);
    font-size: 16px;
    font-weight: 700;
    display: inline-block;
    margin-bottom: 5px;
  }
  &-date-who {
    font-weight: bold;
    font-size: 16px;
    line-height: 120%;
    display: flex;
    align-items: center;
  }
  &-date {
    color: var(--kui-white);
  }

  &-anchor {
    height: 100%;
    display: inline-block;
    width: 100%;
  }
  &-separator {
    color: var(--kui-gray-200);
    margin: 0 16px;
    font-size: 24px;
    display: flex;
    align-items: center;
    font-weight: 300;
  }
  &-who {
    font-style: normal;
    font-weight: normal;
    font-size: 16px;
    line-height: 160%;
    color: var(--kui-gray-300);
  }
  &-team {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 16px 0;
  }
  &-club {
    display: flex;
    align-items: center;
  }
  &-image-team {
    width: 32px;
    height: 32px;
    border: 1px solid var(--kui-gray-200);
    background-size: cover;
    background-position: center;
    border-radius: 50%;
    margin-right: 8px;
  }
  &-name,
  &-result {
    font-weight: bold;
    font-size: 16px;
    line-height: 120%;
    padding-right: 20px;
    color: var(--kui-white);
  }
}
