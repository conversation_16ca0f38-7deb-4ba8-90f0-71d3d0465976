@use 'shared' as *;

:host::ng-deep {
  display: block;
  width: 100%;
  font-size: 16px;

  @include media-breakpoint-down(xl) {
    overflow-x: auto;
  }

  .team-league {
    width: 100%;
    border: 1px solid var(--kui-gray-200);

    @include media-breakpoint-down(xl) {
      border: none;
    }

    &-header {
      height: 67px;
      border-bottom: 1px solid var(--kui-gray-200);
    }

    &-form-container {
      display: flex;
      justify-content: flex-start;
    }

    &-form {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      margin: 3px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-weight: 700;
      line-height: 120%;
    }

    &-teams-container {
      display: flex;
      align-items: center;
      gap: 8px;

      @include media-breakpoint-down(xl) {
        justify-content: center;
      }
    }

    &-teams-container-position-mobile {
      display: none;

      @include media-breakpoint-down(xl) {
        margin-right: 10px;
        display: block;
        min-width: 25px;
        text-align: left;
      }
    }

    &-teams-container-image {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background-size: cover;
      background-position: center;

      @include media-breakpoint-down(xl) {
        width: 16px;
        height: 16px;
      }
    }

    &-teams-container-team-name {
      @include media-breakpoint-down(xl) {
        display: none;
      }
    }

    &-teams-container-team-name-mobile {
      display: none;

      @include media-breakpoint-down(xl) {
        display: block;
      }
    }

    .green {
      background-color: var(--kui-green-400);
      color: var(--kui-green-600);
    }

    .red {
      background-color: var(--kui-red-400);
      color: var(--kui-red-800);
    }

    .gray {
      background-color: var(--kui-gray-300);
      color: var(--kui-gray-420);
    }

    .highlighted {
      font-weight: 700;
    }

    tr td:first-child,
    th:first-child {
      padding-left: 33px;

      @include media-breakpoint-down(xl) {
        display: none;
      }
    }

    // Becasuse the first column is hidden on mobile

    tr:nth-child(even) td:nth-child(2) {
      @include media-breakpoint-down(xl) {
        background-color: var(--kui-gray-75);
        border-right: 1px solid var(--kui-gray-200);
      }
    }

    // Becasuse the first column is hidden on mobile

    tr:nth-child(odd) td:nth-child(2),
    th:nth-child(2) {
      @include media-breakpoint-down(xl) {
        background-color: var(--kui-white);
        border-right: 1px solid var(--kui-gray-200);
      }
    }

    th:nth-child(2) {
      border-right: none;
    }

    // Becasuse the first column is hidden on mobile, and create sticky column

    tr th:nth-child(2),
    td:nth-child(2) {
      text-align: left;

      @include media-breakpoint-down(xl) {
        text-align: center;
        min-width: 140px;
        position: sticky;
        z-index: 2;
        left: 0;
      }
    }

    // all the columns on mobile, except the first

    tr td:not(:nth-child(2)) {
      @include media-breakpoint-down(xl) {
        min-width: 70px;
        padding: 10px;
      }
    }

    tr {
      height: 64px;
      text-align: center;

      td a {
        color: var(--kui-black);
      }

      @include media-breakpoint-down(xl) {
        height: 40px;
        padding: 12px 24px;
      }
    }

    tr:nth-child(even) {
      background-color: var(--kui-gray-75);
    }
  }
}
