import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, TemplateRef, ViewChild } from '@angular/core';
import { BaseComponent, buildTeamUrl, dataTableConfig, DataTableGeneratorComponent } from '@trendency/kesma-ui';
import { LeagueTable, MatchFormEnum } from '../../definitions';
import { Observable, tap } from 'rxjs';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { AsyncPipe, NgForOf, NgIf, UpperCasePipe } from '@angular/common';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'nso-full-page-league-table',
  templateUrl: './nso-full-page-league-table.component.html',
  styleUrls: ['./nso-full-page-league-table.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, AsyncPipe, NgForOf, RouterLink, UpperCasePipe, DataTableGeneratorComponent],
})
export class NsoFullPageLeagueTableComponent extends BaseComponent<LeagueTable[]> implements AfterViewInit {
  @Input() leagueSlug?: string;
  @ViewChild('position', {
    read: TemplateRef,
    static: false,
  })
  position?: TemplateRef<HTMLElement>;

  @ViewChild('teamForm', {
    read: TemplateRef,
    static: false,
  })
  teamForm?: TemplateRef<HTMLElement>;

  @ViewChild('team', {
    read: TemplateRef,
    static: false,
  })
  team?: TemplateRef<HTMLElement>;

  @ViewChild('goalDifference', {
    read: TemplateRef,
    static: false,
  })
  goalDifference?: TemplateRef<HTMLElement>;

  @ViewChild('nextOpponent', {
    read: TemplateRef,
    static: false,
  })
  nextOpponent?: TemplateRef<HTMLElement>;

  tableConfig: dataTableConfig[] = [];
  teamFormEnum = MatchFormEnum;

  mobileWidthMatched?: boolean;

  mobileWidthMatched$?: Observable<BreakpointState> = this.breakpointObserver.observe('(max-width: 450px)').pipe(
    tap((data) => {
      this.mobileWidthMatched = data?.matches;
      if (this.position) {
        this.setTableData();
      }
    })
  );

  constructor(
    private readonly breakpointObserver: BreakpointObserver,
    private readonly cdr: ChangeDetectorRef
  ) {
    super();
  }

  generateTeamLink(tableItem: LeagueTable): string[] | undefined {
    if (tableItem?.team?.team) {
      return buildTeamUrl({ slug: this.leagueSlug ?? '' }, tableItem?.team?.team);
    } else {
      return ['/', '404'];
    }
  }

  ngAfterViewInit(): void {
    this.createLast5Comeout();
    this.setTableData();
    this.cdr.detectChanges();
  }

  createLast5Comeout(): void {
    this.data?.map((tableItem) => {
      tableItem.teamFormType = [];
      tableItem?.last_5_schedule?.forEach((schedule) => {
        // iterate over the last 5 match
        if (
          (+schedule?.homeScore > +schedule?.awayScore && tableItem?.team?.title === schedule?.homeTeam?.title) || // Check if team won the match
          (+schedule?.homeScore < +schedule?.awayScore && tableItem?.team?.title === schedule?.awayTeam?.title)
        ) {
          tableItem?.teamFormType.push(this.teamFormEnum.WIN);
        } else if (
          (+schedule?.homeScore > +schedule?.awayScore && tableItem?.team?.title === schedule?.awayTeam?.title) || // if lost the match
          (+schedule?.homeScore < +schedule?.awayScore && tableItem?.team?.title === schedule?.homeTeam?.title)
        ) {
          tableItem?.teamFormType.push(this.teamFormEnum.DEFEAT);
        } else if (+schedule?.homeScore === +schedule?.awayScore) {
          // draw
          tableItem?.teamFormType.push(this.teamFormEnum.DRAW);
        }
      });
    });
  }

  setTableData(): void {
    this.tableConfig = [
      {
        hasHeader: true,
        headerTitle: 'Poz.',
        headerColspan: 1,
        columnDataProperty: '',
        headerClass: 'team-league-header',
        customColumnTemplate: this.position,
        columnClass: 'highlighted',
      },
      {
        hasHeader: true,
        headerTitle: 'CSAPAT',
        headerColspan: 1,
        columnDataProperty: 'team.teamName',
        headerClass: 'team-league-header',
        customColumnTemplate: this.team,
        columnClass: 'highlighted',
      },
      {
        hasHeader: true,
        headerTitle: this.mobileWidthMatched ? 'M' : 'Meccsek',
        headerColspan: 1,
        columnDataProperty: 'all',
        headerClass: 'team-league-header',
      },
      {
        hasHeader: true,
        headerTitle: this.mobileWidthMatched ? 'NY' : 'Nyert',
        headerColspan: 1,
        columnDataProperty: 'win',
        headerClass: 'team-league-header',
      },
      {
        hasHeader: true,
        headerTitle: this.mobileWidthMatched ? 'V' : 'Vesztett',
        headerColspan: 1,
        columnDataProperty: 'lose',
        headerClass: 'team-league-header',
      },
      {
        hasHeader: true,
        headerTitle: this.mobileWidthMatched ? 'D' : 'Döntetlen',
        headerColspan: 1,
        columnDataProperty: 'draw',
        headerClass: 'team-league-header',
      },
      {
        hasHeader: true,
        headerTitle: this.mobileWidthMatched ? 'G' : 'Gólok',
        headerColspan: 1,
        columnDataProperty: 'goal',
        headerClass: 'team-league-header',
      },
      {
        hasHeader: true,
        headerTitle: this.mobileWidthMatched ? 'GK' : 'Gólkülönbség',
        headerColspan: 1,
        columnDataProperty: 'goalDifference',
        headerClass: 'team-league-header',
        customColumnTemplate: this.goalDifference,
      },
      {
        hasHeader: true,
        headerTitle: this.mobileWidthMatched ? 'P' : 'Pontok',
        headerColspan: 1,
        columnDataProperty: 'point',
        headerClass: 'team-league-header',
        columnClass: 'highlighted',
      },
      {
        hasHeader: true,
        headerTitle: this.mobileWidthMatched ? 'F' : 'Forma',
        headerColspan: 1,
        columnDataProperty: 'form',
        headerClass: 'team-league-header',
        customColumnTemplate: this.teamForm,
      },
      {
        hasHeader: true,
        headerTitle: this.mobileWidthMatched ? 'KE' : 'Következő ellenfél',
        headerColspan: 1,
        columnDataProperty: 'nextOpponent',
        customColumnTemplate: this.nextOpponent,
        headerClass: 'team-league-header',
      },
    ];
  }
}
