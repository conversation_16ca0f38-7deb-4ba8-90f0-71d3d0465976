<ng-container *ngIf="mobileWidthMatched$ | async"></ng-container>

<!-- Table position -->
<ng-template #position let-rowData="data">
  <ng-container> {{ rowData?.position }}. </ng-container>
</ng-template>

<!-- Team form -->
<ng-template #teamForm let-rowData="data">
  <section class="team-league-form-container">
    <ng-container *ngFor="let formType of rowData?.teamFormType">
      <div
        class="team-league-form"
        [class.green]="formType === teamFormEnum.WIN"
        [class.red]="formType === teamFormEnum.DEFEAT"
        [class.gray]="formType === teamFormEnum.DRAW"
      >
        {{ formType }}
      </div>
    </ng-container>
  </section>
</ng-template>

<!-- Team  -->
<ng-template #team let-rowData="data">
  <a [routerLink]="generateTeamLink(rowData)" class="team-league-teams-container">
    <span class="team-league-teams-container-position-mobile highlighted"> {{ rowData?.position }}.</span>
    <img
      class="team-league-teams-container-image"
      loading="lazy"
      [src]="rowData?.team?.logo || rowData?.team?.team?.logo || 'assets/images/nemzetisport.png'"
      [alt]="rowData?.team?.title || ''"
    />
    <span class="team-league-teams-container-team-name">{{ rowData?.team?.title }}</span>
    <span class="team-league-teams-container-team-name-mobile highlighted">{{ rowData?.team?.short | uppercase }}</span>
  </a>
</ng-template>

<!-- Goal difference -->
<ng-template #goalDifference let-rowData="data">
  <ng-container *ngIf="rowData?.goal_difference > 0">+</ng-container>
  {{ rowData?.goal_difference }}
</ng-template>

<!-- Next Opponent -->

<ng-template #nextOpponent let-rowData="data">
  {{ rowData?.next_schedule?.awayTeam?.title }}
</ng-template>

<kesma-data-table-generator [data]="data" [tableConfig]="tableConfig" [dataTableClass]="'team-league'"></kesma-data-table-generator>
