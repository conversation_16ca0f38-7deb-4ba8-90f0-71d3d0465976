import { AfterViewInit, ChangeDetectionStrategy, Component, inject, Input } from '@angular/core';
import { UtilService } from '@trendency/kesma-core';
import { DOCUMENT, NgIf } from '@angular/common';

const TWITTER_PLATFORM_JS_URL = 'https://platform.twitter.com/widgets.js';
@Component({
  selector: 'nso-twitter-follow',
  templateUrl: './nso-twitter-follow.component.html',
  styleUrls: ['./nso-twitter-follow.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf],
})
export class NsoTwitterFollowComponent implements AfterViewInit {
  private readonly document = inject(DOCUMENT);
  @Input() showTwitterFollow = true;
  @Input() showOtherSocialIcons = true;

  constructor(private readonly utils: UtilService) {}

  ngAfterViewInit(): void {
    if (this.utils.isBrowser() && this.document) {
      const scriptElement = this.document.createElement('script');
      scriptElement.type = 'text/javascript';
      scriptElement.async = true;
      scriptElement.src = TWITTER_PLATFORM_JS_URL;
      this.document.body.appendChild(scriptElement);
    }
  }
}
