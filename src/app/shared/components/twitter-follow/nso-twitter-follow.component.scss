@use 'shared' as *;

:host {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  gap: 10px;
  @include media-breakpoint-down(sm) {
    flex-direction: column;
    gap: 30px;
  }
  .social-icons {
    display: flex;
    flex-direction: row;
    gap: 16px;
    svg {
      width: 20px;
      height: 20px;
      fill: #000;
      @include media-breakpoint-down(sm) {
        width: 30px;
        height: 30px;
      }
      &.facebook {
        fill: #1778f2;
      }
    }
  }

  .twitter-follow {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: nowrap;
    gap: 8px;
    min-width: 400px;
    &:only-child {
      flex: 1;
    }
    @include media-breakpoint-down(sm) {
      flex-wrap: wrap;
      justify-content: center;
    }
  }

  .nso-logo {
    width: 40px;
    height: 40px;
    object-fit: cover;
    border-radius: 40px;
  }
  .meta {
    display: flex;
    flex-direction: column;
    min-width: 140px;
    .title {
      font-size: 16px;
      font-style: normal;
      font-weight: 700;
      line-height: 120%; /* 19.2px */
      letter-spacing: 0.16px;
      color: var(--kui-black);
      .certified-mark {
        vertical-align: middle;
        margin-left: 4px;
      }
    }
    .username {
      color: #828282; //Twitter gray
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 160%; /* 22.4px */
    }
  }
  .follow-button {
    display: flex;
    justify-content: flex-end;
    flex: 1;
    min-width: 250px;
    @include media-breakpoint-down(sm) {
      margin-top: 10px;
      justify-content: center;
    }
    @media screen and (min-width: 900px) {
      transform: scale(1.1);
    }

    a {
      color: var(--Twitter-Blue, #1da1f2);
      border: 2px solid var(--Twitter-Blue, #1da1f2);
      font-size: 15px;
      font-style: normal;
      font-weight: 700;
      line-height: normal;
      padding: 6px 16px;
      align-items: center;
      gap: 10px;
      border-radius: 59px;
    }
  }
}
