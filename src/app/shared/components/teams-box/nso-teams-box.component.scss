@use 'shared' as *;

:host {
  display: block;
  width: 100%;
  font-family: var(--kui-font-primary);
  padding: 0;
  border: 1px solid var(--kui-gray-200);
}

.nso-teamsbox-header {
  display: flex;
  padding: 16px 24px;
  align-items: center;
  gap: 10px;
  background-color: var(--kui-purple-600);

  &-img {
    max-width: 32px;
    max-height: 32px;
  }

  &-title {
    color: var(--kui-white);
    font-family: var(--kui-font-condensed);
    font-size: 24px;
    font-style: normal;
    font-weight: 700;
    line-height: 120%;
    text-transform: uppercase;
  }
}

.nso-teamsbox-body {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  padding: 18px;

  &-item {
    padding: 4px 6px;
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 4px;
    color: var(--kui-black);
    font-weight: 700;

    border-radius: 20px;
    border: 1px solid var(--kui-gray-200);
    background: var(--kui-gray-100);

    &-icon {
      width: 16px;
      height: 16px;
      border-radius: 50%;
      object-fit: cover;
    }
  }
}
