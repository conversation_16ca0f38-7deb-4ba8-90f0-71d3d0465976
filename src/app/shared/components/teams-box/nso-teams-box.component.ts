import { ChangeDetectionStrategy, Component } from '@angular/core';
import { BaseComponent } from '@trendency/kesma-ui';
import { TeamsBoxDefinition, TeamTagDefinition } from '../../definitions/nso-team-box.definitions';
import { RouterLink } from '@angular/router';
import { NgForOf } from '@angular/common';

@Component({
  selector: 'nso-teams-box',
  templateUrl: './nso-teams-box.component.html',
  styleUrls: ['./nso-teams-box.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [RouterLink, NgForOf],
})
export class NsoTeamsBoxComponent extends BaseComponent<TeamsBoxDefinition> {
  get headerIconUrl(): string {
    return this.data?.icon ?? '/assets/images/icons/teams-box-default.svg';
  }

  getTeamLink(team: TeamTagDefinition): Array<string | undefined> | null {
    if (team && team.title) {
      return ['/', 'csapat', team?.slug, this.data?.selectedChampionship?.slug];
    } else {
      return null;
    }
  }
}
