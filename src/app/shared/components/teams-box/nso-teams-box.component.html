<div class="nso-teamsbox-header">
  <img [src]="headerIconUrl" class="nso-teamsbox-header-img" loading="lazy" />
  <div class="nso-teamsbox-header-title">{{ data?.title || 'Csapatok' }}</div>
</div>
<div class="nso-teamsbox-body">
  <a [routerLink]="getTeamLink(team)" class="nso-teamsbox-body-item" *ngFor="let team of data?.teams">
    <img class="nso-teamsbox-body-item-icon" [src]="team?.icon || 'assets/images/nemzetisport.png'" loading="lazy" />
    <div class="nso-teamsbox-body-item-name">{{ team?.title }}</div>
  </a>
</div>
