@use 'shared' as *;

:host {
  display: block;
  width: 100%;

  nso-button {
    text-transform: none !important;
  }
}

.search {
  display: flex;
  flex-direction: column;
  font-weight: 700;
  font-size: 24px;
  line-height: 29px;
  text-transform: uppercase;

  &-title {
    font-family: var(--kui-font-condensed);
  }

  &-bar {
    display: flex;
    flex-direction: row;
    margin: 16px 0;
    width: 100%;
    justify-content: space-between;
    font-size: 16px;

    @include media-breakpoint-down(md) {
      flex-direction: column;
    }

    &-input {
      border: 1px solid var(--kui-gray-200);
      border-radius: 5px;
      height: 51px;
      max-width: 632px;
      width: 100%;
      font-weight: 700;
      padding-left: 32px;
      font-size: 16px;

      @include media-breakpoint-down(md) {
        margin-bottom: 16px;
        max-width: 100%;
      }
    }
  }
}

.btn-text {
  text-transform: none;
  font-family: var(--kui-font-primary);
  font-size: 16px;
}

.icon {
  width: 24px;
  height: 24px;
  margin-right: 12px;
}
