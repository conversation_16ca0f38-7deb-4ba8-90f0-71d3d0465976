import { ChangeDetectionStrategy, Component, EventEmitter, HostListener, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { ReactiveFormsModule, UntypedFormControl } from '@angular/forms';
import { NsoButtonComponent } from '../button/nso-button.component';
import { BaseComponent } from '@trendency/kesma-ui';
import { SearchQueryDefinitons } from '../../definitions';

@Component({
  selector: 'nso-search',
  templateUrl: './nso-search.component.html',
  styleUrls: ['./nso-search.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [ReactiveFormsModule, NsoButtonComponent],
})
export class NsoSearchComponent extends BaseComponent<SearchQueryDefinitons> implements OnInit, OnChanges {
  @HostListener('document:keydown', ['$event'])
  public onKeyDown(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      this.onSearch();
    }
  }
  @Input() placeholder: string = 'Keresés';
  @Output() sendSearchKey: EventEmitter<string> = new EventEmitter<string>();

  searchKey = new UntypedFormControl('');

  override ngOnInit(): void {
    this.searchKey.setValue(this.data?.searchQuery);
  }

  ngOnChanges(simpleChanges: SimpleChanges): void {
    if (simpleChanges['data'] && simpleChanges['data'].previousValue !== simpleChanges['data'].currentValue) {
      this.searchKey.setValue(this.data?.searchQuery);
    }
  }

  onSearch(): void {
    this.sendSearchKey.emit(this.searchKey.value);
  }
}
