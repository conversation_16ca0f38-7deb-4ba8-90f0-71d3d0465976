import { ChangeDetectionStrategy, Component, Input, OnInit } from '@angular/core';
import { QuizAnswer, QuizComponent, QuizRating } from '@trendency/kesma-ui';
import { NgForOf, NgIf } from '@angular/common';
import { NsoButtonComponent } from '../button/nso-button.component';

type NsoQuizRating = QuizRating & {
  image: {
    thumbnailUrl?: string;
  };
};

@Component({
  selector: 'nso-quiz',
  templateUrl: './nso-quiz.component.html',
  styleUrls: ['./../../../../../node_modules/@trendency/kesma-ui/src/lib/components/quiz/quiz.component.scss', './nso-quiz.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NgForOf, NsoButtonComponent],
})
export class NsoQuizComponent extends QuizComponent implements OnInit {
  currentQuestionIndex = 0;
  quizHasBeenStarted = false;
  selectedAnswer?: QuizAnswer;
  @Input() desktopWidth = 12;

  get progressLineWidth(): number {
    if (!this.data?.questions.length) return 10;

    return (100 / this.data.questions.length) * (this.currentQuestionIndex + 1) || 10;
  }

  get ratingImage(): string {
    return (
      this.rating?.thumbnailUrl || (typeof this.rating?.image === 'string' ? this.rating?.image : (this.rating as NsoQuizRating)?.image?.thumbnailUrl || '')
    );
  }

  override get placeholderImage(): string {
    return 'assets/images/nemzetisport.png';
  }

  get isNextButtonVisible(): boolean {
    return !!this.data?.questions.length && this.data.questions.length > this.currentQuestionIndex + 1;
  }

  get narrowView(): boolean {
    return this.desktopWidth < 5;
  }

  override ngOnInit(): void {
    super.ngOnInit();
  }

  onStartQuiz(): void {
    this.quizHasBeenStarted = true;
  }

  onPickAnswer(answer: QuizAnswer): void {
    this.selectedAnswer = answer;
  }

  onGetNextQuestion(): void {
    if ((this.data?.questions.length && this.data.questions.length <= this.currentQuestionIndex + 1) || !this.selectedAnswer) return;

    this.selectedAnswer = undefined;
    this.currentQuestionIndex++;
    this.currentQuestion = this.data?.questions[this.currentQuestionIndex];
  }
}
