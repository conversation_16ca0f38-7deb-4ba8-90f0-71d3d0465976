<div class="quiz-title">{{ data?.title }}</div>

<ng-container *ngIf="!quizHasBeenStarted; else quizTemplate">
  <nso-button [class.narrow-view]="narrowView" (click)="onStartQuiz()">A kezdéshez kattintson ide</nso-button>
</ng-container>

<ng-template #quizTemplate>
  <div class="quiz-step">
    <div class="quiz-step-progress">
      <div class="quiz-step-progress-line" [style.width.%]="progressLineWidth"></div>
    </div>
    <span class="quiz-step-pager">{{ currentQuestionIndex + 1 }} / {{ data?.questions?.length }}</span>
  </div>

  <div class="quiz-question">
    <ng-container *ngIf="!rating; else ratingImg">
      <img loading="lazy" class="quiz-question-image" [src]="currentQuestion?.image || placeholderImage" alt="K<PERSON><PERSON>z kérd<PERSON><PERSON> ábrázo<PERSON>ó kép." />
    </ng-container>
    <ng-template #ratingImg>
      <img loading="lazy" class="quiz-question-image" [src]="ratingImage || placeholderImage" alt="Kvíz kérdést ábrázoló kép." />
    </ng-template>

    <h5 class="quiz-question-text">{{ currentQuestion?.title }}</h5>

    <div class="answer-list">
      <ng-container *ngFor="let answer of currentQuestion?.answers; let answerIndex = index">
        <input
          class="radio-input"
          type="radio"
          (change)="onSelectAnswer(currentQuestionIndex, answerIndex); onPickAnswer(answer)"
          [name]="'answer_' + currentQuestion?.id"
          [id]="'answer_' + currentQuestion?.id + '_' + answerIndex"
          [disabled]="givenAnswers[currentQuestionIndex] !== undefined"
        />
        <label
          class="radio-label"
          [for]="'answer_' + currentQuestion?.id + '_' + answerIndex"
          [class.wrong]="!answer.isCorrect && givenAnswers[currentQuestionIndex] !== undefined && givenAnswers[currentQuestionIndex] === answerIndex"
          [class.correct]="answer.isCorrect && givenAnswers[currentQuestionIndex] !== undefined && givenAnswers[currentQuestionIndex] === answerIndex"
          [class.hidden-original-circle]="radioLabel.classList.contains('correct') || radioLabel.classList.contains('wrong')"
          #radioLabel
        >
          {{ answer.title }}
          <span class="extra-label correct">
            <i class="extra-label-icon icon icon-check-circle"></i>
          </span>
          <span class="extra-label wrong">
            <i class="extra-label-icon icon icon-x-circle"></i>
          </span>
        </label>

        <div class="explanation" *ngIf="selectedAnswer?.isCorrect && answer.isCorrect && answer?.explanation">
          <span>Magyarázat: </span>{{ answer.explanation }}
        </div>
      </ng-container>
    </div>
  </div>

  <nso-button (click)="onGetNextQuestion()" [disabled]="!selectedAnswer" *ngIf="isNextButtonVisible">Következő</nso-button>
</ng-template>

<div class="quiz-question" *ngIf="rating">
  <div class="quiz-result">
    <div class="quiz-result-status">{{ correctCount }}/{{ data?.questions?.length }}</div>
    <div class="quiz-result-text">{{ rating.text }}</div>
    <div class="quiz-result-share">
      <i class="icon icon-share-fb"></i>
      <a (click)="onFBShareClick()"> Facebook megosztás </a>
    </div>
  </div>
</div>
