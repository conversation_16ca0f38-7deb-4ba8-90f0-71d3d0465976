@use 'shared' as *;

:host {
  display: block;
  background: var(--kui-gray-550);
  padding: 16px 24px;

  .quiz {
    &-title {
      font-weight: 700;
      font-size: 12px;
      color: var(--kui-white);
      line-height: 14.06px;
      text-align: center;
      margin-bottom: 16.5px;
    }

    &-step {
      display: flex;
      gap: 8px;
      align-items: center;
      color: var(--kui-white);

      &-progress {
        width: 100%;
        background: var(--kui-white);

        &-line {
          background: var(--kui-red-400);
          height: 2px;
        }
      }

      &-pager {
        flex: 0 0 auto;
        font-weight: 700;
        font-size: 16px;
        line-height: 19.2px;
        letter-spacing: 0.01em;
      }
    }

    &-question {
      &-image {
        position: static;
        aspect-ratio: 16/9;
        object-fit: cover;
        margin-bottom: 16px;
      }

      &-text {
        position: static;
        font-weight: 400;
        font-size: 16px;
        line-height: 25.6px;
        padding: 0;
        margin: 0;
      }

      .answer-list {
        padding: 17.5px 0 25.5px;
        background: none;
        color: var(--kui-white);
        display: flex;
        flex-direction: column;
        gap: 11px;
        margin: 0;

        .radio-label {
          border-bottom: none;
          padding: 0 0 0 30px;
          cursor: pointer;
          width: fit-content;

          &.correct {
            color: var(--kui-green-400);
          }

          &.wrong {
            color: var(--kui-red-200);
          }

          &.hidden-original-circle {
            background-color: transparent;

            &:before {
              content: none;
            }

            .extra-label {
              left: 0;
              right: auto;

              .icon {
                width: 20px;
                height: 20px;
              }
            }
          }

          &:before {
            width: 20px;
            height: 20px;
            background: none;
            border: 2px solid var(--kui-white);
            left: 0;
          }

          &:after {
            display: none;
          }
        }

        .explanation {
          font-weight: 400;
          font-size: 16px;
          line-height: 25.6px;
          color: var(--kui-gray-200);
          text-transform: none;
          padding-left: 30px;

          span {
            color: var(--kui-white);
          }
        }
      }
    }

    &-result {
      background-color: transparent;

      &-share {
        background-color: var(--kui-blue-700);
      }
    }
  }
}

nso-button.narrow-view::ng-deep {
  @include media-breakpoint-up(md) {
    .button {
      height: unset;
      padding: 5px 10px;
    }
  }
}
