import { Component, Input, ChangeDetectionStrategy } from '@angular/core';
import { TabType } from '../../definitions';
import { ChampionshipDetails } from '../../../feature/championship/api/championship.definitions';
import { NgForOf, NgIf } from '@angular/common';
import { RouterLink, RouterLinkActive } from '@angular/router';

@Component({
  selector: 'app-sub-page-tab',
  templateUrl: './sub-page-tab.component.html',
  styleUrls: ['./sub-page-tab.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NgForOf, RouterLinkActive, RouterLink],
})
export class SubPageTabComponent {
  @Input() set pageTabs(tabs: TabType[]) {
    this._pageTab = tabs;
  }
  get pageTabs(): TabType[] {
    return this._pageTab as TabType[];
  }
  @Input() championshipType?: ChampionshipDetails | null;
  @Input() isChampionshipPage = false;
  @Input() pageType: string;

  private _pageTab: TabType[];
}
