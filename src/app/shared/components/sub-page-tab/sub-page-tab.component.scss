@use 'shared' as *;

:host {
  display: block;
}

.sub-page-tab-header {
  display: flex;
  flex-direction: row;
  gap: 40px;
  align-items: center;

  @include media-breakpoint-down(md) {
    gap: 24px;
  }

  &-championship-detail {
    display: flex;
    gap: 10px;
    align-items: center;
    padding: 10px;

    @include media-breakpoint-down(md) {
      min-width: 160px;
    }
  }

  &-championship-logo {
    width: 32px;
    height: 32px;
  }

  &-championship-name {
    font-family: var(--kui-font-primary);
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: 120%;
    letter-spacing: 0.16px;
    white-space: nowrap;
  }

  &-tabs {
    display: flex;
    gap: 20px;
    justify-content: center;
    align-items: center;
    height: 100%;
    padding-top: 20px;

    @include media-breakpoint-down(md) {
      justify-content: flex-start;
      overflow: auto;
      padding: 10px;
    }
  }

  &-tab {
    font-family: var(--kui-font-primary);
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 160%;
    color: var(--kui-gray-550);
    white-space: nowrap;
    height: 100%;
    padding-bottom: 10px;
  }

  &-match {
    border-bottom: 4px solid var(--kui-red-400);
    font-weight: 700;
  }

  &-championship {
    font-weight: 700;
  }
}

.championship {
  padding-left: 20px;
  @include media-breakpoint-up(xl) {
    border-left: 1px solid var(--kui-gray-325);
  }
}
