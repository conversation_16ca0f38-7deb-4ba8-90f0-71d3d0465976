<section class="sub-page-tab-header">
  <section class="sub-page-tab-header-championship-detail" *ngIf="isChampionshipPage">
    <img
      class="sub-page-tab-header-championship-logo"
      loading="lazy"
      [src]="championshipType?.logo ?? '/assets/images/nemzetisport.png'"
      [alt]="championshipType?.title ?? ''"
    />
    <span class="sub-page-tab-header-championship-name">{{ championshipType?.title ?? '-' }}</span>
  </section>
  <section [class.championship]="isChampionshipPage" class="sub-page-tab-header-tabs">
    <ng-container *ngFor="let tab of pageTabs">
      <a
        class="sub-page-tab-header-tab"
        [routerLinkActiveOptions]="{ exact: true }"
        [routerLinkActive]="'sub-page-tab-header-' + pageType"
        [routerLink]="tab?.tabSlug"
        >{{ tab?.tabName }}
      </a>
    </ng-container>
  </section>
</section>
