@use 'shared' as *;

$olympics-blue-1: #022366;

:host {
  display: block;
  width: 100%;
  position: relative;
  z-index: 1;

  .menu {
    background-color: $olympics-blue-1;
    margin-bottom: 50px;
    padding: 0 15px;

    &-content {
      @extend %flex-center;
      max-width: $header-max-width;
      height: 84px;
      justify-content: space-between;
      margin: 0 auto;
    }

    &-box {
      @extend %flex-center;
      position: relative;
      max-width: 235px;
      width: 100%;
      height: 24px;
      gap: 20px;

      @include media-breakpoint-down(md) {
        width: auto;
      }

      &.center {
        justify-content: center;

        @include media-breakpoint-down(md) {
          flex-shrink: 0;
        }
      }

      &.flex-end {
        justify-content: flex-end;

        @include media-breakpoint-down(md) {
          .menu-link {
            max-width: 90px;
            text-align: right;
          }
        }
      }

      @include media-breakpoint-down(md) {
        gap: 12px;
      }
    }

    &-right {
      display: flex;
      flex-direction: row;
      column-gap: 20px;
    }

    &-link {
      @extend %flex-center;
      color: var(--kui-white);
      font-size: 16px;
      font-weight: 700;
      line-height: 120%;
      letter-spacing: 0.16px;
      height: 100%;

      &.only-left-border {
        border-left: 1px solid rgba(255, 255, 255, 0.5);
        padding-left: 20px;

        @include media-breakpoint-down(md) {
          padding-left: 12px;
        }
      }
    }

    &-toggle {
      @extend %flex-center;
      @extend %absolute-center;
      bottom: -66px;
      background-color: $olympics-blue-1;
      padding: 10px;
      border-radius: 0 0 200px 200px;
      justify-content: center;
      width: 52px;
      height: 52px;
      cursor: pointer;

      @include media-breakpoint-down(md) {
        padding: 8px;
        width: 48px;
        height: 48px;
      }

      &-icon {
        transition: transform 400ms ease-in-out;
        background-image: url(/assets/images/olympics/olympics-arrow-down.svg);
        width: 22px;
        height: 26px;
        transform: rotate(180deg);

        &.collapsed {
          transform: rotate(0deg);
        }
      }
    }
  }

  .collapsible-content {
    width: $header-max-width;
    max-width: calc(100% - 30px);
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    gap: 40px;

    &.hidden {
      display: none;
    }

    @include media-breakpoint-down(sm) {
      gap: 24px;
    }
  }

  ::ng-deep {
    kesma-eb-table-header-with-url {
      background-color: #009ce0;

      &.medal-header {
        cursor: pointer;
        margin-bottom: 26px; // layout margin-top 30px + 26px

        @include media-breakpoint-down(md) {
          flex-direction: row;
          align-items: center;

          margin-bottom: 25px; // layout margin-top 15px + 25px

          &.without-margin-on-mobile {
            margin-bottom: 0;
          }

          .table-header-link {
            width: auto;
          }
        }
      }
    }

    nso-eb-superfeed.superfeed {
      .superfeed-article-date {
        background-color: $olympics-blue-1;
        border-color: $olympics-blue-1;
      }

      .superfeed-article-title {
        color: $olympics-blue-1;
        font-family: var(--kui-font-primary);
      }
    }
  }
}

%flex-center {
  display: flex;
  align-items: center;
}

%absolute-center {
  position: absolute;
  left: 50%;
  transform: translate(-50%);
}

.olympics-logo-text {
  color: var(--kui-white);

  @include media-breakpoint-down(md) {
    display: flex;
    flex-direction: column;
    align-items: center;
    font-size: 26px;
  }
}
