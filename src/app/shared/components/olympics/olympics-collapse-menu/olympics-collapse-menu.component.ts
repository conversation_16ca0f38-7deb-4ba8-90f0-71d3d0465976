import { ChangeDetectionStrategy, Component, OnDestroy, OnInit } from '@angular/core';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { filter, map, startWith } from 'rxjs/operators';
import { Observable, Subject } from 'rxjs';
import { ArticleCard, backendDateToDate, EbTableHeaderWithUrlComponent } from '@trendency/kesma-ui';
import { NavigationEnd, Router, RouterLink } from '@angular/router';
import { StorageService } from '@trendency/kesma-core';
import { ApiService } from '../../../services';
import { AsyncPipe, NgIf } from '@angular/common';
import { NsoEbSuperfeedComponent } from '../../eb/nso-eb-superfeed/nso-eb-superfeed.component';

const MOBILE_BREAKPOINT = '(min-width: 992px)';
const NEWS_FEED_SLUG = 'olimpia-hirfolyam';

@Component({
  selector: 'app-olympics-collapse-menu',
  templateUrl: 'olympics-collapse-menu.component.html',
  styleUrls: ['olympics-collapse-menu.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [RouterLink, AsyncPipe, NgIf, NsoEbSuperfeedComponent, EbTableHeaderWithUrlComponent],
})
export class OlympicsCollapseMenuComponent implements OnInit, OnDestroy {
  #isCollapsed = false;

  readonly #destroy$ = new Subject<void>();

  isMobile$: Observable<boolean> = this.breakpointObserver.observe([MOBILE_BREAKPOINT]).pipe(map((state: BreakpointState) => !state.matches));

  superFeed$: Observable<ArticleCard[]> = this.apiService.getDossier(NEWS_FEED_SLUG, 0, 12).pipe(
    map(({ data }) => data as ArticleCard[]),
    map((data) => {
      return data.map((article) => ({ ...article, publishDate: backendDateToDate(article.publishDate as string) as Date }));
    })
  );

  shouldShowCompositeLogo$ = this.router.events.pipe(
    filter((e) => e instanceof NavigationEnd),
    startWith(null),
    map(() => ['/rovat/parizs2024', '/hirfolyam/olimpia-hirfolyam'].includes(this.router.url) || this.router.url.startsWith('/parizs2024/'))
  );

  constructor(
    private readonly breakpointObserver: BreakpointObserver,
    private readonly apiService: ApiService,
    private readonly router: Router,
    private readonly storageService: StorageService
  ) {}

  ngOnInit(): void {
    // Only temporary, we probably have to go back to the original
    const isCollapsedFromCookie = !!this.storageService.getCookie('isOlympicsMenuCollapsed') || false;

    if (isCollapsedFromCookie) {
      this.#isCollapsed = true;
      // core package's setCookie function use utc format, that's why we have to add two more hours plus to get a one full day expiration
    }
  }

  ngOnDestroy(): void {
    this.#destroy$.next();
    this.#destroy$.complete();
  }

  get isCollapsed(): boolean {
    return this.#isCollapsed;
  }

  toggle(): void {
    this.#isCollapsed = !this.#isCollapsed;
    if (this.#isCollapsed) {
      this.storageService.setCookie('isOlympicsMenuCollapsed', true, 93600);
    } else {
      this.storageService.setCookie('isOlympicsMenuCollapsed', false, -1);
    }
  }
}
