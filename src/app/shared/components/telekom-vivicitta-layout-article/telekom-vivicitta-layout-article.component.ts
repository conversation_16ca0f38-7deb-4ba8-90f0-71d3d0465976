import { ChangeDetectionStrategy, Component, inject, Signal } from '@angular/core';
import { map, of, switchMap } from 'rxjs';
import { ArticleCard } from '@trendency/kesma-ui';
import { ActivatedRoute, Params } from '@angular/router';
import { toSignal } from '@angular/core/rxjs-interop';
import { NsoArticleCardComponent } from '../article-card/nso-article-card.component';
import { TelekomVivicittaService } from '../../services';
import { mapVivicittaArticleToArticleCard } from '../../utils';
import { ArticleCardType } from '../../definitions';

@Component({
  selector: 'app-telekom-vivicitta-layout-article',
  templateUrl: './telekom-vivicitta-layout-article.component.html',
  styleUrls: ['./telekom-vivicitta-layout-article.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NsoArticleCardComponent],
})
export class TelekomVivicittaLayoutArticleComponent {
  readonly #route: ActivatedRoute = inject(ActivatedRoute);
  readonly #telekomVivicittaService: TelekomVivicittaService = inject(TelekomVivicittaService);

  readonly article: Signal<ArticleCard | undefined> = toSignal<ArticleCard | undefined>(
    this.#route.queryParams.pipe(
      switchMap((params: Params) => {
        const slug = params['vivicitta2025'];
        if (slug) {
          return this.#telekomVivicittaService.getArticle(slug).pipe(map(mapVivicittaArticleToArticleCard));
        }
        return of(undefined);
      })
    )
  );

  readonly ArticleCardType = ArticleCardType;
}
