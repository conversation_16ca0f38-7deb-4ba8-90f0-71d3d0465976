<section class="minute-by-minute">
  <h4 class="minute-by-minute-title">{{ data?.timelineTitle }}</h4>
  <div class="minute-by-minute-club top">
    <img *ngIf="data?.homeTeamImg" class="minute-by-minute-image" [src]="data?.homeTeamImg" [alt]="data?.homeTeamName || ''" loading="lazy" />
    <p class="minute-by-minute-name">{{ data?.homeTeamName }}</p>
  </div>
  <div class="timeline">
    <span class="timeline-left-pos">{{ data?.posLeft }}</span>

    <div class="timeline-time">
      <div class="timeline-time-item" *ngFor="let item of data?.timeLineData; let index = index">
        <div class="timeline-minute-content" [ngStyle]="{ left: item.percent }">
          <span class="timeline-minute">{{ item.time }}'</span>
          <div class="timeline-item {{ item.pos }}">
            <img
              *ngIf="item.icon"
              (mouseenter)="onChangeActiveTooltip(true, index)"
              (mouseleave)="onChangeActiveTooltip(false, index)"
              class="timeline-icon"
              [src]="item.icon"
              loading="lazy"
            />
            <div *ngIf="(isTooltipVisible$ | async) && currentIndex === index && item.playerName" class="tooltip">
              <div class="tooltip-{{ item.pos }}">
                <p>
                  <span *ngIf="item?.onSubstitutedPlayerName">Le: </span>
                  <span> {{ item?.playerName }}</span>
                </p>
                <p *ngIf="item?.onSubstitutedPlayerName" class="tooltip-substituted-player">Be: {{ item.onSubstitutedPlayerName }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <span class="timeline-right-pos">{{ data?.posRight }}</span>
  </div>
  <div class="minute-by-minute-club bottom">
    <img *ngIf="data?.awaTeamImg" class="minute-by-minute-image" [src]="data?.awaTeamImg" [alt]="data?.awayTeamName || ''" loading="lazy" />
    <p class="minute-by-minute-name">{{ data?.awayTeamName }}</p>
  </div>
</section>

<section class="minute-by-minute mobile">
  <div class="mobile-content">
    <h4 class="mobile-content-title">{{ data?.timelineTitle }}</h4>
    <div class="mobile-content-wrapper">
      <div class="timeline">
        <div class="minute-by-minute-club mobile-content-left-team">
          <img *ngIf="data?.awaTeamImg" class="minute-by-minute-image" [src]="data?.awaTeamImg" [alt]="data?.homeTeamName || ''" loading="lazy" />
          <p class="minute-by-minute-name">{{ data?.homeTeamName }}</p>
        </div>
        <div class="minute-by-minute-club mobile-content-right-team">
          <img *ngIf="data?.awaTeamImg" class="minute-by-minute-image" [src]="data?.awaTeamImg" [alt]="data?.awayTeamName || ''" loading="lazy" />
          <p class="minute-by-minute-name">{{ data?.awayTeamName }}</p>
        </div>
        <span class="timeline-top-pos">
          {{ data?.posLeft }}
        </span>
        <div class="timeline-time">
          <div class="timeline-time-wrapper" *ngFor="let item of data?.timeLineData; let index = index">
            <div class="timeline-minute-content" [ngStyle]="{ top: item.percent }">
              <span class="timeline-minute">{{ item.time }}'</span>
              <div class="timeline-item {{ item.pos }}">
                <img
                  *ngIf="item.icon"
                  class="timeline-icon"
                  [src]="item.icon"
                  (touchstart)="onChangeActiveTooltip(true, index)"
                  (touchend)="onChangeActiveTooltip(false, index)"
                  loading="lazy"
                />
                <span class="timeline-player">{{ item.player }}</span>
                <div *ngIf="(isTooltipVisible$ | async) && currentIndex === index && item.playerName" class="tooltip">
                  <div class="tooltip-{{ item.pos }}">
                    <p>
                      <span *ngIf="item?.onSubstitutedPlayerName">Le: </span>
                      <span> {{ item?.playerName }}</span>
                    </p>
                    <p *ngIf="item?.onSubstitutedPlayerName" class="tooltip-substituted-player">Be: {{ item?.onSubstitutedPlayerName }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <span class="timeline-bottom-pos">{{ data?.posRight }}</span>
      </div>
    </div>
  </div>
</section>
