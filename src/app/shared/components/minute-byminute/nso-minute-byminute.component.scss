@use 'shared' as *;

:host {
  display: block;
  width: 100%;

  &.large {
    overflow-x: auto;
    padding-bottom: 5px;
    @include media-breakpoint-up(sm) {
      .minute-by-minute {
        min-width: 1125px;
      }
    }
    @include media-breakpoint-down(sm) {
      .mobile-content-wrapper,
      .timeline {
        min-height: 1125px;
      }
    }
  }
  &.x-large {
    @include media-breakpoint-up(sm) {
      .minute-by-minute {
        min-width: 2700px;
      }
    }
    @include media-breakpoint-down(sm) {
      .mobile-content-wrapper,
      .timeline {
        min-height: 3000px;
      }
    }
  }
}

.minute-by-minute {
  font-size: 16px;
  color: var(--kui-gray-550);

  @include media-breakpoint-down(sm) {
    display: none;
  }

  &.mobile {
    display: block;
    width: 100%;

    @include media-breakpoint-up(sm) {
      display: none;
    }
  }

  &-title {
    font-weight: bold;
    font-size: 12px;
    line-height: 14px;
    color: var(--kui-gray-550);
    margin-bottom: 24px;
  }
  &-club {
    display: flex;
    align-items: center;
    &.top {
      margin-bottom: 54px;
    }
    &.bottom {
      margin-top: 54px;
    }
  }
  &-image {
    margin-right: 8px;
    width: 42px;
    height: 42px;
    border-radius: 42px;
    border: 1px solid var(--kui-gray-150);
    object-fit: cover;
  }
  &-name {
    font-weight: bold;
    font-size: 16px;
    line-height: 120%;
    color: var(--kui-gray-550);
  }

  .timeline {
    position: relative;
    background: var(--kui-gray-100);
    width: 100%;
    height: 38px;
    border-radius: 50px;
    padding: 6px 16px;

    &-left-pos {
      font-weight: bold;
      line-height: 120%;
      position: absolute;
      left: 9.5px;
      top: 9.5px;
    }
    &-time {
      position: absolute;
      width: calc(100% - 140px);
      left: 50px;
      height: 100%;
      top: 9.5px;
    }
    &-time-item {
      position: absolute;
      width: 100%;
    }
    &-minute-content {
      position: absolute;
    }
    &-right-pos {
      font-weight: bold;
      line-height: 120%;
      position: absolute;
      right: 9.5px;
      top: 9.5px;
    }
    &-icon {
      max-width: 24px;
      max-height: 24px;
    }
    &-item {
      position: relative;
    }
    .awayTeamName {
      position: absolute;
      top: 35px;
    }
    .homeTeamName {
      position: absolute;
      bottom: 35px;
    }

    .tooltip {
      p {
        line-height: 1.25;
        font-size: 12px;
        width: 70px;
      }

      &-awayTeamName {
        position: absolute;
        top: 35px;
        left: -30px;
        z-index: 2;
        background: var(--kui-gray-150);
        border-radius: 10px;
        padding: 9px;

        &:after {
          content: '';
          position: absolute;
          top: -10px;
          left: 50%;
          margin-left: -5px;
          border-width: 5px;
          border-style: solid;
          border-color: var(--kui-gray-150) transparent transparent transparent;
          transform: rotate(180deg);
        }
      }

      &-homeTeamName {
        position: absolute;
        top: -60px;
        left: -30px;
        z-index: 2;
        background: var(--kui-gray-150);
        border-radius: 10px;
        padding: 9px;

        &:after {
          content: '';
          position: absolute;
          top: 100%;
          left: 50%;
          margin-left: -5px;
          border-width: 5px;
          border-style: solid;
          border-color: var(--kui-gray-150) transparent transparent transparent;
        }
      }

      &-substituted-player {
        font-weight: bold;
      }
    }
  }
}

.mobile-content {
  .homeTeamName {
    position: absolute;
    right: 60px;
    top: 9.5px;

    &.item {
      flex-direction: row-reverse;
      display: flex;
    }
  }
  .awayTeamName {
    position: absolute;
    left: 60px;
    top: 0 !important;
  }
  &-title {
    font-size: 16px;
  }
  &-wrapper {
    position: relative;
  }
  &-left-team {
    position: absolute;
    right: 115px;
    top: 35px;
  }
  &-right-team {
    position: absolute;
    left: 80px;
    top: 35px;
  }
  .timeline {
    width: 62px;
    height: 630px;
    margin: 0 auto;

    &-time {
      height: calc(100% - 200px);
      top: 100px;
      left: initial;
    }

    &-time-wrapper {
      height: 100%;
      .team2 {
        top: 0;
      }
    }
    &-minute-content {
      position: absolute;
    }
    &-item {
      display: flex;
      align-items: center;
    }
    &-player {
      margin: 0 12px;
    }
    &-top-pos {
      margin-top: 37px;
      display: block;
      font-weight: 700;
    }
    &-bottom-pos {
      position: absolute;
      bottom: 37px;
      text-align: center;
      display: block;
      font-weight: 700;
    }

    .tooltip {
      p {
        line-height: 1.25;
        font-size: 12px;
        width: 70px;
      }

      &-substituted-player {
        font-weight: bold;
      }

      &-box {
        position: absolute;
        top: -70px;
        left: -25px;
        z-index: 2;
        background: var(--kui-gray-150);
        border-radius: 10px;
        line-height: 1.25;
        font-size: 12px;
        padding: 9px;

        &:after {
          content: '';
          position: absolute;
          top: 100%;
          left: 50%;
          margin-left: -5px;
          border-width: 5px;
          border-style: solid;
          border-color: var(--kui-gray-150) transparent transparent transparent;
        }
      }
    }
  }
}
