import { ChangeDetectionStrategy, Component, HostBinding, OnChanges, OnInit, SimpleChanges } from '@angular/core';
import { BaseComponent } from '@trendency/kesma-ui';
import { BehaviorSubject } from 'rxjs';
import { MinuteByminuteDefinitions } from '../../definitions';
import { Async<PERSON>ip<PERSON>, <PERSON><PERSON>orOf, NgI<PERSON>, Ng<PERSON>tyle } from '@angular/common';

@Component({
  selector: 'nso-minute-byminute',
  templateUrl: './nso-minute-byminute.component.html',
  styleUrls: ['./nso-minute-byminute.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, <PERSON>ForOf, NgStyle, AsyncPipe],
})
export class NsoMinuteByminuteComponent extends BaseComponent<MinuteByminuteDefinitions> implements OnChanges, OnInit {
  isTooltipVisible$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  currentIndex?: number;
  @HostBinding('class.large') isLarge = false;
  @HostBinding('class.x-large') isXLarge = false;

  constructor() {
    super();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (!changes['data'].isFirstChange()) {
      this.handleTimelineSize();
    }
  }

  override ngOnInit(): void {
    super.ngOnInit();
    this.handleTimelineSize();
  }

  onChangeActiveTooltip(mouseIn: boolean, index: number): void {
    this.currentIndex = index;
    this.isTooltipVisible$.next(mouseIn);
  }

  /**
   * If the events are too close to each other (like less than 3 minutes) the needed space needs to be expanded.
   * We calculate what is the minimum difference between two events through the timeline and based on that we apply classes that will enforce
   * a given size.
   */
  handleTimelineSize(): void {
    const timeline = this.data?.timeLineData;
    const minutes = timeline?.map((t) => t.time);
    const distinctMinutes = [...new Set(minutes)];
    const diffMinutes = this.findMinimumDifference(distinctMinutes) || 1;

    if (diffMinutes <= 3) {
      this.isLarge = true;
    }
    if (diffMinutes <= 1) {
      this.isXLarge = true;
    }
  }

  findMinimumDifference(arr: number[]): number | undefined {
    if (arr.length < 2) {
      return undefined; // Not enough elements to find a difference
    }

    arr.sort((a, b) => a - b); // Sort the array in ascending order
    let minDifference = Infinity;

    for (let i = 1; i < arr.length; i++) {
      const difference = arr[i] - arr[i - 1];
      if (difference < minDifference) {
        minDifference = difference;
      }
    }

    return minDifference;
  }
}
