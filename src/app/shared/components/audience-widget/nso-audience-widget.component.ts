import { ChangeDetectionStrategy, Component, computed, input } from '@angular/core';

interface AudienceViewTier {
  tier: number;
  threshold: number;
}

const TIERS: AudienceViewTier[] = [
  { tier: 1, threshold: 5_000 },
  { tier: 2, threshold: 10_000 },
  { tier: 3, threshold: 15_000 },
  { tier: 4, threshold: 20_000 },
  { tier: 5, threshold: 25_000 },
  { tier: 6, threshold: 30_000 },
  { tier: 7, threshold: 35_000 },
  { tier: 8, threshold: 40_000 },
  { tier: 9, threshold: 45_000 },
  { tier: 10, threshold: 50_000 },
];
@Component({
  selector: 'nso-audience-widget',
  templateUrl: './nso-audience-widget.component.html',
  styleUrls: ['./nso-audience-widget.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class NsoAudienceWidgetComponent {
  audienceTier = computed(() => {
    const views = this.views();
    if (views === undefined) {
      return 0;
    }
    return this.calculateTier(views);
  });
  audienceNumberText = computed(() => {
    const views = this.views();
    if (views === undefined) {
      return '0';
    }
    return views > 999 ? `${Math.floor(views / 1000)} ${(views % 1000).toString().padStart(3, '0')}` : views.toString();
  });
  views = input<number>();

  private calculateTier(views: number): number {
    const passedTiers = Object.values(TIERS).filter((tier) => {
      return tier.threshold <= views;
    });
    if (passedTiers.length === 0) {
      return 0;
    }
    return passedTiers[passedTiers.length - 1].tier;
  }
}
