@use 'shared' as *;

.audience {
  padding: 16px;
  background-color: var(--kui-gray-550);
  width: 100%;
  text-align: center;

  &-title {
    font-family: var(--kui-font-condensed);
    font-weight: 700;
    font-size: 22px;
    line-height: 28px;
    color: var(--kui-white);
    text-transform: uppercase;
  }

  &-header {
    margin-bottom: 24px;
    text-align: start;
  }

  &-bottom-container {
    font-family: var(--kui-font-condensed);
    font-weight: 700;
    font-size: 24px;
    line-height: 28px;
    margin-bottom: 10px;
    color: var(--kui-white);
    text-transform: uppercase;
    text-align: center;
    margin-top: 45px;
    display: flex;
    justify-content: center;
    align-items: center;

    @include media-breakpoint-down(md) {
      margin-top: 30px;
    }
  }

  &-thanks {
    @include media-breakpoint-down(md) {
      width: 139px;
      height: 49px;
      background-color: var(--kui-red-400);
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  &-content {
    width: 100%;
    height: 189px;
    display: flex;
    justify-content: center;
    position: relative;
    @include media-breakpoint-down(md) {
      height: 50px;
    }
  }

  &-stadion {
    display: block;
    @include media-breakpoint-down(md) {
      display: none;
    }
    g {
      opacity: 0.15;
      path {
        fill: var(--kui-white);
      }
    }
  }

  &-number {
    font-family: var(--kui-font-condensed);
    color: var(--kui-red);
    font-size: 40px;
    text-align: center;
    font-weight: 700;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    @include media-breakpoint-down(md) {
      color: var(--kui-white);
    }
  }

  .icon-user {
    width: 24px;
    height: 24px;
    margin-left: 5px;
    display: flex;
    align-items: center;
  }

  .icon-audience-logo {
    width: 54px;
    height: 24px;
  }
}
