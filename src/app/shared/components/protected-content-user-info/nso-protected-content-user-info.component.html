<div class="user-info">
  <ng-container *ngIf="isComponentVisible$ | async; else notLoggedIn">
    <div class="user-info-logged">
      <div class="user-info-logged-name"><strong>Előfizető:</strong> {{ storedData?.name }}</div>
      <div class="user-info-logged-action">
        <a (click)="logout()">Kijelentkezés</a>
      </div>
    </div>
  </ng-container>
  <ng-template #notLoggedIn>
    <div class="user-info-not-logged">
      <div class="user-info-not-logged-text"><PERSON><PERSON><PERSON><PERSON> tartalom, a digitális eléréshez kérjük adja meg az előfizetéséhez tartozó adatokat.</div>
      <div class="user-info-not-logged-action">
        <nso-simple-button round="round" color="primary" (click)="openModal()">Adatok megadása</nso-simple-button>
      </div>
    </div>
  </ng-template>

  <div class="user-info-content-loading" *ngIf="isLoading">Betöltés, kérem várjon....</div>
</div>

<nso-protected-content-login-modal></nso-protected-content-login-modal>
