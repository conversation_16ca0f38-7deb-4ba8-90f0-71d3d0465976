@use 'shared' as *;

.user-info {
  &-not-logged {
    &-text {
      font-weight: bold;
    }

    &-action {
      nso-simple-button {
        margin: 20px 0 50px;
      }
    }
  }

  &-logged {
    display: flex;
    margin-bottom: 20px;
    justify-content: space-between;

    a {
      color: var(--kui-gray-550);
      text-decoration: underline;
      transition: color ease-in-out 300ms;
      cursor: pointer;

      &:hover {
        color: var(--kui-red-200);
        text-decoration: underline;
      }
    }
  }

  &-content-loading {
    margin-bottom: 100px;
  }
}
