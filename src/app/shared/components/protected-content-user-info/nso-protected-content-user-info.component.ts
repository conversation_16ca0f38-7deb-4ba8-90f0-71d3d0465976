import { ChangeDetectionStrategy, Component } from '@angular/core';
import { ProtectedContentUserInfoComponent } from '@trendency/kesma-ui';
import { AsyncPipe, NgIf } from '@angular/common';
import { NsoSimpleButtonComponent } from '../simple-button/nso-simple-button.component';
import { NsoProtectedContentLoginModalComponent } from '../protected-content-login-modal/nso-protected-content-login-modal.component';

@Component({
  selector: 'nso-protected-content-user-info',
  templateUrl: './nso-protected-content-user-info.component.html',
  styleUrls: ['./nso-protected-content-user-info.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, AsyncPipe, NsoSimpleButtonComponent, NsoProtectedContentLoginModalComponent],
})
export class NsoProtectedContentUserInfoComponent extends ProtectedContentUserInfoComponent {}
