import { ChangeDetectionStrategy, Component, EventEmitter, inject, Output } from '@angular/core';
import { PortalConfigSetting } from '@trendency/kesma-ui';
import { telekomVivicittaUrl } from '../../utils';
import { PortalConfigService } from '../../services';

@Component({
  selector: 'app-telekom-vivicitta-header-element',
  templateUrl: './telekom-vivicitta-header-element.component.html',
  styleUrls: ['./telekom-vivicitta-header-element.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [],
})
export class TelekomVivicittaHeaderElementComponent {
  readonly telekomVivicittaUrl: string = telekomVivicittaUrl;
  readonly #portalConfigService: PortalConfigService = inject(PortalConfigService);
  readonly isEnabled: boolean = this.#portalConfigService.isConfigSet(PortalConfigSetting.ENABLE_TELEKOM_VIVICITTA);

  @Output() closeHamburgerMenu: EventEmitter<void> = new EventEmitter<void>();
}
