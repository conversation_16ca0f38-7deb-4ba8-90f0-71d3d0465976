import { ChangeDetectionStrategy, Component } from '@angular/core';
import { BaseComponent } from '@trendency/kesma-ui';
import { ScoreTripHeaderLineDefinitions } from '../../definitions';
import { NgIf, NgStyle } from '@angular/common';

@Component({
  selector: 'nso-scoretrip-header-line',
  templateUrl: './nso-scoretrip-header-line.component.html',
  styleUrls: ['./nso-scoretrip-header-line.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NgStyle],
})
export class NsoScoretripHeaderLineComponent extends BaseComponent<ScoreTripHeaderLineDefinitions> {}
