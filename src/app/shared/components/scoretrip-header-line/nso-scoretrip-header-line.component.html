<div *ngIf="data" class="scoretrip-header">
  <div class="scoretrip-header-rectangle left" [ngStyle]="{ 'border-bottom': '95px solid ' + data?.color1 }"></div>
  <div class="scoretrip-header-data">
    <div class="scoretrip-header-data-team">
      <div class="scoretrip-header-data-team-logo"><img *ngIf="data?.img1" [src]="data?.img1" [alt]="data?.img1 || ''" loading="lazy" /></div>
      <div class="scoretrip-header-data-team-name">{{ data?.club1 }}</div>
    </div>
    <div class="scoretrip-header-data-result" *ngIf="!data.isFuture">
      <div class="scoretrip-header-data-result-point">{{ data?.score1 }}</div>
      <div class="scoretrip-header-data-result-live">
        <span *ngIf="data?.isLive" class="scoretrip-header-data-result-live-text">Élő</span>
        <span class="scoretrip-header-data-result-score"> {{ data?.isLive ? 'Eredmény' : 'Végeredmény' }}</span>
      </div>
      <div class="scoretrip-header-data-result-point">{{ data?.score2 }}</div>
    </div>
    <div class="scoretrip-header-data-team away">
      <div class="scoretrip-header-data-team-logo"><img *ngIf="data?.img2" [src]="data?.img2" [alt]="data?.img2 || ''" loading="lazy" /></div>
      <div class="scoretrip-header-data-team-name">{{ data?.club2 }}</div>
    </div>
  </div>
  <div class="scoretrip-header-rectangle right" [ngStyle]="{ 'border-bottom': '95px solid ' + data?.color2 }"></div>
</div>
