@use 'shared' as *;

:host {
  display: block;
}

.scoretrip-header {
  display: flex;
  justify-content: space-between;
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
  height: 95px;

  @include media-breakpoint-down(md) {
    justify-content: center;
    background-color: var(--kui-gray-100);
    padding: 16px 0;
    height: auto;
  }

  &-data {
    display: flex;
    gap: 64px;

    @include media-breakpoint-down(md) {
      gap: 16px;
    }

    &-result {
      display: flex;
      align-items: center;
      gap: 24px;
      position: relative;
      padding-top: 12px;

      @include media-breakpoint-down(md) {
        &-score {
          position: absolute;
          top: 0;
          left: 50%;
          transform: translateX(-50%);
          font-weight: 700;
          font-size: 12px;
        }
      }

      &-point {
        font-family: var(--kui-font-condensed);
        font-size: 48px;
        font-weight: 700;
        line-height: 53px;

        @include media-breakpoint-down(md) {
          font-size: 38px;
          line-height: 42px;
        }
      }

      &-live {
        display: flex;
        flex-direction: column;
        gap: 5px;
        justify-content: center;
        align-items: center;

        &-text {
          color: var(--kui-red-350);
          font-weight: 700;
        }
      }

      &-date {
        font-size: 24px;
        font-weight: 700;
        line-height: 31px;
        text-align: center;

        @include media-breakpoint-down(md) {
          font-size: 14px;
          line-height: 22px;
          padding-bottom: 12px;
        }
      }
    }

    &-team {
      display: flex;
      align-items: center;
      gap: 16px;
      max-width: 200px;

      @include media-breakpoint-down(md) {
        flex-direction: column;
        align-items: center;
        max-width: 100px;
        width: 100px;
        gap: 8px;
      }

      &-name {
        font-family: var(--kui-font-condensed);
        font-weight: 700;
        font-size: 22px;
        line-height: 24px;
        text-transform: uppercase;

        @include media-breakpoint-down(md) {
          text-align: center;
          font-size: 16px;
          line-height: 19px;
        }
      }

      &-logo {
        display: flex;
        justify-content: center;
        align-items: center;
        min-width: 64px;
        height: 64px;
        border-radius: 64px;
        border: 1px solid var(--kui-gray-150);

        @include media-breakpoint-down(md) {
          min-width: 48px;
          width: 48px;
          height: 48px;
        }

        img {
          border-radius: 54px;
          max-width: 54px;
          max-height: 54px;

          @include media-breakpoint-down(md) {
            max-width: 40px;
            max-height: 40px;
          }
        }
      }

      &.away {
        flex-direction: row-reverse;

        @include media-breakpoint-down(md) {
          flex-direction: column;
          align-items: center;
        }
      }
    }
  }

  &-rectangle {
    height: 0;
    width: 35%;
    max-width: 275px;
    margin: 0 0 10px 0;

    @include media-breakpoint-down(md) {
      display: none;
    }
    @include media-breakpoint-down(lg) {
      width: 20%;
    }

    &.left {
      border-right: 45px solid transparent;
    }

    &.right {
      border-left: 45px solid transparent;
    }
  }
}
