import { ChangeDetectionStrategy, Component, Input, OnInit } from '@angular/core';
import { ChampionshipSchedule, EbDailyProgramComponent, EBPortalEnum } from '@trendency/kesma-ui';
import { Observable } from 'rxjs';
import { UtilService } from '@trendency/kesma-core';
import { EbService, SportResultService } from '../../../services';
import { AsyncPipe, NgIf } from '@angular/common';

@Component({
  selector: 'app-eb-daily-program',
  templateUrl: './eb-daily-program.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, AsyncPipe, EbDailyProgramComponent],
})
export class NsoEbDailyProgramComponent implements OnInit {
  @Input() fillByDate: [string, string];

  dailyProgram$: Observable<ChampionshipSchedule[]>;

  readonly EBPortalEnum = EBPortalEnum;

  constructor(
    private readonly sportResultService: SportResultService,
    private readonly ebService: EbService,
    private readonly utilService: UtilService
  ) {}

  ngOnInit(): void {
    const [slug, date] = this.fillByDate ?? [];
    const ebSlug = this.ebService.getSlug();

    this.dailyProgram$ =
      this.utilService.isBrowser() && slug && date
        ? this.sportResultService.getScheduleByDate(slug, date)
        : this.sportResultService.getScheduleByCompetition(ebSlug);
  }
}
