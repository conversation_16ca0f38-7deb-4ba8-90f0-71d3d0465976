import { ChangeDetectionStrategy, Component, Input, OnInit } from '@angular/core';
import { Observable, of } from 'rxjs';
import { backendDateToDate, EBPortalEnum, EbSingleEliminationComponent, Elimination, SingleElimination } from '@trendency/kesma-ui';
import { map } from 'rxjs/operators';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { UtilService } from '@trendency/kesma-core';
import { EbService, SportResultService } from '../../../services';
import { AsyncPipe, NgIf } from '@angular/common';

@Component({
  selector: 'app-eb-single-elimination',
  templateUrl: './eb-single-elimination.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [AsyncPipe, NgIf, EbSingleEliminationComponent],
})
export class NsoEbSingleEliminationComponent implements OnInit {
  @Input() explicitCompetitionSlug?: string;

  singleElimination$: Observable<SingleElimination>;

  readonly MOBILE_BREAKPOINT = '(max-width: 768px)';
  readonly EBPortalEnum = EBPortalEnum;

  isMobile$ = this.breakpointObserver.observe([this.MOBILE_BREAKPOINT]).pipe(map((state: BreakpointState) => state.matches));

  constructor(
    private readonly sportResultService: SportResultService,
    private readonly ebService: EbService,
    private readonly breakpointObserver: BreakpointObserver,
    private readonly utilService: UtilService
  ) {}

  ngOnInit(): void {
    const ebSlug = this.explicitCompetitionSlug || this.ebService.getSlug();
    this.singleElimination$ = !this.utilService.isBrowser()
      ? of({} as any)
      : this.sportResultService
          .getSchedulesGroupedByRound(ebSlug)
          .pipe(map((singleElimination) => this.mapBackendSingleEliminationToSingleElimination(singleElimination)));
  }

  private mapBackendSingleEliminationToSingleElimination(singleElimination: SingleElimination): SingleElimination {
    Object.entries(singleElimination).forEach(([key, value]) => {
      singleElimination[key as keyof SingleElimination] = value.map((elimination: Elimination) => ({
        ...elimination,
        scheduleDate: {
          ...elimination.scheduleDate,
          date: backendDateToDate(elimination.scheduleDate.date.toString()),
        },
      }));
    });
    return singleElimination;
  }
}
