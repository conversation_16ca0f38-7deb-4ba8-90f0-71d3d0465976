<kesma-eb-table-header-with-url title="Legfrissebb" [link]="link" linkText="Tovább az élő hírfolyamra"></kesma-eb-table-header-with-url>

<div class="superfeed-articles" *ngIf="(isMobile$ | async) === false">
  <div class="superfeed-articles-column">
    <ng-container *ngFor="let article of data | slice: 0 : 6">
      <ng-container *ngTemplateOutlet="feedArticle; context: { article }"></ng-container>
    </ng-container>
  </div>
  <div class="superfeed-articles-column">
    <ng-container *ngFor="let article of data | slice: 6 : 12">
      <ng-container *ngTemplateOutlet="feedArticle; context: { article }"></ng-container>
    </ng-container>
  </div>
</div>

<ng-template #feedArticle let-article="article">
  <article>
    <a class="superfeed-article-link" [routerLink]="buildArticleUrl(article)">
      <span class="superfeed-article-date">{{ getPublishDate(article) | dfnsFormat: 'HH:mm' }}</span>
      <h2 class="superfeed-article-title">{{ article.title }}</h2>
    </a>
  </article>
</ng-template>
