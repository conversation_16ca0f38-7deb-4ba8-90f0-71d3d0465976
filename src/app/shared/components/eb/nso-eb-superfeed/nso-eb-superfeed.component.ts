import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { ArticleCard, BaseComponent, buildArticleUrl, EbTableHeaderWithUrlComponent } from '@trendency/kesma-ui';
import { Observable, startWith } from 'rxjs';
import { map } from 'rxjs/operators';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { AsyncPipe, NgForOf, NgIf, NgTemplateOutlet, SlicePipe } from '@angular/common';
import { RouterLink } from '@angular/router';
import { FormatPipeModule } from 'ngx-date-fns';

const MOBILE_BREAKPOINT = '(min-width: 768px)';
const MOBILE_DESKTOP_WIDTH = 6;

@Component({
  selector: 'nso-eb-superfeed',
  templateUrl: 'nso-eb-superfeed.component.html',
  styleUrls: ['nso-eb-superfeed.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [EbTableHeaderWithUrlComponent, NgIf, AsyncPipe, NgForOf, SlicePipe, NgTemplateOutlet, RouterLink, FormatPipeModule],
})
export class NsoEbSuperfeedComponent extends BaseComponent<ArticleCard[]> implements OnInit {
  @Input() link: string = '/hirfolyam/eb-hirfolyam';
  @Input() desktopWidth: number = 12;
  isMobile: boolean = false;
  isMobile$: Observable<boolean> = this.breakpointObserver.observe([MOBILE_BREAKPOINT]).pipe(
    startWith({ matches: this.desktopWidth <= MOBILE_DESKTOP_WIDTH, breakpoints: {} }),
    map((state: BreakpointState) => {
      this.isMobile = !state.matches || this.desktopWidth <= MOBILE_DESKTOP_WIDTH;
      return this.isMobile;
    })
  );

  constructor(
    private readonly breakpointObserver: BreakpointObserver,
    private readonly cdr: ChangeDetectorRef
  ) {
    super();
  }

  buildArticleUrl = buildArticleUrl;

  override ngOnInit(): void {
    super.ngOnInit();
    if (this.data?.length) {
      this.sortArticlesByPublishDate();
    }
  }

  sortArticlesByPublishDate(): void {
    this.data?.sort((a: ArticleCard, b: ArticleCard) => this.getPublishDate(b)?.getTime() - this.getPublishDate(a)?.getTime());
    this.cdr.markForCheck();
  }

  getPublishDate(article: ArticleCard): Date {
    // In this case the BE sends publishDate in ISO8601 format, that's why we don't need to use the backendDateToDate function
    return article?.publishDate instanceof Date ? article?.publishDate : new Date(article?.publishDate as string);
  }
}
