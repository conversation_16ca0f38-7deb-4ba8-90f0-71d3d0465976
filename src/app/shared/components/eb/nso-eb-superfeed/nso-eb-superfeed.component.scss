@use 'shared' as *;

:host {
  display: flex;
  flex-direction: column;
  gap: 24px;
  width: 100%;

  .superfeed {
    &-articles {
      display: flex;
      gap: 40px;
      &-column {
        display: flex;
        flex-direction: column;
        gap: 16px;
      }
    }

    &-article {
      &-link {
        display: flex;
        align-items: center;
        padding: 0 8px;
        gap: 16px;
      }

      &-date {
        background-color: #143cdb;
        border: 1px solid #143cdb;
        border-radius: 32px;
        padding: 8px 12px;
        font-size: 24px;
        font-family: var(--kui-font-condensed);
        line-height: 28px;
        letter-spacing: 0.48px;
        color: var(--kui-white);
        font-weight: 700;
      }

      &-title {
        font-size: 18px;
        line-height: normal;
        color: var(--kui-gray-600);
      }
    }
  }
}
