import { ChangeDetectionStrategy, Component, Input, OnInit } from '@angular/core';
import { EBPortalEnum, EbTeamsComponent, EBTeamsTable, PortalConfigSetting } from '@trendency/kesma-ui';
import { CompetitionSummary } from 'src/app/shared/definitions/sport-results.definitions';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { EbService, PortalConfigService, SportResultService } from '../../../services';
import { AsyncPipe, NgIf } from '@angular/common';

@Component({
  selector: 'app-eb-teams',
  templateUrl: './eb-teams.component.html',
  styleUrls: ['./eb-teams.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [AsyncPipe, NgIf, EbTeamsComponent],
})
export class NsoEbTeamsComponent implements OnInit {
  @Input() desktopWidth = 12;
  @Input() explicitCompetitionSlug?: string;
  @Input() phaseId?: string;

  teams$?: Observable<EBTeamsTable[]>;
  isSingleEliminationEnabled = this.portalConfigService.isConfigSet(PortalConfigSetting.MENU_TYPE_AVAILABLE_SPORT_SINGLE_ELIMINATION);

  readonly EBPortalEnum = EBPortalEnum;

  constructor(
    private readonly sportResultService: SportResultService,
    private readonly ebService: EbService,
    private readonly portalConfigService: PortalConfigService
  ) {}

  ngOnInit(): void {
    const ebSlug = this.explicitCompetitionSlug || this.ebService.getSlug();
    this.teams$ = this.sportResultService
      .getCompetitionSummary(ebSlug, this.phaseId)
      .pipe(map((competitionSummary) => this.mapBackendSummaryToSummary(competitionSummary)));
  }

  private mapBackendSummaryToSummary(summaries: CompetitionSummary[]): EBTeamsTable[] {
    return summaries.map((summary) => ({
      groupName: summary?.phase?.name,
      teams: summary?.table,
      results: summary?.schedules,
    }));
  }
}
