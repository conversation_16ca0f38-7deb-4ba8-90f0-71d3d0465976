import { ChangeDetectionStrategy, ChangeDetectorRef, Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { filter, map, takeUntil } from 'rxjs/operators';
import { Observable, Subject } from 'rxjs';
import { ArticleCard, backendDateToDate, PortalConfigSetting } from '@trendency/kesma-ui';
import { NavigationEnd, Router, RouterLink } from '@angular/router';
import { StorageService } from '@trendency/kesma-core';
import { ApiService, PortalConfigService } from '../../../services';
import { AsyncPipe, NgIf } from '@angular/common';
import { NsoEbSuperfeedComponent } from '../nso-eb-superfeed/nso-eb-superfeed.component';
import { NsoEbDailyProgramComponent } from '../eb-daily-program/eb-daily-program.component';
import { NsoEbTeamsComponent } from '../eb-teams/eb-teams.component';
import { NsoEbSingleEliminationComponent } from '../eb-single-elimination/eb-single-elimination.component';

const MOBILE_BREAKPOINT = '(min-width: 992px)';
const NEWS_FEED_SLUG = 'eb-hirfolyam';

@Component({
  selector: 'app-eb-collapse-menu',
  templateUrl: 'eb-collapse-menu.component.html',
  styleUrls: ['eb-collapse-menu.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [RouterLink, NgIf, AsyncPipe, NsoEbSuperfeedComponent, NsoEbDailyProgramComponent, NsoEbTeamsComponent, NsoEbSingleEliminationComponent],
})
export class EbCollapseMenuComponent implements OnInit, OnDestroy {
  #isCollapsed = false;
  readonly #destroy$ = new Subject<void>();

  isMobile$: Observable<boolean> = this.breakpointObserver.observe([MOBILE_BREAKPOINT]).pipe(map((state: BreakpointState) => !state.matches));

  superFeed$: Observable<ArticleCard[]> = this.apiService.getDossier(NEWS_FEED_SLUG, 0, 12).pipe(
    map(({ data }) => data as ArticleCard[]),
    map((data) => {
      return data.map((article) => ({ ...article, publishDate: backendDateToDate(article.publishDate as string) as Date }));
    })
  );

  readonly MENU_TYPE_AVAILABLE_SPORT_SINGLE_ELIMINATION = this.portalConfigService.isConfigSet(
    PortalConfigSetting.MENU_TYPE_AVAILABLE_SPORT_SINGLE_ELIMINATION
  );

  constructor(
    private readonly breakpointObserver: BreakpointObserver,
    private readonly portalConfigService: PortalConfigService,
    private readonly apiService: ApiService,
    private readonly router: Router,
    private readonly cdr: ChangeDetectorRef,
    private readonly storageService: StorageService
  ) {}

  ngOnInit(): void {
    (this.router.events.pipe(takeUntil(this.#destroy$)).pipe(filter((event) => event instanceof NavigationEnd)) as Observable<NavigationEnd>).subscribe(() => {
      this.#isCollapsed = false;
      this.cdr.markForCheck();
    });

    if (!this.storageService.getLocalStorageData('hasVisitedThePage')) {
      this.#isCollapsed = true;
      this.storageService.setLocalStorageData('hasVisitedThePage', true);
    }
  }

  ngOnDestroy(): void {
    this.#destroy$.next();
    this.#destroy$.complete();
  }

  get isCollapsed(): boolean {
    return this.#isCollapsed;
  }

  toggle(): void {
    this.#isCollapsed = !this.#isCollapsed;
  }
}
