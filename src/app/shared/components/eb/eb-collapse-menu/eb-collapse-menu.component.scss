@use 'shared' as *;

:host {
  display: block;
  width: 100%;
  position: relative;
  z-index: 1;

  .menu {
    background-color: var(--kui-brand-eb-light-blue);
    margin-bottom: 50px;
    padding: 0 15px;

    &-content {
      @extend %flex-center;
      max-width: $header-max-width;
      height: 84px;
      justify-content: space-between;
      margin: 0 auto;
    }

    &-box {
      @extend %flex-center;
      position: relative;
      max-width: 235px;
      width: 100%;
      height: 24px;
      gap: 20px;

      @include media-breakpoint-down(md) {
        width: auto;
      }

      &.center {
        justify-content: center;

        @include media-breakpoint-down(md) {
          width: 32px;
          flex-shrink: 0;
        }
      }

      &.flex-end {
        justify-content: flex-end;
      }

      @include media-breakpoint-down(md) {
        gap: 12px;
      }
    }

    &-link {
      @extend %flex-center;
      color: var(--kui-white);
      font-size: 16px;
      font-weight: 700;
      line-height: 120%;
      letter-spacing: 0.16px;
      height: 100%;

      &.vertical-border {
        border-inline: 1px solid rgba(255, 255, 255, 0.5);
        padding: 0 20px;

        @include media-breakpoint-down(md) {
          padding: 0 12px;
        }

        @media (max-width: 330px) {
          padding: 0 8px;
        }
      }

      &.only-left-border {
        border-left: 1px solid rgba(255, 255, 255, 0.5);
        padding-left: 20px;
      }
    }

    &-toggle {
      @extend %flex-center;
      @extend %absolute-center;
      bottom: -66px;
      background-color: var(--kui-brand-eb-light-blue);
      padding: 10px;
      border-radius: 0 0 200px 200px;
      justify-content: center;
      width: 52px;
      height: 52px;
      cursor: pointer;

      @include media-breakpoint-down(md) {
        padding: 8px;
        width: 48px;
        height: 48px;
      }
    }
  }

  .icon-arrow-right {
    transform: rotate(90deg);
    transition: transform 400ms ease-in-out;
    width: 22px;
    height: 26px;

    &.collapsed {
      transform: rotate(270deg);
    }
  }

  .collapsible-content {
    width: $header-max-width;
    max-width: calc(100% - 30px);
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    gap: 48px;

    &.hidden {
      display: none;
    }

    @include media-breakpoint-down(md) {
      gap: 24px;
    }
  }
}

%flex-center {
  display: flex;
  align-items: center;
}

%absolute-center {
  position: absolute;
  left: 50%;
  transform: translate(-50%);
}
