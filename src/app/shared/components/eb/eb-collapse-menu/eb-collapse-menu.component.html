<div class="menu">
  <div class="menu-content">
    <div class="menu-box">
      <a class="menu-link" routerLink="/hirfolyam/eb-hirfolyam">ÉLŐ</a>
      <a class="menu-link vertical-border" routerLink="/rovat/foci-eb-2024">Főoldal</a>
    </div>
    <div class="menu-box center">
      <img alt="UEFA 2024" src="./assets/images/eb/eb-uefa-logo.svg" />
      <img *ngIf="(isMobile$ | async) === false" alt="UEFA 2024" src="./assets/images/eb/eb-uefa.svg" />
      <div (click)="toggle()" class="menu-toggle">
        <i [class.collapsed]="isCollapsed" class="icon icon-arrow-right"></i>
      </div>
    </div>
    <div class="menu-box flex-end">
      <a class="menu-link vertical-border" routerLink="/foci-eb-2024/2023/12/17-labdarugo-europa-bajnoksag-a-teljes-program-naponkent">Menetrend</a>
      <a class="menu-link" routerLink="/foci-eb-2024/2023/12/17-labdarugo-europa-bajnoksag-a-teljes-program-csoportonkent">
        <ng-container *ngIf="(isMobile$ | async) === false; else mobileTemplate">Eb&#8209;adatbank</ng-container>
        <ng-template #mobileTemplate>Eb</ng-template>
      </a>
      <a
        *ngIf="(isMobile$ | async) === false"
        class="menu-link only-left-border"
        routerLink="/foci-eb-2024/2024/05/a-2024-es-labdarugo-europa-bajnoksag-keretei"
        >Játékoskeretek</a
      >
    </div>
  </div>
</div>

<div [class.hidden]="!isCollapsed" class="wrapper collapsible-content">
  <ng-container *ngIf="superFeed$ | async as superFeed">
    <nso-eb-superfeed *ngIf="superFeed?.length" [data]="superFeed"></nso-eb-superfeed>
  </ng-container>
  <app-eb-daily-program></app-eb-daily-program>
  <app-eb-teams></app-eb-teams>
  <app-eb-single-elimination *ngIf="MENU_TYPE_AVAILABLE_SPORT_SINGLE_ELIMINATION && (isMobile$ | async) === false"> </app-eb-single-elimination>
</div>
