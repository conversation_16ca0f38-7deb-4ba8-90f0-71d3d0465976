import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { Ng<PERSON>lass, NgForOf } from '@angular/common';

@Component({
  selector: 'nso-header',
  templateUrl: './nso-header.component.html',
  styleUrls: ['./nso-header.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgClass, NgForOf],
})
export class NsoHeaderComponent {
  @Input() data: any;

  activeItem = 0;

  activateItem(i: any): void {
    this.activeItem = i;
  }
}
