@use 'shared' as *;

.header {
  background-color: var(--kui-red-400);
  padding: 24px 0;
  width: 100%;
  max-width: 1240px;
  @include media-breakpoint-down(sm) {
    padding: 16px 0;
  }

  .wrapper {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;

    .header-left {
      display: flex;
      flex-direction: row;
      align-items: center;

      .header-logo {
        display: block;
        width: 76px;
        height: 34px;
        margin-right: 32px;
        // @include icon('logo.svg');
        @include media-breakpoint-down(sm) {
          width: 60px;
          height: 28px;
        }
      }

      .menu {
        margin: 0 -10px;
        display: flex;
        flex-direction: row;
        align-items: center;

        @include media-breakpoint-down(sm) {
          display: none;
        }

        &:not(:focus-within):not(:hover) {
          .menu-item {
            color: var(--kui-white);
          }
        }

        .menu-item {
          font-weight: bold;
          font-size: 16px;
          line-height: 120%;
          letter-spacing: 0.01em;
          padding: 0 10px;
          color: rgba(255, 255, 255, 0.7);
          position: relative;

          &:hover {
            color: var(--kui-white);
          }

          &.active {
            &:after {
              content: '';
              position: absolute;
              width: calc(100% - 20px);
              height: 4px;
              background-color: var(--kui-white);
              bottom: -33px;
              left: 10px;
            }
          }
        }

        .no-2 ~ .menu-item {
          @include media-breakpoint-down(md) {
            display: none;
          }
        }

        .no-3 ~ .menu-item {
          @include media-breakpoint-down(md) {
            display: none;
          }
        }

        .no-5 ~ .menu-item {
          @media (max-width: 1360px) {
            display: none;
          }
        }
      }

      .icon-more {
        margin-left: 20px;
        width: 24px;
        height: 24px;
        // @include icon('icons/icon-more.svg');
        @include media-breakpoint-down(sm) {
          display: none;
        }
      }
    }

    .header-right {
      display: flex;
      flex-direction: row;
      align-items: center;

      .menu-item {
        font-weight: bold;
        font-size: 16px;
        line-height: 120%;
        letter-spacing: 0.01em;
        margin-left: 20px;
        padding-right: 20px;
        border-right: 1px solid rgba(255, 255, 255, 0.5);
        color: var(--kui-white);

        &.loc {
          @include media-breakpoint-down(md) {
            display: none;
          }
        }

        @include media-breakpoint-down(sm) {
          display: none;
        }
      }

      .menu-button {
        width: 24px;
        height: 24px;
        margin-left: 20px;

        &.profile {
          display: none;
          //   @include icon('icons/user.svg');
          @include media-breakpoint-down(sm) {
            display: block;
          }
        }

        &.search {
          //   @include icon('icons/icon-search.svg');

          &.open {
            &:after {
              content: '';
              position: absolute;
              width: calc(100% - 20px);
              height: 4px;
              background-color: var(--kui-white);
              bottom: -33px;
              left: 10px;
            }
          }
        }

        &.hamburger {
          //   @include icon('icons/icon-menu.svg');
        }
      }

      .search-container {
        position: relative;

        .search-tab {
          position: absolute;
          bottom: -84px;
          right: -30px;
          padding: 16px 36px 16px 30px;
          display: flex;
          flex-direction: row;
          align-items: center;
          z-index: 100;
          background-color: var(--kui-white);
          @include media-breakpoint-down(sm) {
            right: -50px;
            bottom: -73px;
          }

          .search-input {
            width: 450px;
            max-width: 50vw;
            margin-right: 15px;
          }

          .search-close {
            font-size: 16px;
            line-height: 160%;
            color: var(--kui-red-400);
          }
        }
      }
    }
  }
}
