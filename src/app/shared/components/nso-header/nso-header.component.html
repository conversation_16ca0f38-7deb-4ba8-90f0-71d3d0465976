<section class="header">
  <div class="wrapper">
    <div class="header-left">
      <a href="/" class="header-logo"></a>
      <div class="menu">
        <a
          class="menu-item active no-{{ i }}"
          [ngClass]="{ active: i === activeItem }"
          (click)="activateItem(i)"
          *ngFor="let menuItem of data.menuItems; let i = index"
          >{{ menuItem.name }}</a
        >
      </div>
      <button class="icon-more"></button>
    </div>
    <div class="header-right">
      <div class="menu-item loc">English edition</div>
      <div class="menu-item">My NSO</div>
      <div class="menu-item">Belépés</div>
      <button class="menu-button profile"></button>
      <div class="search-container">
        <button class="menu-button search"></button>
        <div class="search-tab">
          <input type="text" class="search-input" placeholder="Keresés sportágra, csapatra, lig<PERSON>ra vagy j<PERSON>... " />
          <button class="search-close">Be<PERSON><PERSON><PERSON><PERSON></button>
        </div>
      </div>
      <button class="menu-button hamburger"></button>
    </div>
  </div>
</section>
