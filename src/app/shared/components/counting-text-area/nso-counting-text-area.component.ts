import { ChangeDetectionStrategy, Component, forwardRef } from '@angular/core';
import { FormsModule, NG_VALIDATORS, NG_VALUE_ACCESSOR } from '@angular/forms';
import { CountingTextAreaComponent } from '@trendency/kesma-ui';
import { NgIf } from '@angular/common';

@Component({
  selector: 'nso-counting-text-area',
  templateUrl: '../../../../../node_modules/@trendency/kesma-ui/src/lib/components/counting-text-area/counting-text-area.component.html',
  styleUrls: [
    '../../../../../node_modules/@trendency/kesma-ui/src/lib/components/counting-text-area/counting-text-area.component.scss',
    './nso-counting-text-area.component.scss',
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => NsoCountingTextAreaComponent),
      multi: true,
    },
    {
      provide: NG_VALIDATORS,
      useExisting: forwardRef(() => NsoCountingTextAreaComponent),
      multi: true,
    },
  ],
  imports: [FormsModule, NgIf],
})
export class NsoCountingTextAreaComponent extends CountingTextAreaComponent {}
