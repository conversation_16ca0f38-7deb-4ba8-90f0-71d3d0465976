@use 'shared' as *;

.protected-content-login-modal {
  .modal-wrapper:not(.submitted) {
    width: 600px;

    @include media-breakpoint-down(md) {
      width: auto;
    }
  }

  &-actions {
    margin-top: 30px;

    nso-simple-button {
      max-width: 250px;
      margin: 0 auto;
    }

    &-link {
      text-align: center;
      margin-top: 20px;

      a {
        color: var(--kui-gray-550);
        text-decoration: underline;
        transition: color ease-in-out 300ms;

        &:hover {
          color: var(--kui-red-200);
          text-decoration: underline;
        }
      }
    }
  }

  &-info-text {
    font-size: 12px;
    line-height: 16px;
    color: var(--kui-gray-400);
    text-align: left;
    padding: 30px 0 0;

    a {
      color: var(--kui-gray-550);
      text-decoration: underline;
      transition: color ease-in-out 300ms;

      &:hover {
        color: var(--kui-red-200);
        text-decoration: underline;
      }
    }
  }

  &-submitted-text {
    font-family: var(--kui-font-primary);
    color: var(--kui-text-color);
    font-size: 16px;
    font-weight: 400;
    line-height: 18px;
    text-align: center;
    max-width: 300px;
    margin: 0 auto;
  }
}
