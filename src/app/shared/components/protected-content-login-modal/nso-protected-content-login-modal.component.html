<div class="modal protected-content-login-modal" *ngIf="isModalVisible$ | async">
  <form *ngIf="formGroup" [formGroup]="formGroup" class="nso-form" (ngSubmit)="checkPermission()">
    <div class="modal-wrapper {{ isSubmitted ? 'submitted' : '' }}">
      <div class="modal-header">
        <div class="title">Digitális olvasás aktiválása</div>
        <i class="icon icon-x-circle" (click)="closeModal()"></i>
      </div>
      <div class="modal-body">
        <ng-container *ngIf="!isSubmitted">
          <div class="nso-form-row">
            <kesma-form-control>
              <label class="nso-form-label" for="name">Előfizető neve</label>
              <input class="nso-form-input" type="text" id="name" formControlName="name" />
            </kesma-form-control>
          </div>
          <div class="nso-form-row">
            <kesma-form-control>
              <label class="nso-form-label" for="code">Kérjük adja meg az ügyfélkódot</label>
              <div class="nso-form-input-password">
                <input class="nso-form-input" [type]="showCode ? 'text' : 'password'" id="code" formControlName="code" />
                <img
                  class="nso-form-input-password-img"
                  [src]="showCode ? 'assets/images/icons/icon-no-eye.svg' : 'assets/images/icons/icon-eye.svg'"
                  alt="Ügyfélkód megtekintése"
                  (click)="showCode = !showCode"
                  loading="lazy"
                />
              </div>
            </kesma-form-control>
          </div>
          <div class="nso-form-row">
            <kesma-form-control class="checkbox">
              <label class="nso-form-checkbox" for="terms">
                <input type="checkbox" id="terms" formControlName="terms" />
                <span
                  >Regisztrációmmal elfogadom a Mediaworks Hungary Zrt.
                  <a class="nso-form-checkbox-link" href="https://mediaworks.hu/adatvedelem" target="_blank">Adatvédelmi tájékoztatóját</a>
                  és
                  <a class="nso-form-checkbox-link" href="https://mediaworks.hu/elofizetoi-aszf" target="_blank">Felhasználási feltételeit</a>
                  és hozzájárulok ahhoz, hogy az általam közölt adatokat a digitális lapelérés érdekében a Mediaworks Hungary Zrt. kezelje.</span
                >
              </label>
            </kesma-form-control>
          </div>
          <div class="nso-form-general-error" *ngIf="error">
            {{ error }}
          </div>
          <div class="protected-content-login-modal-actions">
            <nso-simple-button round="round" class="w-100" color="primary" [disabled]="isLoading" [isSubmit]="true">
              {{ isLoading ? 'Kérlek várj...' : 'Aktiválás' }}
            </nso-simple-button>
            <div class="protected-content-login-modal-actions-link">
              <a href="https://digitalstand.hu/nemzetisport">Még nem vagyok előfizető</a>
            </div>
          </div>
          <div class="protected-content-login-modal-info-text">
            Amennyiben Ön a Magyar Posta Zrt.-nél fizetett elő a kiadványra, azaz postai előfizetéssel rendelkezik, kérjük írja meg nevét, kézbesítési címét,
            telefonszámát, e-mail címét és az előfizetett lap nevét a
            <a href="mailto:<EMAIL>">digitalislap&#64;mediaworks.hu</a> címre.
          </div>
        </ng-container>
        <ng-container *ngIf="isSubmitted">
          <div class="protected-content-login-modal-submitted-text">
            Sikeresen aktiválta a szolgáltatást!<br /><br />
            Önnek lehetősége van akár két eszközön egyidejűleg használni a szolgáltatást. Most ezen az eszközön megkezdheti a használatot.<br /><br />
            Kellemes olvasást kívánunk!
          </div>
          <div class="protected-content-login-modal-actions">
            <nso-simple-button round="round" class="w-100" color="primary" (click)="closeModal()">Olvasás</nso-simple-button>
          </div>
        </ng-container>
      </div>
    </div>
  </form>
  <div class="modal-backdrop" (click)="closeModal()"></div>
</div>
