import { ChangeDetectionStrategy, Component } from '@angular/core';
import { KesmaFormControlComponent, ProtectedContentLoginModalComponent } from '@trendency/kesma-ui';
import { AsyncPipe, NgIf } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { NsoSimpleButtonComponent } from '../simple-button/nso-simple-button.component';

@Component({
  selector: 'nso-protected-content-login-modal',
  templateUrl: './nso-protected-content-login-modal.component.html',
  styleUrls: ['./nso-protected-content-login-modal.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, AsyncPipe, ReactiveFormsModule, KesmaFormControlComponent, NsoSimpleButtonComponent],
})
export class NsoProtectedContentLoginModalComponent extends ProtectedContentLoginModalComponent {
  override limitLabel: string = 'napon';
  override limitNextLabel: string = 'holnap';
}
