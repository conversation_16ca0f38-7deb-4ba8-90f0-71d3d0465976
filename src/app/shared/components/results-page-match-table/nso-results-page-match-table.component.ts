import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, OnDestroy, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { BaseComponent, dataTableConfig, DataTableGeneratorComponent } from '@trendency/kesma-ui';
import { ResultsPageMatchTable } from '../../definitions';
import { debounceTime, distinctUntilChanged, fromEvent, map, startWith, Subject, takeUntil } from 'rxjs';
import { isToday } from 'date-fns';
import { NgForOf, NgIf, NgStyle, NgTemplateOutlet, UpperCasePipe } from '@angular/common';
import { FormatPipeModule } from 'ngx-date-fns';
import { LiveMatchDirective } from '../../directives';

@Component({
  selector: 'nso-results-page-match-table',
  templateUrl: './nso-results-page-match-table.component.html',
  styleUrls: ['./nso-results-page-match-table.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [UpperCasePipe, NgIf, FormatPipeModule, NgTemplateOutlet, NgStyle, DataTableGeneratorComponent, LiveMatchDirective, NgForOf],
})
export class NsoResultsPageMatchTableComponent extends BaseComponent<ResultsPageMatchTable> implements OnInit, OnDestroy, AfterViewInit {
  private readonly destroy$ = new Subject<void>();

  currentWindowWidth$ = fromEvent(window, 'resize').pipe(
    map(() => window.innerWidth),
    startWith(window.innerWidth)
  );

  isMobile = window.innerWidth < 450;
  hasLiveMatch?: boolean;
  isToday?: boolean;

  @ViewChild('matchesHeader', {
    read: TemplateRef,
    static: false,
  })
  matchesHeader?: TemplateRef<HTMLElement>;

  @ViewChild('scoreAndInfo', {
    read: TemplateRef,
    static: false,
  })
  scoreAndInfo?: TemplateRef<HTMLElement>;

  @ViewChild('teamA', {
    read: TemplateRef,
    static: false,
  })
  teamA?: TemplateRef<HTMLElement>;

  @ViewChild('teamB', {
    read: TemplateRef,
    static: false,
  })
  teamB?: TemplateRef<HTMLElement>;

  @ViewChild('teamInfo', {
    read: TemplateRef,
    static: false,
  })
  teamInfo?: TemplateRef<HTMLElement>;

  tableConfig: dataTableConfig[] = [];

  constructor(private readonly cd: ChangeDetectorRef) {
    super();
  }

  override ngOnInit(): void {
    super.ngOnInit();
    this.currentWindowWidth$.pipe(distinctUntilChanged(), debounceTime(10), takeUntil(this.destroy$)).subscribe((width: number) => {
      this.isMobile = width < 767;
      this.cd.markForCheck();
    });
  }

  ngAfterViewInit(): void {
    this.isToday = this.data?.matchDate && isToday(this.data?.matchDate);

    this.hasLiveMatch = this.data?.matches.some((match) => match?.isLive);
    this.tableConfig = [
      {
        hasHeader: true,
        headerColspan: 4,
        columnDataProperty: 'teamA.name',
        customColumnTemplate: this.teamA,
        customHeaderTemplate: this.matchesHeader,
        columnClass: 'cell',
        rowLinkDataProperty: 'link',
      },
      {
        hasHeader: false,
        headerColspan: 1,
        columnDataProperty: 'teamB.name',
        customColumnTemplate: this.teamB,
        columnClass: 'cell',
        rowLinkDataProperty: 'link',
      },
      {
        hasHeader: false,
        headerColspan: 1,
        columnDataProperty: 'teamB.score',
        customColumnTemplate: this.scoreAndInfo,
        columnClass: 'cell',
        rowLinkDataProperty: 'link',
      },

      {
        hasHeader: false,
        headerColspan: 1,
        columnDataProperty: 'info',
        customColumnTemplate: this.teamInfo,
        rowLinkDataProperty: 'link',
      },
    ];
    this.cd.detectChanges();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
