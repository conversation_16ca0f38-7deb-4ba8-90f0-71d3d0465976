<!-- Simple header -->

<header class="team-match-stats-header">
  {{ 'Eredmény' | uppercase }}
  <section class="team-match-stats-header-date">
    <ng-container *ngIf="isToday">{{ 'MA,' }}</ng-container>
    {{ this.data?.matchDate | dfnsFormat: 'MMMM dd.' | uppercase }}
  </section>
</header>

<!-- Live header desktop && tablet -->
<ng-template #matchesHeader>
  <section class="team-match-stats-live-header">
    <i class="team-stats-icon icon-live"></i>
    <span class="team-match-stats-live-title"> {{ 'Élő' | uppercase }} </span>
  </section>
</ng-template>

<!-- Teams in table -->

<ng-template #teamA let-rowData="data">
  <ng-container [ngTemplateOutlet]="team" [ngTemplateOutletContext]="{ team: rowData?.teamA }"></ng-container>
</ng-template>
<ng-template #teamB let-rowData="data">
  <ng-container [ngTemplateOutlet]="team" [ngTemplateOutletContext]="{ team: rowData?.teamB }"></ng-container>
</ng-template>

<!-- It is used on mobile too -->

<ng-template #team let-team="team">
  <section class="team-match-stats-team">
    <div class="team-match-stats-team-logo" [ngStyle]="{ 'background-image': 'url(' + team?.teamLogo + ')' }"></div>
    <span>{{ team?.name }}</span>
  </section>
</ng-template>

<!-- Scoreline or info on desktop -->

<ng-template #scoreAndInfo let-rowData="data">
  <ng-container *ngIf="rowData?.isLive || rowData?.hasEnded; else info">
    <ng-container
      [ngTemplateOutlet]="score"
      [ngTemplateOutletContext]="{ data: { teamA: rowData?.teamA, teamB: rowData?.teamB, liveMatch: rowData?.isLive } }"
    ></ng-container>
    <ng-template #score let-data="data">
      <ng-container *ngIf="data?.liveMatch; else notLive">
        <span class="team-match-stats-team-score"> {{ data?.teamA?.liveScore }} </span>
        <span class="team-match-stats-team-score"> &nbsp;-&nbsp;</span>
        <span class="team-match-stats-team-score"> {{ data?.teamB?.liveScore }} </span>
      </ng-container>
      <ng-template #notLive>
        <span class="team-match-stats-team-score"> {{ data?.teamA?.score }} </span>
        <span class="team-match-stats-team-score"> &nbsp;-&nbsp;</span>
        <span class="team-match-stats-team-score"> {{ data?.teamB?.score }} </span>
      </ng-template>
    </ng-template>
  </ng-container>

  <ng-template #info>
    <span class="team-match-stats-match-info">Kezdődik: {{ rowData?.startDate | dfnsFormat: 'HH:mm' }} (TV: {{ rowData?.channel }})</span>
  </ng-template>
</ng-template>

<!-- team match extra info -->

<ng-template #teamInfo let-rowData="data">
  <div class="team-match-stats-team-info">
    <div *ngIf="rowData?.info">{{ rowData.info }}</div>
  </div>
</ng-template>

<!-- Data table generator -->
<ng-container *ngIf="!isMobile; else mobileUi">
  <div class="table">
    <kesma-data-table-generator
      [nsoLiveMatchDirective]="data?.matches"
      [data]="data?.matches"
      [tableConfig]="tableConfig"
      [dataTableClass]="'team-match-stats'"
    ></kesma-data-table-generator>
  </div>
</ng-container>

<!-- Mobile ui -->

<ng-template #mobileUi>
  <section *ngIf="hasLiveMatch" class="team-match-stats-live-header-mobile">
    <i class="team-match-stats-icon-mobile icon-live"></i>
    <span class="team-match-stats-live-title-mobile"> {{ 'Élő' | uppercase }} </span>
  </section>
  <ng-container *ngFor="let match of data?.matches; let i = index">
    <section
      class="team-match-stats-container-mobile"
      [class.live-last-border-mobile]="match?.isLive && !data?.matches?.[i + 1]?.isLive"
      [class.live-border-mobile]="match?.isLive && data?.matches?.[i + 1]?.isLive"
    >
      <ng-container [ngTemplateOutlet]="matches" [ngTemplateOutletContext]="{ data: { team: match?.teamA, match } }"></ng-container>
      <ng-container [ngTemplateOutlet]="matches" [ngTemplateOutletContext]="{ data: { team: match?.teamB, match } }"></ng-container>
      <ng-container [ngTemplateOutlet]="teamInfo" [ngTemplateOutletContext]="{ data: match }"></ng-container>
    </section>
  </ng-container>
</ng-template>

<ng-template #matches let-data="data">
  <section class="team-match-stats-container-inner-mobile">
    <ng-container [ngTemplateOutlet]="team" [ngTemplateOutletContext]="{ team: data?.team }"></ng-container>
    <span class="team-match-stats-score-mobile" *ngIf="data?.match?.isLive; else notLive"> {{ data?.team?.liveScore }} </span>
    <ng-template #notLive
      ><span class="team-match-stats-score-mobile">{{ data?.team?.score }}</span></ng-template
    >
  </section>
</ng-template>
