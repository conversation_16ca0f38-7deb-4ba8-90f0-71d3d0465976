@use 'shared' as *;

:host::ng-deep {
  display: block;
  width: 100%;
  font-family: var(--kui-font-condensed);
  font-size: 16px;

  .table {
    overflow-x: auto;
  }

  .team-match-stats {
    width: 100%;

    .cell {
      min-width: 180px;
    }

    tbody tr:hover,
    tbody tr:focus-within {
      background: var(--kui-gray-50);
      outline: none;
    }

    a {
      display: block;
      color: var(--kui-black);
      padding-top: 20px;
      padding-bottom: 20px;
      min-height: 100%;
    }

    &-live-header {
      color: var(--kui-white);
      font-size: 22px;
      display: flex;
      align-items: center;
      padding-left: 16px;
      height: 100%;
    }

    &-icon {
      width: 8px;
      height: 8px;
    }

    &-live-title {
      margin-left: 11px;
    }

    &-team-logo {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      flex-shrink: 0;
      background-size: cover;
      background-position: center;
      margin-right: 16px;
    }

    &-team {
      display: flex;
      flex-direction: row;
      align-items: center;
      padding-left: 16px;
      font-weight: bold;

      @include media-breakpoint-down(md) {
        padding-left: 0;
      }
    }

    &-team-score {
      text-align: center;
      font-weight: bold;
      font-size: 22px;
    }

    &-team-info {
      display: inline-block;
      height: 51px;
      font-size: 16px;
      font-weight: bold;
      text-align: center;
      width: 378px;

      div {
        padding: 16px 24px;
        background: var(--kui-gray-75);
      }

      @include media-breakpoint-down(md) {
        width: 100%;
        text-align: start;
        height: 100%;

        div {
          padding: 8px 16px;
        }
      }
    }

    thead {
      border-left: solid 2px var(--kui-red-400);
      border-right: solid 2px var(--kui-red-400);
      height: 45px;
      background-color: var(--kui-red-400);
    }

    .hidden {
      display: none;
    }

    tr:not(thead tr:first-child) {
      text-align: center;
    }
  }

  .live-border {
    border: 2px solid var(--kui-red-400);
    border-bottom: none;
    border-top: none;

    &.last {
      border-bottom: 2px solid var(--kui-red-400);
    }
  }
}

.team-match-stats {
  &-header {
    text-align: center;
    color: var(--kui-black);
    background-color: var(--kui-gray-75);
    position: relative;
    font-size: 24px;
    font-weight: 700;
    height: 79px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;

    @include media-breakpoint-down(md) {
      height: 45px;
      font-size: 22px;
      justify-content: flex-end;
      padding-right: 16px;
    }
  }

  &-live-header-mobile {
    height: 45px;
    background: var(--kui-red-400);
    font-size: 22px;
    display: flex;
    align-items: center;
    padding-left: 16px;
    color: var(--kui-white);
    font-weight: 700;
  }

  &-icon-mobile {
    width: 8px;
    height: 8px;
  }

  &-live-title-mobile {
    margin-left: 11px;
  }

  &-container-mobile {
    display: flex;
    flex-direction: column;
    padding: 16px;
    border: 1px solid #bcbcbc;

    &.live-border-mobile {
      border-left: 2px solid var(--kui-red-400);
      border-right: 2px solid var(--kui-red-400);
    }

    &.live-last-border-mobile {
      border-left: 2px solid var(--kui-red-400);
      border-right: 2px solid var(--kui-red-400);
      border-bottom: 2px solid var(--kui-red-400);
    }
  }

  &-container-inner-mobile {
    height: 60px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &-score-mobile {
    font-size: 22px;
    font-weight: bold;
    padding-right: 16px;
  }

  &-header-date {
    color: var(--kui-red-400);
    font-size: 24px;
    position: absolute;
    left: 10px;
    font-weight: 700;
  }
}
