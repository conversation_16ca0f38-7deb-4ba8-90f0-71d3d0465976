<ng-template #playersWithPosition let-rowData="data">
  <section class="team-lineups-section">
    <span class="team-lineups-positions"> {{ rowData?.positionName }}: </span>
    <ng-container *ngFor="let player of rowData?.players; let i = index; let last = last">
      {{ player }}<ng-container *ngIf="!last">, </ng-container>
    </ng-container>
  </section>
</ng-template>

<h1 class="lineups-title">CSAPAT</h1>

<kesma-data-table-generator [data]="data" [tableConfig]="tableConfig" [dataTableClass]="'team-lineups'"></kesma-data-table-generator>
