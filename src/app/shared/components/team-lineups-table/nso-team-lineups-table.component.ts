import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { BaseComponent, dataTableConfig, DataTableGeneratorComponent } from '@trendency/kesma-ui';
import { TeamLineUpsDefinition } from '../../definitions';
import { NgForOf, NgIf } from '@angular/common';

@Component({
  selector: 'nso-team-lineups-table',
  templateUrl: './nso-team-lineups-table.component.html',
  styleUrls: ['./nso-team-lineups-table.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgForOf, NgIf, DataTableGeneratorComponent],
})
export class NsoTeamLineupsTableComponent extends BaseComponent<TeamLineUpsDefinition[]> implements OnInit {
  @ViewChild('playersWithPosition', {
    read: TemplateRef,
    static: true,
  })
  playersWithPosition?: TemplateRef<HTMLElement>;

  tableConfig: dataTableConfig[] = [];

  constructor(private readonly cd: ChangeDetectorRef) {
    super();
  }

  override ngOnInit(): void {
    super.ngOnInit();
    this.tableConfig = [
      {
        hasHeader: true,
        headerTitle: 'Játékosok',
        headerColspan: 1,
        columnDataProperty: 'positionName',
        headerClass: 'team-lineups-header',
        customColumnTemplate: this.playersWithPosition,
      },
    ];
    this.cd.detectChanges();
  }
}
