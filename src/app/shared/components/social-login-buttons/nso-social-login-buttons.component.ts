import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';
import { NgIf } from '@angular/common';

@Component({
  selector: 'nso-social-login-buttons',
  templateUrl: './nso-social-login-buttons.component.html',
  styleUrls: ['./nso-social-login-buttons.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf],
})
export class NsoSocialLoginButtonsComponent {
  @Input() isFacebookAllowed: boolean = true;
  @Input() isGoogleAllowed: boolean = true;
  @Input() isLoading: boolean = false;
  @Output() facebookClickEvent: EventEmitter<void> = new EventEmitter<void>();
  @Output() googleClickEvent: EventEmitter<void> = new EventEmitter<void>();
}
