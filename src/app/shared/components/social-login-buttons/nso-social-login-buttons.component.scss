@use 'shared' as *;

:host {
  display: block;
  width: 100%;
}

.social-login-buttons {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.btn {
  position: relative;
  padding: 20px 24px 20px 78px;
  font-family: var(--kui-font-primary);
  color: var(--kui-white);
  border-radius: 5px;
  font-size: 16px;
  font-weight: normal;
  line-height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  transition: all 0.3s ease;

  @include media-breakpoint-down(sm) {
    justify-content: flex-start;
  }

  &-facebook {
    background-color: #4469b0; // Facebook color, we don't need it elsewhere

    &:hover {
      background: rgba(68, 105, 176, 0.9);
    }
  }

  &-google {
    background-color: #d94e45; // Google color, we don't need it elsewhere

    &:hover {
      background: rgba(217, 78, 69, 0.9);
    }
  }

  &-icon {
    width: 30px;
    height: 30px;
    position: absolute;
    left: 24px;
  }

  &-label {
    position: relative;
    z-index: 2;
  }
}
