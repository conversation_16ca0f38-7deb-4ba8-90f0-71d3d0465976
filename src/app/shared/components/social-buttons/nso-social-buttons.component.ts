import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  computed,
  ElementRef,
  EventEmitter,
  Input,
  OnChanges,
  Output,
  SimpleChanges,
  ViewChild,
} from '@angular/core';
import { SeoService, UtilService } from '@trendency/kesma-core';
import { Clipboard } from '@angular/cdk/clipboard';
import { getFacebookShareUrl, getTwitterShareUrl, IconComponent, NumberIntoAbbreviatedStringPipe } from '@trendency/kesma-ui';
import { BehaviorSubject, debounceTime, distinctUntilChanged, fromEvent, Observable, startWith, tap } from 'rxjs';
import { AsyncPipe, NgIf, NgTemplateOutlet } from '@angular/common';
import { ColorChangeService } from '../../services';

@Component({
  selector: 'nso-social-buttons',
  templateUrl: 'nso-social-buttons.component.html',
  styleUrls: ['nso-social-buttons.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, AsyncPipe, NgTemplateOutlet, IconComponent, NumberIntoAbbreviatedStringPipe],
})
export class NsoSocialButtonsComponent implements AfterViewInit, OnChanges {
  @ViewChild('copyUrlButton') copyUrlButton?: ElementRef<HTMLButtonElement>;

  @Input()
  set link(link: string[]) {
    // Shouldn't duplicate the first slash in the URL
    link[0] = '';
    this.shareUrl = this.seoService.hostUrl + link.join('/');
  }

  @Input() likeCount: number = 0;
  @Input() isReactionDisabled = false;
  @Input() isAlreadyReacted = false;

  @Output() reactionClicked = new EventEmitter();

  constructor(
    private readonly seoService: SeoService,
    private readonly clipboard: Clipboard,
    private readonly utilsService: UtilService,
    private readonly colorChangeService: ColorChangeService
  ) {}

  shareUrl: string = '';
  facebookUrl: string = '';
  twitterUrl: string = '';
  urlCopied$?: Observable<Event>;
  urlCopiedSubject$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  firstClick = true;

  readonly isAlternativeColor = computed(() => this.colorChangeService.isCsupasport() || this.colorChangeService.isHatsofuves());

  ngOnChanges(changes: SimpleChanges): void {
    const link = changes['link']?.currentValue;
    if (link?.length) {
      this.getSocialShareUrls(this.shareUrl);
    }
  }

  handleReaction(): void {
    if (this.isReactionDisabled) {
      return;
    }
    this.reactionClicked.emit();
  }

  private getSocialShareUrls(articleLink: string): void {
    this.facebookUrl = getFacebookShareUrl(articleLink);
    this.twitterUrl = getTwitterShareUrl(articleLink);
  }

  onClickUrlCopy(): void {
    this.firstClick = false;
    this.clipboard.copy(this.shareUrl);
  }

  ngAfterViewInit(): void {
    if (this.utilsService.isBrowser() && this.copyUrlButton?.nativeElement)
      this.urlCopied$ = fromEvent(this.copyUrlButton?.nativeElement, 'click').pipe(
        startWith(new Event('click')),
        tap(() => {
          this.urlCopiedSubject$.next(true);
        }),
        debounceTime(1500),
        distinctUntilChanged(),
        tap(() => {
          this.urlCopiedSubject$.next(false);
        })
      );
  }
}
