<ng-container *ngIf="urlCopied$ | async"></ng-container>

<ng-container *ngTemplateOutlet="socialInteractions"></ng-container>
<button #copyUrlButton [disabled]="urlCopiedSubject$ | async" (click)="onClickUrlCopy()">
  <i class="icon icon-social-link{{ isAlternativeColor() ? '-yellow' : '' }}"></i>
</button>

<div class="social-item" *ngIf="!isReactionDisabled">
  <button class="like" [class.reacted]="isAlreadyReacted" (click)="handleReaction()">
    <kesma-icon name="nso-like" [size]="16"></kesma-icon>
    <div>{{ likeCount | numberIntoAbbreviatedString }} <span class="social-item-label">Tetszik</span></div>
  </button>
</div>

<div class="copy-container" [class.slided]="(urlCopiedSubject$ | async) && !firstClick">
  <span class="copy-container-message">Vágólapra másolva!</span>
</div>

<ng-template #socialInteractions>
  <a [href]="twitterUrl" target="_blank">
    <i class="icon icon-social-twitter{{ isAlternativeColor() ? '-yellow' : '' }}"></i>
  </a>
  <a [href]="facebookUrl" target="_blank">
    <i class="icon icon-social-facebook{{ isAlternativeColor() ? '-yellow' : '' }}"></i>
  </a>
</ng-template>
