@use 'shared' as *;

:host {
  display: flex;
  align-items: center;
  flex-direction: row;
  gap: 8px;
  flex-wrap: wrap;
  max-width: 175px;
  position: relative;

  @include media-breakpoint-down(lg) {
    max-width: 100%;
  }

  @include media-breakpoint-down(sm) {
    flex-wrap: nowrap;
  }

  .social-item {
    @include media-breakpoint-up(lg) {
      width: 100%;
      margin-top: 15px;
    }

    .like {
      font-family: var(--kui-font-primary);
      border: 1px solid var(--kui-gray-550);
      color: var(--kui-gray-550);
      transition: all 300ms;
      display: flex;
      align-items: center;
      padding: 6px 10px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 700;
      gap: 6px;

      @include media-breakpoint-down(lg) {
        justify-content: center;
        width: 48px;
        height: 48px;
        border-radius: 50%;
        gap: 2px;

        .social-item-label {
          display: none;
        }
      }

      kesma-icon {
        flex-shrink: 0;
      }

      &.reacted,
      &:hover {
        background-color: var(--kui-blue-500);
        border-color: var(--kui-blue-500);

        &,
        kesma-icon {
          color: var(--kui-white);
        }
      }
    }
  }
}

.icon {
  width: 48px;
  cursor: pointer;
  height: 48px;
}

.copy-container {
  position: fixed;
  top: 150px;
  left: -300px;
  width: 300px;
  height: auto;
  padding: 15px;
  background: var(--kui-white);
  border: 0.5px solid var(--kui-red-400);
  text-align: center;

  &-message {
    font-size: 14px;
  }

  &.slided {
    display: block;
    animation: slide 1.5s forwards;
    font-style: normal;
    font-weight: 700;
    line-height: 130%;
  }
}

@keyframes slide {
  25% {
    left: -5px;
  }
  50% {
    left: -5px;
  }
  75% {
    left: -5px;
  }
  100% {
    left: -300px;
  }
}
