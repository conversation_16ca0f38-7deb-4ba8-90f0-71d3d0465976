@use 'shared' as *;

:host {
  display: block;
  width: 100%;
  color: var(--kui-white);
  font-family: var(--kui-font-primary);
  font-size: 16px;
  line-height: 20px;
  font-weight: 700;
}

.countdown {
  background: var(--kui-red-400);
  padding: 24px;

  &-logo {
    margin-bottom: 24px;

    img {
      max-width: 100%;
    }
  }

  &-main-text {
    font-family: var(--kui-font-condensed);
    font-weight: 700;
    font-size: 32px;
    line-height: 36px;
    text-transform: uppercase;
    margin-bottom: 24px;
  }

  &-sub-text {
    margin-bottom: 12px;
  }

  &-value {
    text-transform: uppercase;
    background-color: var(--kui-white);
    border-radius: 4px;
    padding: 8px;
    color: var(--kui-red-400);
    text-align: center;
    display: block;
  }
}

.countdown-sponsor {
  background: var(--kui-gray-550);
  padding: 24px;

  &-text {
    margin-bottom: 12px;
  }

  &-logo {
    img {
      max-width: 100%;
    }
  }
}
