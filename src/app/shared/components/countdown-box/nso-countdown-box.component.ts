import { ChangeDetectionStrategy, Component } from '@angular/core';
import { CountdownBoxComponent, CountdownService } from '@trendency/kesma-ui';
import { AsyncPipe, NgIf } from '@angular/common';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'nso-countdown-box',
  templateUrl: './nso-countdown-box.component.html',
  styleUrls: ['./nso-countdown-box.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [CountdownService],
  imports: [NgIf, AsyncPipe, RouterLink],
})
export class NsoCountdownBoxComponent extends CountdownBoxComponent {}
