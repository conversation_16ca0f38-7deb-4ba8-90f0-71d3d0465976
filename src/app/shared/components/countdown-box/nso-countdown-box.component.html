<div class="countdown">
  <div *ngIf="data?.logoImage" class="countdown-logo">
    <img [src]="data?.logoImage" alt="Visszaszámlálás" loading="lazy" />
  </div>
  <div *ngIf="data?.mainText" class="countdown-main-text">{{ data?.mainText }}</div>
  <div *ngIf="data?.subText" class="countdown-sub-text">{{ data?.subText }}</div>
  <a *ngIf="countdown$ | async as countdown" [routerLink]="data?.slug" class="countdown-value">
    <ng-container *ngIf="countdown.value; else endText">
      {{ countdown.formattedValue }} <span *ngIf="data?.valueSuffixText" class="countdown-suffix">{{ data?.valueSuffixText }}</span>
    </ng-container>
    <ng-template #endText>{{ data?.endText }}</ng-template>
  </a>
</div>
<div *ngIf="data?.sponsorImage" class="countdown-sponsor">
  <div class="countdown-sponsor-text">A rovat támogatója:</div>
  <div class="countdown-sponsor-logo">
    <ng-container *ngIf="data?.sponsorUrl; else noSponsorLink">
      <a [href]="data?.sponsorUrl" target="_blank">
        <img [src]="data?.sponsorImage" alt="Szponzor logó" loading="lazy" />
      </a>
    </ng-container>
    <ng-template #noSponsorLink><img [src]="data?.sponsorImage" alt="Szponzor logó" loading="lazy" /></ng-template>
  </div>
</div>
