<ng-container *ngIf="(data ?? []).length > 0">
  <div class="articles">
    <div class="article-column" *ngFor="let articleCard of data; trackBy: trackByFn; let first = first; let last = last">
      <nso-article-card nso-article-card [data]="articleCard" [styleID]="articleCardType.FeaturedTitleArrowDate" [class.first]="first" [class.last]="last">
      </nso-article-card>
    </div>
  </div>
</ng-container>
