import { ChangeDetectionStrategy, Component } from '@angular/core';
import { ArticleCard, BaseComponent } from '@trendency/kesma-ui';
import { ArticleCardType } from '../../definitions';
import { NgForOf, NgIf } from '@angular/common';
import { NsoArticleCardComponent } from '../article-card/nso-article-card.component';

@Component({
  selector: 'nso-latest-news-block',
  templateUrl: './nso-latest-news-block.component.html',
  styleUrls: ['./nso-latest-news-block.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgForOf, NgIf, NsoArticleCardComponent],
})
export class NsoLatestNewsBlockComponent extends BaseComponent<ArticleCard[]> {
  readonly articleCardType = ArticleCardType;
}
