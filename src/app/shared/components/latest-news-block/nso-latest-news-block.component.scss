@use 'shared' as *;

:host {
  display: block;
  width: 100%;

  .articles {
    max-height: 600px;
    overflow-y: scroll;

    &::-webkit-scrollbar {
      width: 5px;

      &-track {
        background: var(--kui-white);
      }

      &-thumb {
        background: var(--kui-red-400);
      }
    }
  }

  ::ng-deep nso-article-card {
    margin-bottom: 0;
    padding-top: 16px;

    &.first {
      padding-top: 0;
    }

    a {
      h2 {
        width: 100%;
        padding-bottom: 16px;
        border-bottom: solid 1px var(--kui-gray-200);
      }

      i {
        margin-bottom: 16px;
      }
    }

    &.last {
      a {
        h2 {
          border-bottom: none;
        }

        i {
          margin-bottom: 16px;
        }
      }
    }
  }
}
