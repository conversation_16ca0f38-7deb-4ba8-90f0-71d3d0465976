import { ChangeDetectionStrategy, Component, HostBinding, Input } from '@angular/core';
import { BaseComponent } from '@trendency/kesma-ui';
import { MatchData, LatestResultsType } from '../../definitions';
import { NgForOf, NgIf, TitleCasePipe } from '@angular/common';
import { NsoSpinnerComponent } from '../spinner/nso-spinner.component';
import { FormatPipeModule } from 'ngx-date-fns';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'nso-latest-results',
  templateUrl: './nso-latest-results.component.html',
  styleUrls: ['./nso-latest-results.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NsoSpinnerComponent, NgForOf, FormatPipeModule, TitleCasePipe, RouterLink],
})
export class NsoLatestResultsComponent extends BaseComponent<MatchData[] | undefined | null> {
  @HostBinding('class') hostClass = '';

  @Input() blockTitle?: string;

  @Input() set styleID(styleID: LatestResultsType) {
    this.type = styleID;
    this.hostClass = `style-${LatestResultsType[styleID]}`;
  }

  public type: LatestResultsType = LatestResultsType.Matches;
  public LatestResultsType = LatestResultsType;

  public get dateFormat(): string {
    switch (this.type) {
      case LatestResultsType.Matches:
        return 'MMM d. EEEE HH:mm';
      default:
        return 'MMM d. EEEE';
    }
  }
}
