@use 'shared' as *;

:host {
  display: block;
  margin-bottom: 20px;
  border: 1px solid var(--kui-gray-200);
  width: 100%;

  .match {
    padding: 20px;

    .spinner-container {
      width: 100%;
      display: flex;
      align-content: center;
    }

    .no-data-text {
      text-align: center;
      font-size: 16px;
    }

    &-title {
      padding: 16px 24px;
      font-family: var(--kui-font-condensed);
      background: var(--kui-red-400);
      color: var(--kui-white);
      font-size: 24px;
      line-height: 29px;
      text-transform: uppercase;
      font-weight: 700;
    }

    &-date {
      margin-bottom: 10px;
      font-size: 14px;
      line-height: 22px;
      font-family: var(--kui-font-primary);
      color: var(--kui-gray-550);
    }

    &-icon {
      width: 24px;
      height: 24px;
      border-radius: 50%;
    }

    &-box {
      margin-bottom: 10px;
    }

    &-data {
      display: flex;
      align-items: center;
      gap: 10px;
    }

    &-team-name {
      font-size: 16px;
      line-height: 19px;
      font-family: var(--kui-font-primary);
      color: var(--kui-gray-550);
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 10px;
      width: 100%;
      cursor: pointer;

      &.is-winner {
        font-weight: bold;
      }
    }

    &-divider {
      width: 100%;
      background-color: var(--kui-gray-200);
      height: 1px;
      margin: 20px 0;
    }

    &-link {
      font-weight: 700;
      font-size: 16px;
      line-height: 19px;
      color: var(--kui-red-400);
      margin-top: 15px;
      border-bottom: 1px solid var(--kui-red-400);
      display: inline-block;
      cursor: pointer;
    }
  }

  &.style-Matches {
    .match {
      &-team-name {
        font-weight: bold;
      }

      &-divider {
        width: calc(100% + 40px);
        margin: 20px -20px;
      }
    }
  }
}
