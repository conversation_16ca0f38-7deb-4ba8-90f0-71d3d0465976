<div class="match-title">{{ blockTitle }}</div>

<div class="match">
  <ng-container *ngIf="!data">
    <div class="spinner-container">
      <nso-spinner></nso-spinner>
    </div>
  </ng-container>
  <ng-container *ngIf="data && data.length === 0">
    <p class="no-data-text">Ni<PERSON>senek közelgő mérkőzések</p>
  </ng-container>
  <ng-container *ngFor="let item of data; last as last">
    <div class="match-date">
      {{ item?.date | dfnsFormat: dateFormat | titlecase }}
    </div>
    <ng-container *ngFor="let match of item?.matches; trackBy: trackByFn">
      <div class="match-box" *ngIf="match?.teamA as teamA">
        <div class="match-data">
          <img class="match-icon" [src]="teamA?.icon ?? '/assets/images/icons/gool.svg'" [alt]="teamA?.name" loading="lazy" />
          <div [routerLink]="['/', 'csapat', teamA?.slug, teamA?.competition?.slug ?? '']" class="match-team-name" [class.is-winner]="teamA?.isWinner">
            {{ teamA?.name }}
            <div *ngIf="type === LatestResultsType.MatchesWithScore" class="match-score">{{ teamA?.score }}</div>
          </div>
        </div>
      </div>

      <div class="match-box" *ngIf="match?.teamB as teamB">
        <div class="match-data">
          <img class="match-icon" [src]="teamB?.icon ?? '/assets/images/icons/gool.svg'" [alt]="teamB?.name" loading="lazy" />
          <div class="match-team-name" [routerLink]="['/', 'csapat', teamB?.slug, teamB?.competition?.slug ?? '']" [class.is-winner]="teamB?.isWinner">
            {{ teamB?.name }}
            <div *ngIf="type === LatestResultsType.MatchesWithScore" class="match-score">{{ teamB?.score }}</div>
          </div>
        </div>
      </div>
    </ng-container>
    <div *ngIf="!last" class="match-divider"></div>
  </ng-container>

  <!-- TODO: összes eredmény lista oldal linkje -->
  <div *ngIf="type === LatestResultsType.MatchesWithScore" class="match-link" [routerLink]="['/']">Összes eredmény</div>
</div>
