import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnDestroy, OnInit } from '@angular/core';
import { ReqService, UtilService } from '@trendency/kesma-core';
import { ApiResult, BaseComponent } from '@trendency/kesma-ui';
import { forkJoin, interval, Observable, Subject, take } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { ChampionshipSchedule, TripBoxDefinitions } from '../../definitions';
import { NsoTripBoxComponent } from '../trip-box/nso-trip-box.component';

@Component({
  selector: 'app-trip-box-adapter',
  templateUrl: 'trip-box-adapter.component.html',
  styleUrls: ['trip-box-adapter.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NsoTripBoxComponent],
})
export class TripBoxAdapterComponent extends BaseComponent<TripBoxDefinitions> implements OnInit, OnD<PERSON>roy {
  matches: ChampionshipSchedule[] = [];

  private readonly unsubscribe$: Subject<boolean> = new Subject();

  constructor(
    private readonly reqService: ReqService,
    private readonly cdr: ChangeDetectorRef,
    private readonly utilService: UtilService
  ) {
    super();
  }

  override ngOnInit(): void {
    super.ngOnInit();
    this.setMatchData();
    if (this.utilService.isBrowser()) {
      interval(60_000)
        .pipe(takeUntil(this.unsubscribe$))
        .subscribe(() => {
          this.setMatchData();
        });
    }
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next(true);
    this.unsubscribe$.complete();
  }

  private setMatchData(): void {
    const request$: Observable<ApiResult<ChampionshipSchedule>>[] = [];
    this.data?.matches?.forEach((match) => {
      request$.push(this.getMatchData(match?.id));
    });

    forkJoin(request$)
      .pipe(take(1))
      .subscribe((matches) => {
        matches?.forEach(({ data }) => {
          this.matches.push(data);
        });

        this.data = {
          ...this.data,
          matches: this.matches,
        } as TripBoxDefinitions;

        this.matches = [];
        this.cdr.detectChanges();
      });
  }

  private getMatchData(scheduleId: string): Observable<ApiResult<ChampionshipSchedule>> {
    return this.reqService.get<ApiResult<ChampionshipSchedule>>(`sport/schedule-by-id/${scheduleId}`, {
      params: {
        timestamp: Date.now(),
      },
    });
  }
}
