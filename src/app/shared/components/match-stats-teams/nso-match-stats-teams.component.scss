@use 'shared' as *;

:host {
  display: block;
  width: 100%;
  font-family: var(--kui-font-condensed);
}

::ng-deep {
  .team-stats {
    width: 100%;
    font-size: 16px;

    &-header {
      background-color: var(--kui-red-400);
      height: 61px;
      padding: 16px 32px;
      font-size: 24px;
      color: var(--kui-white);
    }

    &-score {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
    }

    tr {
      color: var(--kui-gray-550);
      td a {
        display: block;
        vertical-align: inherit;
        color: var(--kui-black);
      }
      &:nth-child(even) {
        background-color: var(--kui-gray-75);
      }
      &:hover,
      &:focus-within {
        background: var(--kui-gray-100);
        outline: none;
      }
    }

    .first-cell {
      padding-left: 16px;
    }
  }
}

.team-stats-title {
  color: var(--kui-red-400);
  font-size: 38px;
  font-weight: 700;
  margin-bottom: 32px;
}

.team-stats-name {
  color: var(--kui-black);
  font-size: 38px;
  margin-bottom: 32px;
}

.team-stats-match {
  font-weight: bold;
  color: var(--kui-black);
  padding: 8px 16px;
  text-align: center;
}

.score-win {
  color: var(--kui-green-400);
}

.score-loose {
  color: var(--kui-red-400);
}

.teams-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;

  @include media-breakpoint-down(md) {
    &.mobile {
      flex-direction: column;
      justify-content: center;
    }
  }

  &.reversed {
    flex-direction: row-reverse;
  }

  &-image {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    flex-shrink: 0;
    background-size: cover;
    background-position: center;
  }

  &-name {
    margin-right: 10px;

    @include media-breakpoint-down(md) {
      margin-right: 0;
      margin-top: 10px;
    }

    &.reversed {
      margin-left: 10px;
    }

    &.highlighted {
      font-weight: bold;
    }
  }
}

// mobile ui

.team-stats-header-mobile {
  background-color: var(--kui-red-400);
  height: 61px;
  padding: 16px 8px;
  font-size: 24px;
  color: var(--kui-white);
  font-weight: bold;
}

.team-stats-mobile {
  display: flex;
  flex-direction: column;

  &.odd {
    background-color: var(--kui-gray-75);
  }

  &-result {
    text-align: center;
    margin-top: 10px;
    font-weight: bold;
  }

  &-championship {
    margin-bottom: 10px;
    text-align: center;
  }

  &-teams {
    display: flex;
    flex-direction: row;
    justify-content: center;
    padding: 10px;
  }
}
