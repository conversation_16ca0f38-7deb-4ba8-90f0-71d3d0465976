import { ChangeDetectionStrategy, ChangeDetectorRef, Component, inject, Input, OnDestroy, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { BaseComponent, dataTableConfig, DataTableGeneratorComponent } from '@trendency/kesma-ui';
import { MatchStatsTeams } from '../../definitions';
import { debounceTime, distinctUntilChanged, fromEvent, map, of, startWith, Subject, takeUntil } from 'rxjs';
import { UtilService } from '@trendency/kesma-core';
import { NgClass, NgForOf, NgIf, NgStyle, NgTemplateOutlet, UpperCasePipe } from '@angular/common';
import { ScoreHighlightDirective } from '../../directives';

@Component({
  selector: 'nso-match-stats-teams',
  templateUrl: './nso-match-stats-teams.component.html',
  styleUrls: ['./nso-match-stats-teams.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, UpperCasePipe, DataTableGeneratorComponent, ScoreHighlightDirective, NgTemplateOutlet, NgClass, NgStyle, NgForOf],
})
export class NsoMatchStatsTeamsComponent extends BaseComponent<MatchStatsTeams> implements OnInit, OnDestroy {
  utils = inject(UtilService);
  private readonly destroy$ = new Subject<void>();

  currentWindowWidth$ = this.utils.isBrowser()
    ? fromEvent(window, 'resize').pipe(
        map(() => window.innerWidth),
        startWith(window.innerWidth)
      )
    : of(1920);

  isMobile = window.innerWidth < 450;

  @Input() showTeamName = true;

  @ViewChild('score', {
    read: TemplateRef,
    static: true,
  })
  score?: TemplateRef<HTMLElement>;

  @ViewChild('teamHome', {
    read: TemplateRef,
    static: true,
  })
  teamHome?: TemplateRef<HTMLElement>;

  @ViewChild('teamAway', {
    read: TemplateRef,
    static: true,
  })
  teamAway?: TemplateRef<HTMLElement>;

  tableConfig: dataTableConfig[] = [];

  constructor(private readonly cd: ChangeDetectorRef) {
    super();
  }

  override ngOnInit(): void {
    super.ngOnInit();
    this.currentWindowWidth$.pipe(distinctUntilChanged(), debounceTime(10), takeUntil(this.destroy$)).subscribe((width: number) => {
      this.isMobile = width < 450;
      this.cd.markForCheck();
    });
    this.tableConfig = [
      {
        hasHeader: true,
        headerTitle: 'LEGUTÓBBI ÖT MÉRKŐZÉS',
        headerColspan: 5,
        columnDataProperty: 'result',
        headerClass: 'team-stats-header',
        columnClass: 'first-cell',
        rowLinkDataProperty: 'link',
      },
      {
        hasHeader: false,
        headerColspan: 1,
        columnDataProperty: 'teamA.teamName',
        customColumnTemplate: this.teamHome,
        rowLinkDataProperty: 'link',
      },
      {
        hasHeader: false,
        headerColspan: 1,
        columnDataProperty: 'teamA.score',
        customColumnTemplate: this.score,
        rowLinkDataProperty: 'link',
      },
      {
        hasHeader: false,
        headerColspan: 1,
        columnDataProperty: 'teamB.teamName',
        customColumnTemplate: this.teamAway,
        rowLinkDataProperty: 'link',
      },
      {
        hasHeader: false,
        headerColspan: 1,
        columnDataProperty: 'championshipName',
        rowLinkDataProperty: 'link',
      },
    ];
    this.cd.detectChanges();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
