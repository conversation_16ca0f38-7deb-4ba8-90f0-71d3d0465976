<h1 class="team-stats-title" *ngIf="data?.hasTitle">{{ 'Statisztika' | uppercase }}</h1>

<h2 *ngIf="showTeamName && data?.teamName" class="team-stats-name">{{ data?.teamName }}</h2>

<ng-container *ngIf="!isMobile; else mobileUi">
  <kesma-data-table-generator [data]="data?.matches" [tableConfig]="tableConfig" [dataTableClass]="'team-stats'"></kesma-data-table-generator>
</ng-container>

<!-- Table score -->

<ng-template #score let-rowData="data">
  <section class="team-stats-match">
    <span [nsoScoreHighlight]="{ data: rowData, teamName: data?.teamName, score: rowData?.teamA?.score }">
      {{ rowData?.teamA?.score }}
    </span>

    &nbsp;-&nbsp;
    <span [nsoScoreHighlight]="{ data: rowData, teamName: data?.teamName, score: rowData?.teamB?.score }">
      {{ rowData?.teamB?.score }}
    </span>
  </section>
</ng-template>

<!-- Table teams -->

<ng-template #teamHome let-rowData="data">
  <ng-container [ngTemplateOutlet]="team" [ngTemplateOutletContext]="{ team: { data: rowData, team: rowData?.teamA, reversed: false } }"></ng-container>
</ng-template>
<ng-template #teamAway let-rowData="data">
  <ng-container [ngTemplateOutlet]="team" [ngTemplateOutletContext]="{ team: { data: rowData, team: rowData?.teamB, reversed: true } }"></ng-container>
</ng-template>

<ng-template #team let-team="team">
  <section class="teams-container" [ngClass]="{ reversed: team?.reversed }">
    <span
      class="teams-container-name"
      [ngClass]="{
        reversed: team?.reversed,
        highlighted: team?.team?.score > team?.data?.teamB?.score || team?.team?.score > team?.data?.teamA?.score,
      }"
    >
      {{ team?.team?.teamName }}
    </span>
    <div class="teams-container-image" [ngStyle]="{ 'background-image': 'url(' + team?.team?.teamLogo + ')' }"></div>
  </section>
</ng-template>

<!-- Mobile ui -->

<ng-template #mobileUi>
  <section class="team-stats-header-mobile">
    {{ 'Legutóbbi öt mérkőzés' | uppercase }}
  </section>
  <ng-container *ngFor="let match of data?.matches; let i = index; let odd = odd">
    <div class="team-stats-mobile" [class.odd]="odd">
      <section class="team-stats-mobile-result">
        {{ match?.result }}
      </section>
      <section class="team-stats-mobile-championship">
        {{ match?.championshipName }}
      </section>
      <section class="team-stats-mobile-teams">
        <ng-container [ngTemplateOutlet]="teamsMobile" [ngTemplateOutletContext]="{ team: { data: match, team: match?.teamA } }"></ng-container>
        <section class="team-stats-match">
          <span [nsoScoreHighlight]="{ data: match, teamName: data?.teamName, score: match?.teamA?.score }">
            {{ match?.teamA?.score }}
          </span>

          &nbsp;-&nbsp;
          <span [nsoScoreHighlight]="{ data: match, teamName: data?.teamName, score: match?.teamB?.score }">
            {{ match?.teamB?.score }}
          </span>
        </section>
        <ng-container [ngTemplateOutlet]="teamsMobile" [ngTemplateOutletContext]="{ team: { data: match, team: match?.teamB } }"></ng-container>
      </section>
    </div>
  </ng-container>
</ng-template>

<!-- Mobile teams template -->

<ng-template #teamsMobile let-team="team">
  <section class="teams-container mobile">
    <div class="teams-container-image" [ngStyle]="{ 'background-image': 'url(' + team?.team?.teamLogo + ')' }"></div>
    <span
      class="teams-container-name"
      [ngClass]="{ highlighted: team?.team?.score > team?.data?.teamB?.score || team?.team?.score > team?.data?.teamA?.score }"
    >
      {{ team?.team?.short }}
    </span>
  </section>
</ng-template>
