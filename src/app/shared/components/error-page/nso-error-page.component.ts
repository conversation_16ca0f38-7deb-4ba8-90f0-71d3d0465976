import { ChangeDetectionStrategy, Component } from '@angular/core';
import { NsoButtonComponent } from '../button/nso-button.component';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'nso-error-page',
  templateUrl: './nso-error-page.component.html',
  styleUrls: ['./nso-error-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NsoButtonComponent, RouterLink],
})
export class NsoErrorPageComponent {}
