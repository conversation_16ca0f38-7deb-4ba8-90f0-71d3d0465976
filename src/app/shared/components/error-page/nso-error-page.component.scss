@use 'shared' as *;

:host {
  nso-button {
    max-width: 300px;
    margin: 0 auto;
  }
}

.error-page {
  margin: 0 auto;
  max-width: 468px;

  .icon-ball-404 {
    margin: 0 auto;
    display: block;
    width: 222px;
    height: 138px;

    @include media-breakpoint-down(md) {
      width: 176px;
      height: 109px;
    }
  }

  &-title {
    font-family: var(--kui-font-condensed);
    font-weight: 700;
    color: var(--kui-gray-500);
    font-size: 38px;
    line-height: 41px;
    text-align: center;
    margin-top: 40px;
    text-transform: uppercase;
    margin-bottom: 20px;

    @include media-breakpoint-down(md) {
      font-size: 32px;
      line-height: 35px;
      margin: 32px 0 34px;
    }
  }

  &-text {
    font-family: var(--kui-font-primary);
    font-weight: 400;
    font-size: 16px;
    line-height: 25px;
    color: var(--kui-black);
    text-align: center;
    margin-bottom: 40px;
  }
}
