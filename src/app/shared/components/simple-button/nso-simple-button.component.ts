import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconComponent, SimpleButtonComponent } from '@trendency/kesma-ui';
import { Ng<PERSON><PERSON>, NgIf, Ng<PERSON>tyle, NgTemplateOutlet } from '@angular/common';

@Component({
  selector: 'nso-simple-button',
  templateUrl: '../../../../../node_modules/@trendency/kesma-ui/src/lib/components/simple-button/simple-button.component.html',
  styleUrls: [
    '../../../../../node_modules/@trendency/kesma-ui/src/lib/components/simple-button/simple-button.component.scss',
    './nso-simple-button.component.scss',
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgStyle, NgTemplateOutlet, NgIf, IconComponent, NgClass],
})
export class NsoSimpleButtonComponent extends SimpleButtonComponent {}
