@use 'shared' as *;

:host {
  --button-bg-primary: var(--kui-red-400);
  --button-bg-secondary: var(--kui-gray-550);
}

button {
  &[disabled] {
    background-color: var(--kui-gray-450);
    color: var(--kui-gray-300);
  }

  &:not([disabled]):hover {
    background-color: var(--kui-red-350);
  }

  &:not([disabled]):focus {
    border: 1px solid var(--kui-gray-200);
  }
}

button.btn-primary {
  border: 1px solid var(--kui-red-400);
  background: var(--kui-red-400);
  &[disabled] {
    border: 1px solid var(--kui-gray-450);
    color: var(--kui-gray-300);
  }
}

button.btn-secondary {
  border: 1px solid var(--kui-gray-550);
  background: var(--kui-gray-550);
  color: var(--kui-white, #fff);

  &:not([disabled]):hover {
    border: 1px solid var(--kui-red-350);
  }
}

button.btn-link {
  color: var(--kui-red-400);
  text-decoration: underline;
  font-weight: 700;
  font-size: 16px;
  line-height: 120%;
  letter-spacing: 0.01em;
  border: none !important;
}
