@use 'shared' as *;

:host {
  display: block;
  text-align: center;

  .button {
    background: var(--kui-red-400);
    border-radius: 5px;
    text-transform: uppercase;
    font-family: var(--kui-font-condensed);
    font-size: 22px;
    padding: 15px 55px;
    color: var(--kui-white);
    font-weight: 700;
    line-height: 130%;
    width: 100%;
    height: 51px;
    display: flex;
    align-items: center;
    justify-content: center;

    @include media-breakpoint-down(sm) {
      font-size: 18px;
      padding: 15px;
    }

    &.disabled {
      background: var(--kui-gray-450);
      color: var(--kui-gray-300);
    }
  }
}
