@use 'shared' as *;

.round-select {
  padding: 8px 20px 8px;
  min-width: 160px;
  border-radius: 20px;
  color: white;
  -webkit-appearance: none;
  -moz-appearance: none;
  background: transparent url('../../../../assets/images/icons/arrow-down-white.svg') no-repeat;
  background-position-x: 90%;
  background-position-y: 12px;
  font-weight: 700;
  font-size: 16px;
  line-height: 120%;
  option {
    background-color: var(--kui-gray-450);
  }
}

.scorestrip {
  background: var(--kui-gray-450);
  .wrapper {
    display: flex;
    justify-content: space-between;

    .left {
      width: 206px;
      background: var(--kui-gray-450);
      display: flex;
      align-items: center;
      justify-content: flex-end;
      padding-right: 20px;
      .white-rounded {
        width: 181px;
      }
    }

    .slider-wrapper {
      background: var(--kui-gray-550);

      .scorestrip-slider {
        display: flex;
        .score-card {
          font-family: var(--kui-font-primary);
          padding: 12px;
          color: var(--kui-white);
          width: 200px;
          height: 140px;
          border-right: 1px solid var(--kui-gray-450);

          .score-card-top {
            margin-bottom: 16px;
            .score-card-top-standard {
              font-weight: 700;
              .date {
                font-weight: 400;
                font-size: 14px;
                color: var(--kui-gray-300);
                &.date-name {
                  font-weight: 400;
                  font-size: 14px;
                  color: var(--kui-gray-300);
                  display: inline-block;
                  margin-left: 5px;
                }
              }
            }

            .score-card-top-live {
              color: var(--kui-red-400);
              font-weight: 700;
              padding-left: 16px;
              position: relative;
              &:before {
                content: ' ';
                width: 6px;
                height: 6px;
                position: absolute;
                background: var(--kui-red-400);
                left: 0;
                top: calc(50% - 3px);
                border-radius: 50%;
              }
            }
          }

          .score-card-content-row {
            display: flex;
            align-items: center;
            width: 168px;
            justify-content: space-between;
            margin-top: 10px;
            font-size: 16px;
            font-weight: 700;

            .team {
              display: flex;
              align-items: center;
              .team-logo {
                width: 32px;
                height: 32px;
                border-radius: 50%;
                background-position: center;
                background-size: cover;
                margin-right: 8px;
              }
            }
          }
        }
      }
    }

    .right {
      width: 82px;
      .arrow-left,
      .arrow-right {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 82px;
        height: 70px;
        background: var(--kui-gray-450);
        border-left: 1px solid var(--kui-gray-350);
        border-right: 1px solid var(--kui-gray-350);
        i {
          width: 32px;
          height: 32px;
          display: block;
        }
      }

      .arrow-left {
        border-bottom: 1px solid var(--kui-gray-350);
      }
    }

    /* TEMPLATE LITE */
    &.lite {
      .left,
      .right {
        display: none;
      }
      .slider-wrapper {
        width: 100%;
        .scorestrip-slider {
          .score-card {
            .score-card-top {
              .date {
                &.date-name[class],
                &:before {
                  display: none;
                }
              }
            }
            .score-card-content-row {
              width: 121px;
            }
          }
        }
      }
    }
  }
}
