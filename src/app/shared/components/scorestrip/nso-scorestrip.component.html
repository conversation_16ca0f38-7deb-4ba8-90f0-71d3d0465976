<section class="scorestrip">
  <div class="wrapper">
    <div class="left">
      <div class="select-wrapper">
        <select #round class="round-select" id="city" (change)="roundChangeEvent.emit(round.value)">
          <option *ngFor="let round of rounds" class="round-select-option" [ngValue]="round.id" [selected]="round.id === initialRound.id">
            {{ round.title }}
          </option>
        </select>
      </div>
    </div>
    <div class="slider-wrapper">
      <div class="scorestrip-slider">
        <ng-container *ngFor="let scoreData of data?.list">
          <div class="score-card">
            <div class="score-card-top">
              <p class="score-card-top-standard" *ngIf="!scoreData?.live">
                Vége&nbsp;
                <span class="date">/ Jan 24.</span>
                <span class="date date-name">(Vasárnap)</span>
              </p>
              <p class="score-card-top-live" *ngIf="scoreData?.live">Élő</p>
            </div>
            <ng-container *ngFor="let team of scoreData.teams">
              <div class="score-card-content-row">
                <div class="team">
                  <div class="team-logo" [ngStyle]="{ 'background-image': 'url(' + team.image + ')' }"></div>
                  <p class="team-name">{{ team.name }}</p>
                </div>
                <div class="team-score">{{ team.score }}</div>
              </div>
            </ng-container>
          </div>
        </ng-container>
      </div>
    </div>
    <div class="right">
      <button class="arrow-left"><i class="icon icon-arrow-right"></i></button>
      <button class="arrow-right"><i class="icon icon-arrow-left"></i></button>
    </div>
  </div>
</section>
