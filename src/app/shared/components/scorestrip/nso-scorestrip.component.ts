import { ChangeDetectionStrategy, Component, Input, Output, EventEmitter } from '@angular/core';
import { BaseComponent } from '@trendency/kesma-ui';
import { NgForOf, NgIf, NgStyle } from '@angular/common';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'nso-scorestrip',
  templateUrl: './nso-scorestrip.component.html',
  styleUrls: ['./nso-scorestrip.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgForOf, FormsModule, NgIf, NgStyle],
})
export class NsoScorestripComponent extends BaseComponent<any> {
  @Input() rounds: any[] = [];
  @Input() initialRound: any;
  @Output() roundChangeEvent = new EventEmitter<string>();
}
