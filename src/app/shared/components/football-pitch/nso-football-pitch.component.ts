import { ChangeDetectionStrategy, Component, Input, OnInit } from '@angular/core';
import { availableFormations } from './nso-football-pitch.utils';
import { FootballPitchData, FootballPitchFormation, Player } from '../../definitions';
import { BaseComponent } from '@trendency/kesma-ui';
import { Ng<PERSON>orOf, Ng<PERSON><PERSON>, Ng<PERSON><PERSON>le, NgTemplateOutlet } from '@angular/common';

const nameHeight = 34; // Height of name below the player position

@Component({
  selector: 'nso-football-pitch',
  templateUrl: './nso-football-pitch.component.html',
  styleUrls: ['./nso-football-pitch.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgTemplateOutlet, NgForOf, NgStyle, NgIf],
})
export class NsoFootballPitchComponent extends BaseComponent<FootballPitchData> implements OnInit {
  @Input() iconWidth = 50;
  @Input() iconHeight = 50;
  @Input() desktopPitch = './assets/images/football-pitch/football-pitch.jpg';
  @Input() homeJersey = 'https://www.svgrepo.com/show/349227/halves-white-black-football-shirt.svg';
  @Input() awayJersey = 'https://www.svgrepo.com/show/349266/sleeves-white-blue-football-shirt.svg';

  homeSelectedFormation?: FootballPitchFormation;
  awaySelectedFormation?: FootballPitchFormation;

  readonly nameHeight = nameHeight;

  override ngOnInit(): void {
    this.iconHeight += nameHeight / 2;
  }

  protected override setProperties(): void {
    this.homeSelectedFormation = availableFormations.find((formation) => formation.formationName === this.data?.homeTeam.lineUp);
    this.awaySelectedFormation = availableFormations.find((formation) => formation.formationName === this.data?.awayTeam.lineUp);
  }

  public removeCSPlayers(players: Player[] | undefined): Player[] {
    return this.sortPlayersByPosition(players?.filter((player) => player?.fieldPosition !== 'CS') || []);
  }

  public sortPlayersByPosition(players: Player[]): Player[] {
    return players?.sort(function (a, b) {
      const keyA = +a?.fieldPosition,
        keyB = +b?.fieldPosition;
      if (keyA < keyB) return -1;
      if (keyA > keyB) return 1;
      return 0;
    });
  }

  public getPositionY(posY: string): string {
    return `calc(${posY} - ${this.iconHeight / 2}px)`;
  }

  public getXAxis(direction: string, value: string): { [x: string]: string } {
    return { [direction]: this.getPositionX(value) };
  }

  public getPositionX(posX: string): string {
    return `calc(${posX} - ${this.iconWidth / 2}px)`;
  }
}
