@use 'shared' as *;

:host {
  display: block;
  position: relative;
  overflow-x: auto;
  margin-bottom: 30px;

  @include media-breakpoint-down(md) {
    overflow-x: unset;
  }

  .football-pitch {
    position: relative;
    min-width: 900px;

    @include media-breakpoint-down(md) {
      transform: rotate(90deg) scale(1.5);
      display: flex;
      min-width: auto;
      margin-top: 40%;
    }
  }

  .position {
    position: absolute;

    @include media-breakpoint-down(md) {
      transform: rotate(-90deg) scale(0.5);
    }

    .img-wrapper {
      position: relative;

      @include media-breakpoint-down(md) {
        height: 40px;
      }

      .jersey {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-weight: bold;
        font-size: 20px;

        @include media-breakpoint-down(md) {
          font-size: 14px;
        }
      }
    }

    .name {
      font-weight: bold;
      background: var(--kui-black);
      color: var(--kui-white);
      font-size: 12px;
      padding: 3px 6px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      margin-top: -10px;
      z-index: 10;
      position: relative;
    }
  }

  &::-webkit-scrollbar {
    height: 12px;
    background: var(--kui-gray-200);
  }

  &::-webkit-scrollbar-thumb {
    background: var(--kui-red-400);
  }
}
