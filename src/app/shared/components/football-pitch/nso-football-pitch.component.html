<section class="football-pitch">
  <img [src]="desktopPitch" loading="lazy" alt="Football Pálya" class="football-pitch-img" />

  <ng-container
    *ngTemplateOutlet="
      formationTemplate;
      context: {
        $implicit: homeSelectedFormation,
        players: removeCSPlayers(data?.homeTeam?.players),
        jersey: homeJersey,
        direction: 'left',
      }
    "
  >
  </ng-container>

  <ng-container
    *ngTemplateOutlet="
      formationTemplate;
      context: {
        $implicit: awaySelectedFormation,
        players: removeCSPlayers(data?.awayTeam?.players),
        jersey: awayJersey,
        direction: 'right',
      }
    "
  >
  </ng-container>
</section>

<ng-template #formationTemplate let-formation let-players="players" let-direction="direction" let-jersey="jersey">
  <div
    class="position"
    *ngFor="let position of formation?.positions; index as index"
    [style.top]="getPositionY(position.posY)"
    [ngStyle]="getXAxis(direction, position.posX)"
    [style.width.px]="iconWidth"
    [style.height.px]="iconHeight"
  >
    <ng-container *ngIf="players[index] as player">
      <div class="img-wrapper">
        <img [src]="jersey" loading="lazy" alt="" />
        <div class="jersey">{{ player?.jersey }}</div>
      </div>

      <div class="name" [style.width.px]="iconWidth + 20" [style.margin-left.px]="-10" [style.min-height.px]="nameHeight" *ngIf="player?.lastName as lastName">
        {{ lastName }}<br />{{ player?.firstName }}
      </div>
    </ng-container>
  </div>
</ng-template>
