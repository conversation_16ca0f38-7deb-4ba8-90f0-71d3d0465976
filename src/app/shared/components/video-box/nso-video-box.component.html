<nso-block-title-row *ngIf="blockTitle" [data]="blockTitle"></nso-block-title-row>

<div class="articles">
  <div class="articles-main">
    <ng-container *ngIf="data?.[0] as bigArticle">
      <nso-video-card [styleID]="VideoCardType.FeaturedSidedImgTitleLead" [data]="bigArticle"> </nso-video-card>
    </ng-container>
  </div>
  <div class="articles-recommender">
    <ng-container *ngFor="let article of data | slice: 1 : count; trackBy: trackByFn">
      <nso-video-card [styleID]="VideoCardType.FeaturedTitleInsideImg" [data]="article"> </nso-video-card>
    </ng-container>
  </div>
</div>
