import { ChangeDetectionStrategy, Component, HostBinding, Input } from '@angular/core';
import { BaseComponent, BlockTitle, VideoCard } from '@trendency/kesma-ui';
import { VideoCardType } from '../../definitions';
import { NsoBlockTitleRowComponent } from '../block-title-row/nso-block-title-row.component';
import { NgForOf, NgIf, SlicePipe } from '@angular/common';
import { NsoVideoCardComponent } from '../video-card/nso-video-card.component';

@Component({
  selector: 'nso-video-box',
  templateUrl: 'nso-video-box.component.html',
  styleUrls: ['nso-video-box.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NsoBlockTitleRowComponent, NgIf, NsoVideoCardComponent, NgForOf, SlicePipe],
})
export class NsoVideoBoxComponent extends BaseComponent<VideoCard[]> {
  @HostBinding('class') hostClass = '';

  @Input() blockTitle?: BlockTitle;
  /**
   * Number of displayed video cards.
   */
  public count = 4;

  public VideoCardType = VideoCardType;
}
