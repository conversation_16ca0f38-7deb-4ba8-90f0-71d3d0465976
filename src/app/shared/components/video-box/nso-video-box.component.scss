@use 'shared' as *;

:host {
  display: block;
  background-color: var(--kui-gray-550);
  padding: 40px;
  width: 100%;
  position: relative;

  @include media-breakpoint-down(md) {
    padding: 48px 32px 80px 32px;
  }

  .articles {
    margin-top: 32px;

    &-main {
      @include media-breakpoint-down(md) {
        // Can't use @extend inside @include.
        ::ng-deep {
          .icon-play {
            width: 32px;
            height: 32px;
          }
        }
      }
    }

    &-recommender {
      @extend %icon-mobile;
      display: flex;
      gap: 24px;

      @include media-breakpoint-down(md) {
        margin-bottom: 20px;
        flex-direction: column;
        gap: 16px;
      }
    }
  }

  ::ng-deep {
    nso-block-title-row {
      .block-container > .block-title {
        color: var(--kui-white);
        font-family: var(--kui-font-condensed);
      }

      .heading-line-link > .heading-line-title {
        color: var(--kui-white);
        text-align: center;
        padding: 0;
      }

      @include media-breakpoint-down(md) {
        .heading-line-right > .heading-line-right-more {
          position: absolute;
          text-align: center;
          margin-bottom: 48px;
          border-bottom: none;
          left: 0;
          right: 0;
          bottom: 0;
        }
      }
    }

    // nso-video-card {
    //   margin-bottom: 0;
    //   width: calc(100% - 15px);
    // }

    // nso-side-article-card {
    //   .article-card {
    //     &-title,
    //     &-lead {
    //       color: var(--kui-white) !important;
    //     }
    //   }
    // }
  }
}

%icon-mobile {
  ::ng-deep {
    .icon-play {
      width: 32px;
      height: 32px;
    }
  }
}
