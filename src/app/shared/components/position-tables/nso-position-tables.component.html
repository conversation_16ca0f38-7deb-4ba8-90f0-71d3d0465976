<ng-template #actionHeader>
  <header class="position-tables-first-header">
    {{ data?.playerTypeName | uppercase }}
    <!-- Will be used later: -->
    <!--    <i class="position-tables-icon icon-action-question"></i>-->
  </header>
</ng-template>

<ng-template #actionsAndSubstitutions let-rowData="data">
  <section class="position-tables-actions-container" [ngClass]="{ 'has-subbed-player': rowData?.hasSubbed }">
    <div class="position-tables-player-container">
      <span class="position-tables-player-name">{{ rowData?.name }}</span>
      <ng-container *ngIf="rowData?.hasYellowCard">
        <i class="position-tables-icon icon-yellowcard"></i>
      </ng-container>
    </div>

    <ng-container *ngIf="rowData?.actions?.length">
      <div class="position-tables-actions-container-inner">
        <ng-container *ngFor="let action of rowData?.actions">
          <ng-container [ngSwitch]="action">
            <i class="position-tables-icon icon-redcard" *ngSwitchCase="actionTypes.REDCARD"></i>
            <i class="position-tables-icon icon-yellowcard" *ngSwitchCase="actionTypes.YELLOWCARD"></i>
            <i class="position-tables-icon icon-goal" *ngSwitchCase="actionTypes.GOAL"></i>
          </ng-container>
        </ng-container>
      </div>
    </ng-container>
  </section>
  <ng-container *ngIf="rowData?.hasSubbed">
    <span class="substition"
      >{{ rowData?.substitionInfo?.date }}`
      <i class="position-tables-icon-change icon-change-colorful"></i>
      {{ rowData?.substitionInfo?.newPlayer }}</span
    >
  </ng-container>
</ng-template>

<kesma-data-table-generator [data]="data?.players" [tableConfig]="tableConfig" [dataTableClass]="'position-tables'"></kesma-data-table-generator>
