import { ChangeDetectionStrategy, Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { PlayerActions } from '../../definitions';
import { BaseComponent, dataTableConfig, DataTableGeneratorComponent } from '@trendency/kesma-ui';
import { Ng<PERSON><PERSON>, NgForOf, NgI<PERSON>, NgSwitch, NgSwitchCase, UpperCasePipe } from '@angular/common';

@Component({
  selector: 'nso-position-tables',
  templateUrl: './nso-position-tables.component.html',
  styleUrls: ['./nso-position-tables.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [UpperCasePipe, NgClass, NgI<PERSON>, NgF<PERSON>O<PERSON>, NgS<PERSON>, NgSwitchCase, DataTableGeneratorComponent],
})
export class NsoPositionTablesComponent extends BaseComponent<any> implements OnInit {
  @ViewChild('actionHeader', {
    read: TemplateRef,
    static: true,
  })
  actionHeader?: TemplateRef<HTMLElement>;

  @ViewChild('actionsAndSubstitutions', {
    read: TemplateRef,
    static: true,
  })
  actionsAndSubstitutions?: TemplateRef<HTMLElement>;

  actionTypes = PlayerActions;
  tableConfig: dataTableConfig[] = [];

  override ngOnInit(): void {
    super.ngOnInit();
    this.tableConfig = [
      {
        hasHeader: true,
        headerColspan: 1,
        columnDataProperty: 'name',
        headerClass: 'position-tables-header',
        customHeaderTemplate: this.actionHeader,
        columnClass: 'first-column',
        customColumnTemplate: this.actionsAndSubstitutions,
      },
    ];
  }
}
