@use 'shared' as *;

:host::ng-deep {
  display: block;
  width: 100%;
  font-family: var(--kui-font-condensed);

  .position-tables {
    width: 100%;
    border: 1px solid var(--kui-gray-325);
    font-size: 14px;

    .table-sub-title {
      padding-left: 0px;
    }

    .first-column {
      padding: 0px 17px;
      max-width: 100px;
    }
    &-icon {
      width: 16px;
      height: 16px;
    }

    .icon {
      width: 20px;
      height: 20px;
      margin: 0px 3px;

      @include media-breakpoint-down(md) {
        width: 16px;
        height: 16px;
      }
    }

    tr {
      height: 61px;
      color: var(--kui-gray-550);
    }

    tr:nth-child(even) {
      background-color: var(--kui-gray-75);
    }

    &-header {
      background-color: var(--kui-red-400);
      height: 61px;
      color: var(--kui-white);
      font-size: 18px;
    }

    &-first-header {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      padding: 0px 17px;
    }

    &-icon {
      @extend .icon;
    }

    &-icon-change {
      width: 35px;
      height: 11px;
      margin: 0px 5px;

      @include media-breakpoint-down(md) {
        min-width: 16px;
        min-height: 16px;
      }
    }

    &-player-container {
      display: flex;
      justify-content: center;
      align-items: center;
    }

    &-actions-container {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
    }

    &-actions-container-inner {
      display: flex;
      align-items: center;

      @include media-breakpoint-down(md) {
        flex-wrap: wrap;
        gap: 5px;
      }
    }

    .has-subbed-player {
      margin-top: 34px;
    }

    .substition {
      margin-top: 14px;
      margin-left: 8px;
      display: flex;
      align-items: center;
      white-space: nowrap;
      margin-bottom: 6px;
    }
  }
}
