@use 'shared' as *;

.block-content::ng-deep {
  .raw-html-embed {
    // Migrated contents sometimes have absolute iframe.
    position: relative;
    min-height: 400px;

    @include media-breakpoint-down(sm) {
      min-height: 100%;
    }
  }

  a {
    color: var(--kui-red-400);
    font-weight: normal;
  }

  figure.image {
    margin-bottom: 10px;

    &.image-style-align-left {
      position: relative;

      figcaption {
        max-width: 200px;
      }
    }

    &.image-style-align-right {
      position: relative;

      @include media-breakpoint-down(sm) {
        margin: auto auto 10px 10px;
      }

      figcaption {
        max-width: 200px;
      }
    }

    figcaption {
      font-size: 12px;
      line-height: 18px;
      max-width: 800px;
      text-align: center;
      margin: auto;

      &:before {
        content: none;
      }
    }
  }

  .custom-text-style {
    &.underlined-text {
      text-decoration-color: var(--kui-black);
      text-decoration-thickness: 1px;
      display: inline-block;
    }

    &.highlight {
      background: var(--kui-red-400);
      color: var(--kui-white);
      padding: 12px;

      p {
        line-height: 54px;
        font-size: 24px;
        letter-spacing: 0.48px;
      }

      a {
        color: var(--kui-white);
      }
    }

    &.quote {
      background: var(--kui-gray-100);
      color: var(--kui-gray-600);
      padding: 12px 24px;
      font-size: 24px;
      line-height: 38px;
      font-weight: 400;
      font-family: var(--kui-font-primary);
      display: flex;
      font-style: italic;
      justify-content: center;
      align-items: center;
      border: none;
      gap: 16px;

      &::after,
      &::before {
        font-size: 48px;
        color: var(--kui-gray-600);
        line-height: 72px;
        font-weight: 400;
        display: block;
      }

      &::after {
        content: '”';
      }

      &::before {
        content: '“';
      }

      .quoteBlock-content p:before,
      .quoteBlock-content p:after {
        content: none;
      }
    }
  }

  h2,
  h3,
  h4 {
    margin-bottom: 30px;
    font-weight: bold;
  }

  h2 {
    font-size: 38px;
    font-weight: 700;
    line-height: 42px;
    color: var(--kui-gray-500);

    @include media-breakpoint-down(sm) {
      font-size: 26px;
      line-height: 24px;
    }
  }

  h3 {
    font-size: 24px;
    line-height: 31px;

    @include media-breakpoint-down(sm) {
      font-size: 20px;
      line-height: 19px;
    }
  }

  h4 {
    font-size: 18px;
    text-decoration: none;
    line-height: 23px;

    @include media-breakpoint-down(sm) {
      font-size: 16px;
      line-height: 19px;
    }
  }
  .table {
    display: table;
    margin: auto;

    > figcaption {
      display: table-caption;
      caption-side: top;
      border: 1px solid var(--kui-gray-300);
      border-bottom: none;
      padding: 10px;
      font-weight: 600;
      text-transform: uppercase;
    }

    &.table-style-align-right {
      @include media-breakpoint-down(md) {
        margin-left: 0px;
      }
    }

    table {
      table-layout: fixed;
      width: 100%;
      margin-bottom: 10px;
      overflow-wrap: initial;
      border: 1px solid var(--kui-gray-300);

      @include media-breakpoint-down(sm) {
        display: block; // to allow scrollbars on mobile view.
      }
    }
    tr:nth-child(even) {
      background-color: var(--kui-gray-100);
    }
    tr {
      border-top: 1px solid var(--kui-gray-300);

      td {
        figure {
          @include media-breakpoint-down(sm) {
            width: 100%;
          }
        }
      }

      td,
      th {
        background-color: initial;
        padding: 7px 10px;
        border: none;
        font-size: 16px;
        overflow-wrap: break-word;
        p {
          font-size: 16px;
        }
      }
    }
  }
}
