<footer class="footer">
  <div class="footer-wrapper">
    <div class="footer-top">
      <i class="icon icon-logo-red"></i>
      <div class="social-icons-wrapper">
        <a href="https://www.instagram.com" target="_blank"><i class="icon icon-social-insta-mini mini-icon"></i></a>
        <a href="https://www.facebook.com/nsonline" target="_blank"><i class="icon icon-social-facebook-mini mini-icon"></i></a>
        <a href="https://www.youtube.com/@NemzetiSportOnline" target="_blank"><i class="icon icon-social-youtube-mini mini-icon"></i></a>
      </div>
    </div>

    <div class="footer-menu">
      <div class="footer-menu-column" *ngFor="let menu of data; let i = index">
        <a class="footer-lead" (click)="toggleSubmenu(i)">{{ menu.title }} <i *ngIf="menu.hasSubItems" class="icon icon-chevron2"></i> </a>

        <div class="footer-menu-content" [ngClass]="{ opened: menuStates[i] }">
          <ul class="footer-menu-list">
            <li class="footer-menu-item" *ngFor="let submenu of menu.children">
              <a class="footer-menu-link" [routerLink]="submenu.link">{{ submenu.title }}</a>
            </li>
          </ul>
        </div>
      </div>

      <div class="footer-menu-column-fix">
        <ul class="footer-menu-list">
          <li class="footer-menu-item" *ngFor="let item of fixMenus">
            <a class="footer-menu-link" [href]="item.link">{{ item.item }}</a>
          </li>
        </ul>
      </div>
    </div>

    <div class="jump-top" (click)="onClickJumpToTop()">
      <span class="jump-top-text">Ugrás az oldal tetejére</span>
      <i class="icon icon-chevron"></i>
    </div>
    <span class="copyright">A Nemzeti Sport Online kiadója a Mediaworks Hungary Zrt. © Minden jog fenntartva</span>
  </div>
</footer>
