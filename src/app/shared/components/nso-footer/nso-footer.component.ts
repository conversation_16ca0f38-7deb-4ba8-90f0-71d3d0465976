import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';
import { SimplifiedMenuItem } from '@trendency/kesma-ui';
import { NgClass, NgForOf, NgIf } from '@angular/common';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'nso-footer',
  templateUrl: './nso-footer.component.html',
  styleUrls: ['./nso-footer.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgForOf, NgIf, NgClass, RouterLink],
})
export class NsoFooterComponent {
  @Input() data: SimplifiedMenuItem[] = [];
  @Output() jumpToTop = new EventEmitter<void>();

  fixMenus = [
    { item: 'Adatvédelemi Tájékoztató', link: '#' },
    { item: 'Felhasználási Feltételek', link: '#' },
    { item: 'Impresszum', link: '#' },
    { item: 'Hirdetési ÁSZF', link: '#' },
    { item: 'Előfizetői ászf', link: '#' },
  ];

  menuStates: boolean[] = [];

  toggleSubmenu(index: number): void {
    this.menuStates[index] = !this.menuStates[index];
  }

  onClickJumpToTop(): void {
    this.jumpToTop.emit();
  }
}
