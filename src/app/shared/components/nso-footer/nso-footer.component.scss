@use 'shared' as *;

:host {
  display: block;
  width: 100%;
}

.footer {
  font-family: var(--kui-font-primary);
  background-color: var(--kui-gray-550);
  width: 100%;
  padding: 60px 0 38px 0;
  margin-top: 120px;
  display: flex;
  align-items: center;

  @include media-breakpoint-down(sm) {
    padding: 50px 0px 32px 0px;
  }

  &.icon-chevron {
    width: 42px;
    height: 42px;
  }
  &.icon-chevron2 {
    width: 24px;
    height: 24px;
    float: right;
    margin-top: 3px;
    margin-right: 20px;
    display: none;
    @include media-breakpoint-down(sm) {
      display: block;
    }
  }

  &-wrapper {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding-left: 20px;
    padding-right: 20px;
    @media screen and (max-width: 1250px) {
      padding-left: 20px;
      padding-right: 20px;
    }
  }

  .footer-top {
    display: flex;
    justify-content: space-between;
    margin-bottom: 50px;
  }

  .mini-icon {
    width: 24px;
    height: 24px;
  }

  .social-icons-wrapper {
    width: 112px;
    display: flex;
    justify-content: space-between;
  }

  .copyright {
    font-weight: 400;
    font-size: 16px;
    color: var(--kui-gray-300);

    @include media-breakpoint-down(sm) {
      text-align: center;
      font-size: 12px;
      font-weight: 400;
      line-height: 16px;
      display: block;
      margin-top: 24px;
    }
  }
}

.icon-logo-red {
  width: 76px;
  height: 34px;
}

.footer-lead {
  cursor: pointer;
  color: var(--kui-red-400);
  width: 100%;
  margin: 10px 0;
  i {
    height: 24px;
    width: 24px;
    display: none;
  }
  @include media-breakpoint-down(sm) {
    color: var(--kui-white);
    font-size: 24px;
    line-height: 28px;
    text-transform: uppercase;
    display: flex;
    justify-content: space-between;
    i {
      display: block;
      height: 24px;
      width: 24px;
    }
  }
}

.jump-top {
  display: flex;
  justify-content: flex-end;
  padding-bottom: 35px;
  cursor: pointer;

  @include media-breakpoint-down(sm) {
    display: none;
  }

  &-text {
    color: var(--kui-white);
    margin: auto 10px;
    font-weight: 700;
    font-size: 16px;
  }
}

.footer-menu {
  display: grid;
  gap: 90px;
  width: 100%;
  padding: 24px 0 48px 0;

  grid-template-columns: repeat(2, 1fr);

  @include media-breakpoint-up(xs) {
    grid-template-columns: repeat(1, 1fr);
    gap: 20px;
  }
  @include media-breakpoint-up(sm) {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }
  @include media-breakpoint-up(md) {
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
  }
  @include media-breakpoint-up(lg) {
    grid-template-columns: repeat(3, 1fr);
  }
  @include media-breakpoint-up(xl) {
    display: flex;
    justify-content: space-between;
  }

  &-column {
    font-weight: 700;
    font-size: 16px;
    width: 100%;
    position: relative;
    break-inside: avoid-column;

    @include media-breakpoint-down(sm) {
      margin: 15px 5px;
    }
  }

  &-column-fix {
    font-weight: 700;
    font-size: 16px;

    @include media-breakpoint-down(sm) {
      text-align: center;
    }

    .footer-menu-list {
      @include media-breakpoint-down(sm) {
        margin-top: 48px;
      }
    }

    .footer-menu-item {
      @include media-breakpoint-down(sm) {
        display: inline-flex;
        justify-content: space-between;
        text-align: center;
        margin: 5px 0;
        padding: 0 10px;
      }
    }
    .footer-menu-link {
      @include media-breakpoint-down(sm) {
        font-size: 12px;
        color: var(--kui-gray-300);
      }
    }
  }

  &-content {
    overflow: hidden;

    @include media-breakpoint-down(sm) {
      max-height: 1vh;
      visibility: hidden;
      opacity: 0;
      transition:
        max-height 0.4s,
        opacity 0.5s linear;
      &.opened {
        visibility: visible;
        opacity: 1;
        max-height: 100vh;
      }
    }
  }

  &-list {
    padding-bottom: 30px;
    cursor: pointer;

    @include media-breakpoint-down(sm) {
      padding-bottom: 0;
    }
  }
  &-item {
    margin: 10px 0;
  }
  &-link {
    color: var(--kui-white);
  }
}
