import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { SimplifiedMenuItem, SwiperBaseComponent } from '@trendency/kesma-ui';
import { RouterLink } from '@angular/router';
import { NgForOf, NgTemplateOutlet } from '@angular/common';

@Component({
  selector: 'nso-event-header',
  templateUrl: 'nso-event-header.component.html',
  styleUrls: ['nso-event-header.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [RouterLink, NgForOf, NgTemplateOutlet],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class NsoEventHeaderComponent extends SwiperBaseComponent<SimplifiedMenuItem> {}
