@use 'shared' as *;

:host {
  width: 100%;
  display: block;
  background-color: var(--kui-blue-600);
  padding: 10px 75px;

  @include media-breakpoint-down(md) {
    padding: 10px 0 10px 17px;
  }

  .event-header {
    display: flex;
    align-items: center;
    gap: 24px;

    &-icon {
      width: 58px;
      height: 58px;
      object-fit: cover;
      border-radius: 50%;

      @include media-breakpoint-down(md) {
        width: 40px;
        height: 40px;
      }
    }

    &-link {
      color: var(--kui-white);
      font-family: var(--kui-font-condensed);
      font-size: 38px;
      text-transform: uppercase;
      line-height: 41px;
      font-weight: 700;
      white-space: nowrap;

      @include media-breakpoint-down(md) {
        font-size: 24px;
        line-height: 28px;
      }
    }

    &-divider {
      min-width: 1px;
      height: 40px;
      background-color: var(--kui-white);
    }

    &-swiper {
      display: block;
    }

    &-menu {
      position: relative;
      width: 100%;
      overflow-x: scroll;

      &::-webkit-scrollbar {
        display: none;
      }

      &-link {
        display: inline-block;
        color: var(--kui-white);
        font-size: 16px;
        line-height: 25px;
        font-weight: 400;
        font-family: var(--kui-font-primary);

        @include media-breakpoint-down(md) {
          font-size: 14px;
          line-height: 22px;
        }
      }
    }
  }

  swiper-container {
    width: 100%;
    swiper-slide {
      // display: inline-block;
      // width: fit-content;
    }
  }
}
