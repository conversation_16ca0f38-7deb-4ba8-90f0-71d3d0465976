<section class="event-header">
  <!-- TODO: update this if we know where it comes from! -->
  <img class="event-header-icon" src="https://picsum.photos/200/200" alt="" loading="lazy" />
  <a class="event-header-link" [routerLink]="data?.link">{{ data?.title }}</a>
  <div class="event-header-divider"></div>

  <div class="event-header-menu">
    <swiper-container slides-per-view="auto" class="event-header-swiper">
      <swiper-slide *ngFor="let item of data?.children">
        <ng-container *ngTemplateOutlet="!item.isCustomUrl ? normalUrl : customUrl; context: { item: item }"> </ng-container>
        <!-- TODO: no design for submenus! -->
      </swiper-slide>
    </swiper-container>
  </div>
</section>

<ng-template #normalUrl let-item="item">
  <a class="event-header-menu-link" [routerLink]="item.link" [target]="item.target"> {{ item.title }}</a>
</ng-template>
<ng-template #customUrl let-item="item">
  <a class="event-header-menu-link" [href]="item.link" [target]="item.target"> {{ item.title }}</a>
</ng-template>
