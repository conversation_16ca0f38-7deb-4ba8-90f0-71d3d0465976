<ng-container [ngSwitch]="styleID" *ngIf="data">
  <ng-container *ngSwitchCase="cardType.mainArticleDossier">
    <!--ez a kettes típusú -->
    <div class="image-wrapper">
      <img [src]="data?.mainArticle?.thumbnailUrl" [alt]="data?.mainArticle?.title || ''" [title]="data?.mainArticle?.title || ''" loading="lazy" />
      <div class="image-content">
        <a class="main-article-type" [routerLink]="tagLink">{{ data?.mainArticle?.columnTitle }}</a>
        <a class="main-article-title" [routerLink]="link">{{ data?.mainArticle?.title }}</a>
      </div>
    </div>

    <div class="secondary-list-wrapper">
      <ul *ngFor="let article of data.secondaryArticles; let i = index">
        <li *ngIf="article" class="recommender-list">
          <a [routerLink]="links[i]">{{ article.title }}</a>
        </li>
      </ul>
    </div>
    <a *ngIf="tagLink" [routerLink]="tagLink" class="recommender-more">
      <p>{{ data?.mainArticle?.columnTitle }}</p>
    </a>
  </ng-container>
</ng-container>
