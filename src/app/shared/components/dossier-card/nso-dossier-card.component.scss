@use 'shared' as *;

:host {
  display: block;
  font-family: var(--kui-font-primary);
}

.image-wrapper {
  background-size: cover;
  background-position: center;
  display: flex;
  justify-content: flex-start;
  align-items: flex-end;
  position: relative;

  &:before {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(180deg, rgba(44, 44, 44, 0) 0%, rgba(44, 44, 44, 0.5) 0% 100%);
    z-index: 1;
    content: ' ';
    display: block;
  }

  .image-content {
    padding: 24px;
    color: var(--kui-white);
    position: absolute;
    z-index: 2;
    bottom: 0;
    left: 0;
    width: 100%;
    font-weight: 700;
    font-size: 22px;
    line-height: 130%;

    .main-article-title {
      display: block;
      text-transform: none;
      text-decoration: none;
      color: var(--kui-white);
      font-family: var(--kui-font-condensed);
    }
    .main-article-type {
      display: inline-block;
      font-weight: 700;
      font-size: 16px;
      line-height: 120%;
      margin-bottom: 10px;
      padding: 4px 16px;
      background-color: var(--kui-red-400);
      text-transform: none;
      text-decoration: none;
      color: var(--kui-white);
    }
  }
}

.secondary-list-wrapper {
  background-color: var(--kui-gray-100);
  min-height: 200px;
  padding: 24px 24px 39px 24px;
  border-left: 1px solid var(--kui-gray-200);
  border-right: 1px solid var(--kui-gray-200);
  ul {
    list-style: none; /* Remove default bullets */
  }
  ul li::before {
    content: '\2022'; /* Add content: \2022 is the CSS Code/unicode for a bullet */
    color: var(--kui-red-400); /* Change the color */
    font-weight: bold; /* If you want it to be bold */
    display: inline-block; /* Needed to add space between the bullet and the text */
    width: 1em; /* Also needed for space (tweak if needed) */
    margin-left: -1em; /* Also needed for space (tweak if needed) */
  }

  li {
    padding-bottom: 16px;
    a {
      text-transform: none;
      text-decoration: none;
      color: var(--kui-black);
    }
  }
}
.recommender-more {
  display: flex;
  text-transform: none;
  text-decoration: underline;
  text-decoration-color: var(--kui-red-400);
  padding-bottom: 25px;
  padding-right: 24px;
  justify-content: flex-end;
  color: var(--kui-red-400);
  background-color: var(--kui-gray-100);
  border-left: 1px solid var(--kui-gray-200);
  border-right: 1px solid var(--kui-gray-200);
  border-bottom: 1px solid var(--kui-gray-200);
  p {
    font-size: 16px;
  }
}
