import { ChangeDetectionStrategy, Component } from '@angular/core';
import { buildArticleUrl, buildColumnUrl, DossierCardComponent } from '@trendency/kesma-ui';
import { Ng<PERSON>orO<PERSON>, NgIf, Ng<PERSON><PERSON>, NgSwitchCase } from '@angular/common';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'nso-dossier-card',
  templateUrl: './nso-dossier-card.component.html',
  styleUrls: ['./nso-dossier-card.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgS<PERSON>, NgI<PERSON>, Ng<PERSON><PERSON>Case, RouterLink, NgForOf],
})
export class NsoDossierCardComponent extends DossierCardComponent {
  override setProperties(): void {
    if (this.data) {
      this.hostClass = `style-${this.styleID}`;
      this.link = buildArticleUrl(this.data.mainArticle);
      this.links = this.data.secondaryArticles?.map((article) => buildArticleUrl(article));
      this.tagLink = buildColumnUrl(this.data.mainArticle);
    }
  }
}
