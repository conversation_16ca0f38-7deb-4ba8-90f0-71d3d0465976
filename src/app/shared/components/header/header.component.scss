@use 'shared' as *;

.header {
  min-height: 88px;
  width: 100%;
  position: sticky;
  top: 0;
  z-index: 1000;
}

.top-header {
  min-height: 88px;
  background-color: var(--kui-red-400);
  width: 100%;
  display: flex;
  align-items: center;
  position: relative;

  &-wrapper {
    max-width: $header-max-width;
    width: calc(100% - 30px);
    height: 100%;
    margin: 0 auto;
    display: flex;
    align-items: center;
    position: relative;
  }

  &-search-bar {
    position: absolute;
    right: 0;
    bottom: -55px;
    height: 55px;
    width: 100%;
    display: flex;
    background-color: var(--kui-white);
    padding-left: 30px;

    @include media-breakpoint-up(sm) {
      max-width: 595px;
    }

    input {
      width: 100%;
    }

    button {
      font-size: 16px;
      color: var(--kui-red-400);
      padding: 0 20px;

      &:hover {
        font-weight: 700;
        color: (var --kui-red-200);
      }
    }
  }

  &-logo {
    .nso-logo {
      width: 76px;
      height: 34px;
    }
  }

  &-menu {
    display: flex;
    margin-left: 20px;
    align-content: center;

    &-list {
      display: flex;
      flex-wrap: nowrap;
      gap: 20px;

      &-item {
        margin: auto 0;
        padding: 20px 0;

        &-link {
          color: var(--kui-white);
          font-family: var(--kui-primary);
          font-size: 16px;
          font-weight: 700;
          cursor: pointer;
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: center;
          column-gap: 2px;

          &.active {
            img {
              rotate: 180deg;
            }
          }

          &-chevron {
            width: 24px;
            height: 24px;
          }

          &.klubvb {
            background-color: var(--kui-black) !important;
            color: #d4af37 !important;
            padding: 5px !important;
            border-radius: 4px;
          }
        }
      }
    }

    &:hover &-list-item-link:not(:hover) {
      color: rgba(255, 255, 255, 0.7);

      img {
        opacity: 0.7;
      }
    }

    @include media-breakpoint-down(lg) {
      display: none;
    }
  }

  &-right {
    position: relative;
    display: flex;
    align-content: center;
    align-self: center;
    align-items: center;
    gap: 20px;
    margin-left: auto;

    @include media-breakpoint-down(lg) {
      gap: 12px;
    }

    &-button {
      font-family: var(--kui-font-primary);
      color: var(--kui-white);
      font-weight: 700;
      font-size: 16px;
      padding: 10px 10px;
      border-right: 1px solid var(--kui-white);

      &-hover-enabled:hover {
        border-radius: 4px;
        background-color: var(--kui-red-200);
      }

      @include media-breakpoint-down(md) {
        border: none;
        padding: 0;
      }

      .login-text {
        font-size: 16px;
        @include media-breakpoint-down(sm) {
          display: none;
        }
      }
    }

    &-radio {
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px;
      background-color: var(--kui-white);
      border-radius: 4px;

      img {
        height: 24px;
        width: 24px;
      }

      p {
        font-size: 16px;
        font-family: var(--kui-primary);
        color: var(--kui-red-400);
        font-weight: 700;

        @include media-breakpoint-down(lg) {
          display: none;
        }
      }
    }

    &-icon {
      cursor: pointer;
      height: 100%;
      padding: 20px 0;

      img {
        width: 24px;
        height: 24px;
      }

      &.active {
        padding-bottom: 16px;
        border-bottom: 4px solid var(--kui-white);
      }

      &.hamburger-mobile {
        @include media-breakpoint-down(lg) {
          display: block;
        }
        @include media-breakpoint-up(xl) {
          display: none;
        }
      }
    }
  }
}

.mobile-menu {
  position: fixed;
  top: 88px;
  left: 0;
  right: 0;
  width: 100%;
  height: 100%;
  z-index: 20;
  background-color: var(--kui-white);
  padding-bottom: 20px;
  overflow-y: scroll;

  &-list {
    padding: 0 16px 20px 16px;

    &-item {
      width: 100%;
      padding: 24px 0;
      border-bottom: 1px solid var(--kui-gray-200);

      &.olympics-menu-item {
        display: flex;
        margin-left: -16px;
        align-items: stretch;
        width: calc(100% + 32px);
        padding: 12px 16px;
        background-color: #022366;
        color: var(--kui-white);
        border-bottom: 0;
        a {
          display: flex;
          flex-wrap: nowrap;
          align-items: center;
          color: var(--kui-white);
          .olympics-logo {
            width: 49px;
            height: 56px;
          }
        }
      }

      &-link {
        font-size: 16px;
        font-weight: 700;
        color: var(--kui-gray-600);
        width: 100%;
        display: flex;
        justify-content: space-between;

        &.active {
          color: var(--kui-red-400);
          font-weight: 700;

          img {
            rotate: 180deg;
          }
        }

        &-child-list {
          margin-top: 18px;
          display: flex;
          flex-direction: column;
          gap: 16px;

          &-item {
            display: flex;
            flex-direction: column;
            gap: 12px;
            width: 100%;

            &-link {
              font-size: 16px;
              color: var(--kui-gray-600);
            }
          }

          &-grand {
            display: flex;
            flex-direction: column;
            gap: 12px;
            margin-top: 0;
          }
        }
      }

      &.klubvb-ios {
        background: linear-gradient(to right, #1e292f, #2a3841);
        margin: 0 -16px;
        width: calc(100% + 32px);
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0;

        .klubvb-ios-logo {
          margin-right: 16px;
          height: 100%;
          display: block;

          img {
            height: 16px;
            width: 95px;
          }
        }

        .mobile-menu-list-item-link {
          flex: 1;
          padding: 24px 16px;
          color: var(--kui-white);
        }
      }

      &.klubvb {
        background: var(--kui-black);

        .mobile-menu-list-item-link {
          padding: 5px;
          color: #d4af37;
        }
      }
    }

    &-btn {
      font-family: var(--kui-font-condensed);
      font-size: 18px;
    }

    nso-simple-button {
      margin-top: 20px;
    }
  }
}
