<header class="header">
  <section class="top-header" [style.background-color]="redToYellow()">
    <div class="top-header-wrapper">
      <div class="top-header-logo">
        <app-olympics-logo *ngIf="ENABLE_OLYMPICS_ELEMENTS && (shouldShowCompositeLogo$ | async); else nsoLogo"></app-olympics-logo>
        <ng-template #nsoLogo>
          <a (click)="handleLogoClick()" [routerLink]="['/']">
            <kesma-icon name="nso-logo" [style.color]="whiteToGray()" class="nso-logo"></kesma-icon>
          </a>
        </ng-template>
      </div>
      <nav class="top-header-menu">
        <ul class="top-header-menu-list">
          @for (menuItem of mainMenu; let index = $index; track menuItem.id) {
            <li class="top-header-menu-list-item" [class.klubvb]="menuItem.title === 'Klub-vb'">
              <ng-container *ngIf="(menuItem.children ?? []).length > 0; else link">
                <ng-container *ngTemplateOutlet="parent; context: { item: menuItem, index: index }"></ng-container>
              </ng-container>
              <ng-template #link>
                <ng-container *ngTemplateOutlet="!menuItem.isCustomUrl ? basicLink : customUrl; context: { item: menuItem, index: index }"> </ng-container>
              </ng-template>
            </li>
          }
        </ul>
      </nav>
      <div class="top-header-right">
        <ng-container>
          <a
            [routerLink]="!user ? '/bejelentkezes' : '/my-nso'"
            class="top-header-right-button"
            [class.top-header-right-button-hover-enabled]="!isCsupasport() && !isHatsofuves()"
            [style.border-right]="'1px solid ' + whiteToDarkYellow()"
          >
            <p class="login-text" [style.color]="whiteToBlack()">
              {{ !user ? 'Belépés' : 'Profilom' }}
            </p>
          </a>
        </ng-container>
        <a [routerLink]="['nemzeti-sportradio-hajra-magyarok']" class="top-header-right-radio" [style.background]="whiteToGray()">
          <kesma-icon name="radio" size="24" [style.color]="redToYellow()"></kesma-icon>
          <p [style.color]="redToYellow()">Nemzeti Sportrádió</p>
        </a>
        <div (click)="toggleSearchBar()" [ngClass]="{ active: isSearchOpen }" class="top-header-right-icon">
          <kesma-icon name="nso-search" size="24" [style.color]="whiteToGray()"></kesma-icon>
        </div>
        <div (click)="toggleMobileMenu()" class="top-header-right-icon hamburger-mobile">
          @if (!isMobileMenuOpen) {
            <kesma-icon name="icon-menu" size="24" [style.color]="whiteToGray()"></kesma-icon>
          } @else {
            <kesma-icon name="icon-nso-close" size="24" [style.color]="whiteToGray()"></kesma-icon>
          }
        </div>
      </div>
      <div *ngIf="isSearchOpen" class="top-header-search-bar">
        <input [formControl]="searchKey" class="search-bar-input" placeholder="Keresés sportágra, csapatra, ligára vagy játékosra..." type="text" />
        <button (click)="closeSearchBar()" class="close">Bezárás</button>
      </div>
    </div>
  </section>
  <div #hamburgerMenu *ngIf="selectedMenuItem$ | async as selectedItem">
    <app-header-menu (linkClickEvent)="handleMenuLinkClick()" [menuItem]="selectedItem" class="desktop"></app-header-menu>
  </div>
  <app-secondary-header [menuItems]="secondaryMenu" class="desktop"></app-secondary-header>
  <!--   <img [src]="'/assets/images/csupasport.svg'" alt="csupasport" class="csupasport-img" />
  <img [src]="'/assets/images/csupasport-mobil.svg'" alt="csupasport" class="csupasport-img-mobile" /> -->
  <nav *ngIf="isMobileMenuOpen" class="mobile-menu">
    <ul class="mobile-menu-list">
      @for (menuItem of mainMenu; let index = $index; track menuItem.id) {
        <li
          [class.olympics-menu-item]="ENABLE_OLYMPICS_ELEMENTS && menuItem.link?.toString() === OLYMPIC_COLUMN_LINK"
          [class.klubvb-ios]="menuItem.title === 'Klub-vb' && isIos()"
          [class.klubvb]="menuItem.title === 'Klub-vb' && !isIos()"
          class="mobile-menu-list-item"
        >
          <ng-container *ngIf="(menuItem.children ?? []).length > 0; else mobileLink">
            <ng-container *ngTemplateOutlet="mobileParent; context: { item: menuItem, index: index }"></ng-container>
          </ng-container>
          <ng-template #mobileLink>
            <ng-container *ngTemplateOutlet="!menuItem.isCustomUrl ? basicMobileLink : customMobileUrl; context: { item: menuItem, index: index }">
            </ng-container>
          </ng-template>
          <a *ngIf="menuItem.title === 'Foci Eb 2024'" [routerLink]="menuItem.link" (click)="closeHamburgerMenu()"> </a>
          @if (menuItem.title === 'Klub-vb' && isIos()) {
            <a class="klubvb-ios-logo" [routerLink]="menuItem.link" (click)="closeHamburgerMenu()">
              <img src="/assets/images/tippmix-pro.png" loading="lazy" alt="Tippmix Pro" />
            </a>
          }
        </li>
      }
      <nso-simple-button (click)="closeHamburgerMenu()" [routerLink]="!user ? '/bejelentkezes' : '/my-nso'" class="w-100" round="round">
        <span class="mobile-menu-list-btn">{{ !user ? 'BELÉPÉS' : 'MyNSO' }}</span>
      </nso-simple-button>
      <nso-simple-button (click)="logout()" *ngIf="user" class="w-100" round="round">
        <span class="mobile-menu-list-btn">KIJELENTKEZÉS</span>
      </nso-simple-button>
    </ul>
    <app-secondary-header (closeHamburgerMenu)="closeHamburgerMenu()" [menuItems]="secondaryMenu"></app-secondary-header>
  </nav>
</header>
<app-eb-collapse-menu *ngIf="ENABLE_FOOTBALL_EB_ELEMENTS && (isEbHeader$ | async)"></app-eb-collapse-menu>
<app-olympics-collapse-menu *ngIf="ENABLE_OLYMPICS_ELEMENTS && (isOlympicRoute$ | async)"></app-olympics-collapse-menu>
<!-- Link templates -->
<ng-template #parent let-item="item">
  <p
    (click)="activateItem(item)"
    [ngClass]="{ active: selectedMenuItem$.value?.id === item.id }"
    class="top-header-menu-list-item-link"
    [style.color]="whiteToBlack()"
  >
    {{ item.title }}
    <kesma-icon [style.color]="whiteToTurquoise()" name="icon-nso-arrow-down" [size]="24"></kesma-icon>
  </p>
</ng-template>
<ng-template #basicLink let-item="item">
  <a
    [routerLink]="item.link"
    [target]="item.target"
    [class.klubvb]="item.title === 'Klub-vb'"
    class="top-header-menu-list-item-link"
    [style.color]="whiteToBlack()"
  >
    {{ item.title }}
  </a>
</ng-template>
<ng-template #customUrl let-item="item">
  <a [href]="item.link" [target]="item.target" [class.klubvb]="item.title === 'Klub-vb'" class="top-header-menu-list-item-link" [style.color]="whiteToBlack()">
    {{ item.title }}
  </a>
</ng-template>
<ng-template #mobileParent let-item="item">
  <p (click)="activateItem(item)" [ngClass]="{ active: selectedMenuItem$.value?.id === item.id }" class="mobile-menu-list-item-link">
    {{ item.title }}
    <kesma-icon [style.color]="whiteToTurquoise()" name="icon-nso-arrow-down" [size]="24"></kesma-icon>
  </p>
  <ng-container *ngIf="selectedMenuItem$ | async as selectedItem">
    <ng-container *ngIf="selectedItem.id === item.id">
      <ul class="mobile-menu-list-item-link-child-list">
        <ng-container *ngFor="let childMenu of selectedItem.children">
          <li class="mobile-menu-list-item-link-child-list-item">
            <ng-container *ngIf="childMenu.hasSubItems && childMenu.relatedType !== 'HiddenPage'; else childAsLink">
              <span class="mobile-menu-list-item-link-child-list-item-link">{{ childMenu.title }}</span>
            </ng-container>

            <ng-template #childAsLink>
              <a
                (click)="closeHamburgerMenu()"
                *ngIf="childMenu.relatedType !== 'HiddenPage' && !childMenu?.isCustomUrl"
                [routerLink]="childMenu.link"
                class="mobile-menu-list-item-link-child-list-item-link"
                >{{ childMenu.title }}</a
              >

              <a
                (click)="closeHamburgerMenu()"
                *ngIf="childMenu.relatedType !== 'HiddenPage' && childMenu?.isCustomUrl"
                [href]="childMenu.link"
                class="mobile-menu-list-item-link-child-list-item-link"
                >{{ childMenu.title }}</a
              >
            </ng-template>
            <ul class="mobile-menu-list-item-link-child-list-grand">
              <li *ngFor="let grandChildMenu of childMenu.children" class="mobile-menu-list-item-link-child-list-item">
                <a
                  (click)="closeHamburgerMenu()"
                  *ngIf="!grandChildMenu?.isCustomUrl"
                  [routerLink]="grandChildMenu.link"
                  class="mobile-menu-list-item-link-child-list-item-link"
                  >{{ grandChildMenu.title }}</a
                >

                <a
                  (click)="closeHamburgerMenu()"
                  *ngIf="grandChildMenu?.isCustomUrl"
                  [href]="grandChildMenu.link"
                  class="mobile-menu-list-item-link-child-list-item-link"
                  >{{ grandChildMenu.title }}</a
                >
              </li>
            </ul>
          </li>
        </ng-container>
      </ul>
    </ng-container>
  </ng-container>
</ng-template>
<ng-template #basicMobileLink let-item="item">
  <a
    (click)="closeHamburgerMenu()"
    [routerLink]="item.link"
    [target]="item.target"
    [class.klubvb]="item.title === 'Klub-vb' && !isIos()"
    class="mobile-menu-list-item-link"
  >
    {{ item.title }}
    <img
      *ngIf="ENABLE_OLYMPICS_ELEMENTS && item.link?.toString() === OLYMPIC_COLUMN_LINK"
      src="/assets/images/olympics/emblem-olympics.svg"
      alt=""
      class="olympics-logo"
      loading="lazy"
    />
  </a>
</ng-template>
<ng-template #customMobileUrl let-item="item">
  <a (click)="closeHamburgerMenu()" [href]="item.link" [target]="item.target" class="mobile-menu-list-item-link">
    {{ item.title }}
  </a>
</ng-template>
