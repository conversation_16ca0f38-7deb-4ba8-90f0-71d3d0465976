import { ChangeDetectionStrategy, Component, computed, ElementRef, HostListener, inject, Input, On<PERSON><PERSON>roy, OnInit, Signal, ViewChild } from '@angular/core';
import { ReactiveFormsModule, UntypedFormControl } from '@angular/forms';
import { NavigationEnd, Router, RouterLink } from '@angular/router';
import { IconComponent, PortalConfigSetting, SimplifiedMenuItem, User } from '@trendency/kesma-ui';
import { BehaviorSubject, fromEvent, skip, Subject } from 'rxjs';
import { filter, map, startWith, switchMap, takeUntil, tap } from 'rxjs/operators';
import { AuthService, ColorChangeService, IosAppService, PortalConfigService } from '../../services';
import { AsyncPipe, DOCUMENT, NgClass, NgForOf, NgIf, NgTemplateOutlet } from '@angular/common';
import { NsoOlympicsLogoComponent } from './components/olympics-logo/olympics-logo.component';
import { HeaderMenuComponent } from './components/header-menu/header-menu.component';
import { SecondaryHeaderComponent } from './components/secondary-header/secondary-header.component';
import { NsoSimpleButtonComponent } from '../simple-button/nso-simple-button.component';
import { EbCollapseMenuComponent } from '../eb/eb-collapse-menu/eb-collapse-menu.component';
import { OlympicsCollapseMenuComponent } from '../olympics/olympics-collapse-menu/olympics-collapse-menu.component';

@Component({
  selector: 'app-header',
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NsoOlympicsLogoComponent,
    NgIf,
    AsyncPipe,
    IconComponent,
    RouterLink,
    NgForOf,
    NgTemplateOutlet,
    NgClass,
    ReactiveFormsModule,
    HeaderMenuComponent,
    SecondaryHeaderComponent,
    NsoSimpleButtonComponent,
    EbCollapseMenuComponent,
    OlympicsCollapseMenuComponent,
  ],
})
export class HeaderComponent implements OnInit, OnDestroy {
  @HostListener('document:keydown', ['$event'])
  public onKeyDown(event: KeyboardEvent): void {
    this.handleEvent(event);
  }

  @ViewChild('hamburgerMenu') hamburgerMenuElement?: ElementRef;
  @Input() mainMenu: SimplifiedMenuItem[] = [];
  @Input() bottomMenu: SimplifiedMenuItem[] = [];
  @Input() user: User | undefined;
  searchKey = new UntypedFormControl('');
  //TODO almenü bekötés ha kész a backend!
  @Input() secondaryMenu: SimplifiedMenuItem[] = [];
  destroy$ = new Subject<void>();

  isHomePage = true;
  isSearchOpen = false;
  isMobileMenuOpen = false;
  selectedMenuItem$ = new BehaviorSubject<SimplifiedMenuItem | null>(null);
  isEbHeader$ = this.router.events.pipe(
    filter((e) => e instanceof NavigationEnd),
    startWith(null),
    map(() => {
      return this.router.url === '/' || this.router.url === '/rovat/foci-eb-2024' || this.router.url === '/rovat/magyar-valogatott';
    })
  );
  isOlympicRoute$ = this.router.events.pipe(
    filter((e) => e instanceof NavigationEnd),
    startWith(null),
    map(() => ['/rovat/parizs2024', '', '/'].includes(this.router.url))
  );

  shouldShowCompositeLogo$ = this.router.events.pipe(
    filter((e) => e instanceof NavigationEnd),
    startWith(null),
    map(() => ['/rovat/parizs2024', '/hirfolyam/olimpia-hirfolyam'].includes(this.router.url) || this.router.url.startsWith('/parizs2024/'))
  );

  readonly OLYMPIC_COLUMN_LINK = ['/', 'rovat', 'parizs2024'].toString();

  readonly ENABLE_FOOTBALL_EB_ELEMENTS = this.portalConfigService.isConfigSet(PortalConfigSetting.ENABLE_FOOTBALL_EB_ELEMENTS);
  readonly ENABLE_OLYMPICS_ELEMENTS = this.portalConfigService.isConfigSet(PortalConfigSetting.ENABLE_OLYMPICS_ELEMENTS);

  private readonly document: Document = inject(DOCUMENT);
  private readonly colorChangeService = inject(ColorChangeService);

  readonly #iosAppService: IosAppService = inject(IosAppService);

  isIos: Signal<boolean> = this.#iosAppService.isIosApp;

  readonly redToYellow = computed(() => this.colorChangeService.redToYellow());
  readonly whiteToGray = computed(() => this.colorChangeService.whiteToGray());
  readonly whiteToBlack = computed(() => this.colorChangeService.whiteToBlack());
  readonly whiteToTurquoise = computed(() => this.colorChangeService.whiteToTurquoise());
  readonly whiteToDarkYellow = computed(() => this.colorChangeService.whiteToDarkYellow());
  readonly isCsupasport = computed(() => this.colorChangeService.isCsupasport());
  readonly isHatsofuves = computed(() => this.colorChangeService.isHatsofuves());

  constructor(
    private readonly router: Router,
    private readonly authService: AuthService,
    private readonly portalConfigService: PortalConfigService
  ) {}

  ngOnInit(): void {
    this.detectUrlChanges();
    this.selectedMenuItem$
      .asObservable()
      .pipe(
        //Only do it when we open the bar.
        filter((openState) => openState !== null),
        switchMap(() => {
          return fromEvent(this.document, 'click').pipe(
            //Skip the first click, because that is the one that triggered to open the search bar.
            skip(1),
            tap((event: Event) => {
              const isEventTargetInHamburgerMenu = this.hamburgerMenuElement?.nativeElement.contains(event.target);
              if (!isEventTargetInHamburgerMenu) {
                this.handleMenuLinkClick();
              }
            }),
            takeUntil(this.selectedMenuItem$.pipe(skip(1)))
          );
        })
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe();
  }

  toggleSearchBar(): void {
    this.isSearchOpen = !this.isSearchOpen;
    this.handleMenuLinkClick();
    this.isMobileMenuOpen = false;
  }

  closeSearchBar(): void {
    this.isSearchOpen = false;
  }

  toggleMobileMenu(): void {
    this.isMobileMenuOpen = !this.isMobileMenuOpen;
  }

  closeHamburgerMenu(): void {
    this.isMobileMenuOpen = false;
  }

  activateItem(menuItem: SimplifiedMenuItem): void {
    //Close dropdown if selected menu gets clicked again
    if (this.selectedMenuItem$.getValue()?.id === menuItem.id) {
      this.selectedMenuItem$.next(null);
    } else {
      this.selectedMenuItem$.next(menuItem);
    }
  }

  private handleEvent(event: KeyboardEvent): void {
    if (!this.isSearchOpen) return;

    switch (event.key) {
      case 'Enter':
        event.preventDefault();
        event.stopPropagation();
        this.setUrl();
        this.isSearchOpen = false;
        this.searchKey.setValue('');
        break;
    }
  }

  handleMenuLinkClick(): void {
    this.selectedMenuItem$.next(null);
  }

  handleLogoClick(): void {
    if (this.isHomePage) {
      window.location.href = '/';
    }
  }

  private setUrl(): void {
    this.router.navigate(['/kereses'], {
      queryParams: { global_filter: this.searchKey.value },
    });
  }

  private detectUrlChanges(): void {
    this.isHomePage = this.router.url === '/';
    this.router.events
      .pipe(
        filter((router) => router instanceof NavigationEnd),
        takeUntil(this.destroy$)
      )
      .subscribe((router) => {
        const url = (router as NavigationEnd)?.url;
        this.isHomePage = url === '/';
      });
  }

  logout(): void {
    this.authService.clearTokens();
    this.isMobileMenuOpen = false;
    this.router.navigate(['/']);
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
