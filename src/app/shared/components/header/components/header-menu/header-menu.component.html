<section *ngIf="menuItem" class="header-menu">
  <div class="header-menu-wrapper">
    <ng-container *ngIf="hasThirdLevel">
      <div *ngIf="(menuItem?.children ?? []).length > 0" class="header-menu-parent-list">
        <ng-container *ngFor="let childItem of menuItem.children; let index = index">
          <div *ngIf="childItem.relatedType !== relatedTypes.HIDDEN_PAGE" class="header-menu-parent-list-item">
            <p (click)="activateItem(childItem)" class="header-menu-parent-list-item-link" [ngClass]="{ active: childItem.id === activeChild?.id }">
              {{ childItem.title }}
            </p>
          </div>
        </ng-container>
      </div>
    </ng-container>
    <ng-container>
      <div class="header-menu-link-list">
        <div *ngFor="let array of splitItems; let last = last" [ngClass]="{ border: !last }" class="header-menu-link-list-container">
          <ng-container *ngFor="let item of array">
            <ng-container *ngTemplateOutlet="!item.isCustomUrl ? basicLink : customUrl; context: { item: item }"></ng-container>
          </ng-container>
        </div>
      </div>
    </ng-container>
  </div>
</section>

<!-- List item templates -->
<ng-template #basicLink let-item="item">
  <a (click)="emitLinkClickedEvent()" [routerLink]="item.link" class="header-menu-link-list-container-item">{{ item.title }}</a>
</ng-template>

<ng-template #customUrl let-item="item">
  <a (click)="emitLinkClickedEvent()" [href]="item.link" class="header-menu-link-list-container-item">{{ item.title }}</a>
</ng-template>
