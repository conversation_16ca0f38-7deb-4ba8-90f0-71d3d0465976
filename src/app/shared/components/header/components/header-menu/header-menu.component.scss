@use 'shared' as *;

.header-menu {
  position: absolute;
  width: 100%;
  background-color: white;

  &-wrapper {
    max-width: $header-max-width;
    width: calc(100% - 30px);
    margin: auto;
    padding-left: 96px;
    padding-top: 14px;
    padding-bottom: 24px;
  }

  &-parent-list {
    display: flex;
    width: 100%;
    margin-bottom: 24px;
    padding-bottom: 10px;
    gap: 24px;
    border-bottom: 1px solid var(--kui-gray-300);

    &-item {
      &-link {
        cursor: pointer;
        font-size: 14px;
        font-weight: 400;
        color: var(--kui-gray-550);

        &.active {
          color: var(--kui-red-400);
          font-weight: 700;
        }
      }
    }
  }

  &-link-list {
    display: flex;
    width: 100%;
    flex-wrap: nowrap;
    overflow-x: scroll;
    -ms-overflow-style: none;
    scrollbar-width: none;

    &::-webkit-scrollbar {
      display: none;
    }

    &-container {
      display: flex;
      flex-direction: column;
      box-sizing: border-box;
      align-content: center;
      gap: 24px;
      padding-right: 33px;
      margin: 12px 33px 12px 0;

      &.border {
        border-right: 1px solid var(--kui-gray-200);
      }

      &-item {
        color: var(--kui-gray-550);
        font-size: 16px;
        font-weight: 700;
        cursor: pointer;
      }
    }
  }
}
