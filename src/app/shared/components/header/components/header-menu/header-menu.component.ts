import { ChangeDetectionStrategy, Component, EventEmitter, Input, OnChanges, Output, SimpleChanges } from '@angular/core';
import { RelatedType, SimplifiedMenuItem } from '@trendency/kesma-ui';
import { Ng<PERSON>lass, NgForOf, NgIf, NgTemplateOutlet } from '@angular/common';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'app-header-menu',
  templateUrl: './header-menu.component.html',
  styleUrls: ['./header-menu.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NgForOf, NgClass, NgTemplateOutlet, RouterLink],
})
export class HeaderMenuComponent implements OnChanges {
  @Input() menuItem: SimplifiedMenuItem | null;
  @Output() linkClickEvent = new EventEmitter<void>();
  activeChild: SimplifiedMenuItem | null = null;
  hasThirdLevel = false;
  splitItems: SimplifiedMenuItem[][] = [[]];
  readonly relatedTypes = RelatedType;

  ngOnChanges(changes: SimpleChanges): void {
    // Current active mainMenu item
    const currentItem: SimplifiedMenuItem = changes['menuItem']['currentValue'];
    if (currentItem?.children) {
      // Top row of the menu
      // Active child is the first child of the active menu item
      this.activeChild = currentItem.children?.find((e: SimplifiedMenuItem) => e.relatedType !== RelatedType.HIDDEN_PAGE) || null;
      this.hasThirdLevel = this.activeChild?.hasSubItems ?? false;
      if (this.hasThirdLevel) {
        this.splitItems = this.splitMenuArray(this.filterHiddenElement(this.activeChild?.children), 3);
      } else if (currentItem.children?.length > 0) {
        this.splitItems = this.splitMenuArray(this.filterHiddenElement(currentItem.children), 3);
      }
    }
  }

  activateItem(menuItem: SimplifiedMenuItem): void {
    this.activeChild = menuItem;
    this.splitItems = this.splitMenuArray(this.filterHiddenElement(menuItem.children), 3);
  }

  emitLinkClickedEvent(): void {
    this.linkClickEvent.emit();
  }

  private filterHiddenElement(array: SimplifiedMenuItem[] | undefined): SimplifiedMenuItem[] {
    if (!array?.length) {
      return [];
    }

    return array.filter((value) => value.relatedType !== RelatedType.HIDDEN_PAGE);
  }

  private splitMenuArray(array: SimplifiedMenuItem[], size: number): SimplifiedMenuItem[][] {
    if (array.length < 0) {
      return [[]];
    }
    const chunks = [];
    const length = array.length;

    for (let i = 0; i < length; i += size) {
      chunks.push(array.slice(i, i + size));
    }
    return chunks;
  }
}
