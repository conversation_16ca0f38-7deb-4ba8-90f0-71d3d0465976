<section *ngIf="menuItems.length > 0" class="secondary-header" [style.background]="grayToLightGray()">
  <div class="secondary-header-nav">
    <ul class="secondary-header-nav-list">
      <app-telekom-vivicitta-header-element (closeHamburgerMenu)="closeHamburgerMenu.emit()"></app-telekom-vivicitta-header-element>
      <li *ngFor="let menuItem of menuItems" class="secondary-header-nav-list-item">
        <ng-container *ngTemplateOutlet="!menuItem.isCustomUrl ? normalLink : customUrl; context: { item: menuItem }"></ng-container>
        <ng-template #normalLink let-item="item">
          <a
            (click)="closeHamburgerMenu.emit()"
            [routerLink]="item.link"
            [target]="item.target"
            class="secondary-header-nav-list-item-link"
            [class.hervis-balaton]="item.title === HERVIS_BALATON_TITLE"
          >
            {{ item.title }}</a
          >
        </ng-template>
        <ng-template #customUrl let-item="item">
          <a
            (click)="closeHamburgerMenu.emit()"
            [href]="item.link"
            [target]="item.target"
            class="secondary-header-nav-list-item-link"
            [class.hervis-balaton]="item.title === HERVIS_BALATON_TITLE"
          >
            {{ item.title }}</a
          >
        </ng-template>
      </li>
    </ul>
  </div>
</section>
