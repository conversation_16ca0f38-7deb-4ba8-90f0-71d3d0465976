@use 'shared' as *;

.secondary-header {
  width: 100%;
  height: 100%;
  background-color: var(--kui-gray-550);
  display: flex;
  justify-content: center;
  padding: 12px 24px;

  @include media-breakpoint-down(md) {
    display: block;
    padding: 12px 16px;
  }

  &-nav-list {
    display: flex;
    gap: 24px;

    @include media-breakpoint-down(md) {
      display: block;
    }

    &-item {
      @include media-breakpoint-down(md) {
        padding: 12px 0;
      }
      a {
        cursor: pointer;
        font-size: 14px;
        font-weight: 400;
        color: var(--kui-white);

        @include media-breakpoint-down(md) {
          font-size: 16px;
          font-weight: 700;
        }
      }
    }
  }

  .hervis-balaton {
    background-color: #2bbfa7;
    color: var(--kui-black);
    padding: 6px 8px;
    border-radius: 4px;
  }
}
