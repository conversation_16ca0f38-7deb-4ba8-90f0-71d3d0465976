import { ChangeDetectionStrategy, Component, computed, EventEmitter, inject, Input, Output } from '@angular/core';
import { SimplifiedMenuItem } from '@trendency/kesma-ui';
import { NgForOf, NgIf, NgTemplateOutlet } from '@angular/common';
import { RouterLink } from '@angular/router';
import { TelekomVivicittaHeaderElementComponent } from '../../../telekom-vivicitta-header-element/telekom-vivicitta-header-element.component';
import { ColorChangeService } from 'src/app/shared/services';

@Component({
  selector: 'app-secondary-header',
  templateUrl: './secondary-header.component.html',
  styleUrls: ['./secondary-header.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NgForOf, NgTemplateOutlet, RouterLink, TelekomVivicittaHeaderElementComponent],
})
export class SecondaryHeaderComponent {
  @Input() menuItems: SimplifiedMenuItem[] = [];
  @Output() closeHamburgerMenu: EventEmitter<void> = new EventEmitter<void>();
  private readonly colorChangeService = inject(ColorChangeService);
  readonly grayToLightGray = computed(() => this.colorChangeService.grayToLightGray());

  readonly HERVIS_BALATON_TITLE = 'Hervis Balaton-Átevezés';
}
