import { ChangeDetectionStrategy, Component } from '@angular/core';
import { ArticleCard, BaseComponent, buildArticleUrl } from '@trendency/kesma-ui';
import { NgIf, NgTemplateOutlet } from '@angular/common';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'nso-article-card-link-wrapper',
  templateUrl: 'nso-article-card-link-wrapper.component.html',
  styleUrls: ['nso-article-card-link-wrapper.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NgTemplateOutlet, RouterLink],
})
export class NsoArticleCardLinkWrapperComponent extends BaseComponent<ArticleCard> {
  customLink: { url?: string; openInNewTab?: boolean } | null = null;
  customLinkTarget: '_blank' | '_self' = '_self';
  articleLink: string[] = [];

  protected override setProperties(): void {
    this.customLink = this.data?.customLink || null;
    this.customLinkTarget = this.customLink?.openInNewTab ? '_blank' : '_self';
    this.articleLink = this.data ? buildArticleUrl(this.data) : [];
  }
}
