import { ChangeDetectionStrategy, Component, computed, HostBinding, inject, Input, OnInit } from '@angular/core';
import {
  ArticleCard,
  BaseComponent,
  buildArticleUrl,
  buildColumnUrl,
  ClickStopPropagationDirective,
  FocusPointDirective,
  MinuteToMinuteState,
} from '@trendency/kesma-ui';
import { DATE_FORMAT } from '../../constants';
import { ArticleCardType } from '../../definitions';
import { backendDateToDate, FormatDatePipe, PublishDatePipe, UtilService } from '@trendency/kesma-core';
import { DatePipe, NgForOf, NgIf, NgSwitch, NgSwitchCase, NgTemplateOutlet } from '@angular/common';
import { NsoArticleCardLinkWrapperComponent } from './nso-article-card-link-wrapper/nso-article-card-link-wrapper.component';
import { FormatPipeModule } from 'ngx-date-fns';
import { RouterLink } from '@angular/router';
import { ColorChangeService } from '../../services';

const MOBILE_SIZE = 768;

@Component({
  selector: 'nso-article-card',
  templateUrl: './nso-article-card.component.html',
  styleUrls: ['./nso-article-card.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgIf,
    NgSwitch,
    NgSwitchCase,
    NsoArticleCardLinkWrapperComponent,
    FormatPipeModule,
    NgTemplateOutlet,
    NgForOf,
    ClickStopPropagationDirective,
    RouterLink,
    PublishDatePipe,
    FormatDatePipe,
    DatePipe,
    FocusPointDirective,
  ],
})
export class NsoArticleCardComponent extends BaseComponent<ArticleCard> implements OnInit {
  private readonly utils = inject(UtilService);
  private readonly colorChangeService = inject(ColorChangeService);

  @HostBinding('class') hostClass = '';

  @Input() desktopWidth: number = 12; // Value from 1-12 to indicate the width of the card on desktop.

  @Input() set styleID(styleID: ArticleCardType) {
    this.cardType = styleID;
    this.hostClass = `article-card style-${ArticleCardType[styleID]}`;
  }

  get styleID(): ArticleCardType {
    return this.cardType;
  }

  readonly isCsupasport = computed(() => this.colorChangeService.isCsupasport());
  readonly isHatsofuves = computed(() => this.colorChangeService.isHatsofuves());

  public ArticleCardType = ArticleCardType;
  public cardType: ArticleCardType = ArticleCardType.FeaturedImgTitle;
  public publishDate?: Date;
  public displayedThumbnailUrl?: string;
  public articleLink: string[] = [];
  public columnLink: string[] = [];
  public isMobile = this.utils.isBrowser() ? window.innerWidth <= MOBILE_SIZE : false;

  readonly DATE_FORMAT = DATE_FORMAT;
  readonly minuteToMinute = MinuteToMinuteState;

  override ngOnInit(): void {
    super.ngOnInit();
    this.isMobile = this.utils.isBrowser() ? window.innerWidth <= MOBILE_SIZE : false;

    if (this.desktopWidth <= 4) {
      this.hostClass += ` small`;
    } else if (this.desktopWidth <= 9) {
      this.hostClass += ` medium`;
    }
  }

  protected override setProperties(): void {
    this.publishDate = this.data?.publishDate instanceof Date ? this.data?.publishDate : backendDateToDate(this.data?.publishDate as string);
    this.articleLink = this.data ? buildArticleUrl(this.data) : [];
    this.columnLink = this.data ? buildColumnUrl(this.data) : [];
    this.displayedThumbnailUrl =
      (this.data?.isAdultsOnly ? 'assets/images/icons/icon-18-red.svg' : this.data?.thumbnail?.url) || 'assets/images/nemzetisport.png';
  }
}
