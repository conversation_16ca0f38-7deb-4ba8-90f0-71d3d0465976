@use 'shared' as *;

$imgWidth: 303px;

:host {
  display: block;
  width: 100%;
  margin-bottom: 20px;

  @include media-breakpoint-down(md) {
    margin-bottom: 40px;
  }

  .gallery-indicator {
    position: absolute;
    top: 0;
    right: 0;
    cursor: pointer;
    background: var(--kui-red-400);
    padding: 10px;

    .icon-gallery {
      width: 25px;
      height: 23px;
    }
  }

  .icon {
    min-width: 24px;
    min-height: 24px;

    &.live {
      top: 0;
      left: 0;
      position: absolute;
      font-size: 32px;
      text-transform: uppercase;
      font-family: var(--kui-font-condensed);
      background-color: var(--kui-red-400);
      color: var(--kui-white);
      padding: 10px 20px;
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 10px;
    }

    &-podcast,
    &-play {
      top: 50%;
      left: 50%;
      position: absolute;
      transform: translate(-50%, -50%);
      z-index: 20;
    }

    &-play {
      width: 64px;
      height: 64px;
    }

    &-podcast {
      min-width: 125px;
      min-height: 125px;
    }
  }

  .article-card {
    &-link {
      color: var(--kui-gray-600);
    }

    &-thumbnail {
      object-fit: cover;
      transition: all 0.3s ease-in-out;
      width: 100%;
    }

    &-thumbnail-box {
      position: relative;
      overflow: hidden;
    }

    &-title {
      color: var(--kui-gray-600);
      font-size: 22px;
      font-weight: 700;
      line-height: 30px;
      text-transform: uppercase;
      font-family: var(--kui-font-condensed);
    }

    &-lead {
      line-height: 25px;
    }

    &-date {
      font-weight: 700;
      color: var(--kui-gray-550);
    }

    &-column {
      color: var(--kui-red-400);
      font-weight: 700;
      font-size: 16px;
      line-height: 24px;
    }

    &-top {
      display: flex;
      align-items: center;
      gap: 10px;
    }

    &-right {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    &-divider {
      background-color: var(--kui-gray-200);
      display: inline-block;
      content: '';
      height: 20px;
      width: 1px;
    }
  }

  &.article-card {
    &.style-FeaturedTitle,
    &.style-FeaturedTitleArrow,
    &.style-FeaturedTitleArrowDate {
      .article-card {
        &-link {
          display: flex;
          align-items: center;
          justify-content: space-between;
          gap: 5px;
        }

        &-title {
          font-weight: 400;
          font-size: 16px;
          font-family: var(--kui-font-primary);
          text-transform: none;
        }
      }
    }

    &.style-FeaturedColumnTitleLead {
      .article-card {
        &-title {
          margin: 5px 0 15px 0;
        }
      }
    }

    &.style-FeaturedColumnTitleLeadTags {
      .article-card {
        &-tags,
        &-link {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
        }

        &-link {
          flex-direction: column;
        }
      }
    }

    &.style-DataBankListItem {
      padding: 16px;
      background-color: var(--kui-gray-100);

      .article-card {
        &-link {
          display: flex;
          flex-wrap: nowrap;
          align-items: center;
          gap: 16px;
        }

        &-thumbnail {
          border-radius: 48px;
          border: 1px solid var(--kui-gray-200);
          min-width: 48px;
          height: 48px;
          width: 48px;
        }

        &-title {
          color: var(--kui-red-500);
          text-decoration: underline;
          text-underline-offset: 7px;
          font-size: 16px;
          font-family: var(--kui-font-primary);
          text-transform: none;
        }
      }
    }

    &.style-FeaturedImgColumnTitleLeadDate,
    &.style-FeaturedImgTitleLead,
    &.style-FeaturedImgColumnTitle,
    &.style-FeaturedImgTitle {
      .article-card {
        &-link {
          display: flex;
          flex-direction: column;
          gap: 16px;
        }
      }
    }

    &.style-FeaturedImgTitleDate {
      .article-card {
        &-date {
          margin-top: 16px;
          margin-bottom: 5px;
        }
      }
    }

    &.style-FeaturedImgTitleLead {
      .article-card {
        &-link {
          gap: 16px;
        }
      }
    }

    &.style-FeaturedImgColumnTitleDate {
      .article-card {
        &-top {
          margin-top: 16px;
        }

        &-top {
          margin-bottom: 4px;
        }
      }
    }

    &.style-FeaturedImgColumnTitleLeadDate {
      .article-card {
        &-top {
          // This is needed because, i use it elsewhere too and this get 16px gap.
          margin-bottom: -10px;
        }
      }
    }

    &.style-FeaturedTitleInsideImg {
      .article-card {
        &-link {
          position: relative;
          display: block;
          height: 100%;
        }

        &-title {
          position: absolute;
          color: var(--kui-white);
          padding: 16px 24px;
          letter-spacing: 0.16px;
          line-height: 140%;
          bottom: 0;
          z-index: 200;

          @include media-breakpoint-down(md) {
            font-size: 14px;
            padding: 8px 16px;
          }
        }

        &-shadow {
          background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, var(--kui-black) 150%);
          position: absolute;
          height: 50%;
          width: 100%;
          bottom: 0;
        }
      }
    }

    &.style-ExternalRecommendation {
      .article-card {
        &-column {
          margin-top: 16px;
          margin-bottom: 5px;
          text-transform: uppercase;
        }
      }
    }

    &.style-FeaturedSidedImgTitle {
      .article-card {
        &-link {
          display: flex;
          gap: 24px;
        }

        &-thumbnail {
          width: 100%;

          &:not(.small) {
            max-width: 195px;
          }

          @include media-breakpoint-down(md) {
            width: 100%;
          }
        }

        &-title {
          overflow: hidden;
          display: -webkit-box;
          -webkit-line-clamp: 4;
          -webkit-box-orient: vertical;
        }
      }

      &.small {
        .article-card {
          &-link {
            flex-direction: column;
          }
        }
      }

      &:not(.small) {
        .article-card-thumbnail-box {
          max-width: 195px;
          min-width: 195px;

          @include media-breakpoint-down(md) {
            max-width: 50%;
            min-width: 50%;
          }
        }
      }
    }

    &.style-FeaturedSidedImgColumnTitleDate {
      .article-card {
        @include media-breakpoint-down(md) {
          &-title {
            font-size: 18px;
            line-height: 20px;
          }

          &-column {
            font-size: 12px;
          }

          &-top {
            margin-bottom: 15px;
          }
        }

        &-date:first-of-type {
          color: var(--kui-red-400);
        }

        &-link {
          display: flex;
          gap: 24px;

          @include media-breakpoint-down(md) {
            gap: 12px;
          }
        }

        &-thumbnail {
          width: 100%;
          max-width: $imgWidth;

          &.small {
            max-width: unset;
          }

          &-box {
            min-width: $imgWidth;
          }

          @include media-breakpoint-down(md) {
            &:not(.small) {
              width: 120px;
              height: 120px;
            }

            &-box {
              min-width: 120px;
            }
          }
        }
      }

      &.small {
        .article-card {
          &-link {
            flex-direction: column;
            gap: 12px;
          }
        }
      }
    }

    &.style-FeaturedSidedImgBottomColumnTitleLead,
    &.style-FeaturedSidedImgColumnTitleLead {
      .article-card {
        &-link {
          display: flex;
          gap: 24px;

          @include media-breakpoint-down(md) {
            flex-direction: column;
            gap: 16px;
          }

          &.small {
            flex-direction: column;
            gap: 16px;
          }
        }

        &-title {
          display: -webkit-box;
          -webkit-line-clamp: 3;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }

        &-thumbnail {
          width: 100%;

          &:not(.small) {
            max-width: $imgWidth;

            @include media-breakpoint-down(md) {
              max-width: unset;
            }
          }

          &-box {
            min-width: $imgWidth;
          }
        }
      }
    }

    &.style-FeaturedSidedImgColumnTitleLead {
      .article-card {
        &-column {
          line-height: 16px;
        }

        &-top {
          align-items: flex-start;

          .csupasport {
            padding: 2px 4px 2px 6px;
            background: #646464;
            border-left: 2px solid #1ba1c7;
            color: #ffe035;
          }

          .hatsofuves {
            padding: 2px 4px;
            background: #cfc621;
            color: var(--kui-black);
          }
        }

        &-date {
          font-weight: 400;
          line-height: 16px;
        }
      }
    }

    &.style-FeaturedSidedImgBottomColumnTitleLead {
      .article-card {
        @include media-breakpoint-down(md) {
          &-top {
            order: 1;
          }

          &-title {
            order: 2;
          }

          &-lead {
            order: 3;
          }
        }

        &-thumbnail {
          &:not(.small) {
            width: calc(165px * 4 / 3);

            @include media-breakpoint-down(md) {
              width: 100%;
            }
          }

          &-box {
            min-width: 220px;
          }
        }
      }
    }

    &.style-FeaturedSidedBottomColumnTitleDate {
      .article-card {
        &-link {
          display: flex;
          gap: 24px;

          &.small {
            flex-direction: column;
            gap: 16px;
          }

          @include media-breakpoint-down(sm) {
            flex-direction: column;
            gap: 16px;
          }
        }

        &-right {
          @include media-breakpoint-down(sm) {
            flex-direction: column;
          }
        }

        &-thumbnail {
          @include media-breakpoint-up(sm) {
            max-width: 195px;
            min-width: 195px;
          }

          &.small {
            width: 100%;
            max-width: none;
          }

          &-box {
            min-width: 195px;
          }
        }
      }
    }

    &.style-FeaturedBigSidedImgColumnTitleLead,
    &.style-FeaturedSidedImgTitleLead {
      .article-card {
        &-link {
          display: flex;
          gap: 24px;

          @include media-breakpoint-down(md) {
            flex-direction: column;
            gap: 17px;
          }

          &.small {
            flex-direction: column;
            gap: 17px;
          }
        }

        &-thumbnail-box {
          @include media-breakpoint-up(lg) {
            max-width: 630px;
            width: 100%;
          }

          @include media-breakpoint-down(lg) {
            min-width: 400px;
          }

          @include media-breakpoint-down(md) {
            min-width: 100%;
          }
        }

        &-thumbnail {
          width: 100%;
        }
      }
    }

    &.style-FeaturedBigSidedImgColumnTitleLead {
      .article-card {
        &-link {
          @include media-breakpoint-up(md) {
            flex-direction: row;
          }
        }

        &-title {
          font-size: 22px;
          line-height: 30px;
          margin: 4px 0 16px 0;

          display: -webkit-box;
          -webkit-line-clamp: 4;
          overflow: hidden;
          -webkit-box-orient: vertical;

          @include media-breakpoint-down(md) {
            font-size: 22px;
            line-height: 28px;
            margin: 8px 0;
          }
        }

        &-right {
          gap: 0;

          @include media-breakpoint-up(lg) {
            width: calc(33.3% - 12px);
            max-width: none;
          }

          @include media-breakpoint-down(lg) {
            width: 50%;
          }

          @include media-breakpoint-down(md) {
            width: 100%;
          }
        }

        &-thumbnail-box {
          @include media-breakpoint-up(lg) {
            width: 66.6%;
            max-width: none;
          }

          @include media-breakpoint-down(lg) {
            width: 50%;
            min-width: auto;
          }

          @include media-breakpoint-down(md) {
            width: 100%;
          }
        }

        &-link {
          align-items: flex-start;

          &.mobile {
            flex-direction: column;
            gap: 17px;

            .article-card {
              &-thumbnail-box {
                width: 100%;
              }

              &-right {
                width: 100%;
              }
            }
          }
        }
      }
    }
  }
}
