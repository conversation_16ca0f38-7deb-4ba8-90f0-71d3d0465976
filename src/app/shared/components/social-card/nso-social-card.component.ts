import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { NgForOf, NgIf } from '@angular/common';

@Component({
  selector: 'nso-social-card',
  templateUrl: './nso-social-card.component.html',
  styleUrls: ['./nso-social-card.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgForOf, NgIf],
})
export class NsoSocialCardComponent {
  @Input() data: any;
}
