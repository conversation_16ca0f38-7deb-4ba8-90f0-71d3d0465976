<form [formGroup]="form">
  Regular HTML text input with nsoNsoTextInput directive.
  <input type="text" placeholder="Email cím" nsoNsoTextInput formControlName="email" />

  For using errors or adding prefix/suffix, you should wrap the input with the nso-input-field component.
  <nso-input-field>
    <span prefix>🔐</span>
    <input [type]="showPassword ? 'text' : 'password'" placeholder="Jelszó" nsoNsoTextInput formControlName="password" />
    <nso-password-eye-toggle suffix (visibilityChanged)="togglePassword($event)"> </nso-password-eye-toggle>
  </nso-input-field>
</form>
