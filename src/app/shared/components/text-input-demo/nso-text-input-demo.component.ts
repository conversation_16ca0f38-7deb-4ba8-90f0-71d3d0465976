import { ChangeDetectionStrategy, Component } from '@angular/core';
import { ReactiveFormsModule, UntypedFormBuilder, Validators } from '@angular/forms';
import { passwordContainsCharsAndNumbers, passwordContainsSpecial } from '@trendency/kesma-ui';
import { NsoTextInputDirective } from '../../directives';
import { NsoInputFieldComponent } from '../input-field/nso-input-field.component';
import { NsoPasswordEyeToggleComponent } from '../password-eye-toggle/nso-password-eye-toggle.component';

@Component({
  selector: 'nso-nso-text-input-demo',
  templateUrl: './nso-text-input-demo.component.html',
  styleUrls: ['./nso-text-input-demo.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [ReactiveFormsModule, NsoTextInputDirective, NsoInputFieldComponent, NsoPasswordEyeToggleComponent],
})
export class NsoTextInputDemoComponent {
  form = this.fb.group({
    email: ['', [Validators.email]],
    password: ['', [Validators.required, Validators.minLength(6), passwordContainsSpecial, passwordContainsCharsAndNumbers]],
  });
  showPassword = false;
  constructor(private readonly fb: UntypedFormBuilder) {}

  togglePassword(value: boolean): void {
    this.showPassword = value;
  }
}
