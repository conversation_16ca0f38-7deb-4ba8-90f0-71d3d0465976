import { ChangeDetectionStrategy, Component, Input, ViewEncapsulation } from '@angular/core';
import { DateTimePickerComponent, KesmaFormControlComponent } from '@trendency/kesma-ui';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { NgIf } from '@angular/common';
import { NsoSimpleButtonComponent } from '../simple-button/nso-simple-button.component';

@Component({
  selector: 'nso-date-time-picker',
  templateUrl: 'nso-date-time-picker.component.html',
  styleUrls: ['../../../../../node_modules/flatpickr/dist/flatpickr.min.css', './nso-date-time-picker.component.scss'],
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [ReactiveFormsModule, NgIf, KesmaFormControlComponent, NsoSimpleButtonComponent],
})
export class NsoDateTimePickerComponent extends DateTimePickerComponent {
  @Input() formGroup?: FormGroup;
  @Input() controlName?: string;

  @Input() label?: string;

  @Input() nowText: string = 'Most';
}
