@use 'shared' as *;

:root {
  position: relative;

  .flatpickr + .icon {
    position: absolute;
    top: 22px;
    right: 20px;
    width: 16px;
    height: 16px;
  }

  .flatpickr {
    &-custom-content {
      font-weight: bold;
      position: absolute;
      left: 0;
      right: 0;
      top: 7px;
    }

    &-custom-footer {
      display: flex;
      align-items: center;
      border-top: 1px solid var(--kui-gray-350);
      padding: 10px;
      gap: 10px;

      nso-simple-button {
        width: 100%;
      }

      button {
        padding: 7px 16px;
      }
    }
  }

  .flatpickr-calendar {
    border-radius: 5px;
    margin: 4px 0;
    border: 1px solid var(--kui-gray-350);
    box-shadow: none;

    &.open {
      z-index: 5;
    }

    &:before,
    &:after {
      display: none;
    }

    .flatpickr-months {
      .flatpickr-month {
        height: 40px;
      }

      .flatpickr-prev-month,
      .flatpickr-next-month {
        top: 0;
        margin: 0 10px;
        padding: 5px;

        svg {
          width: 10px;
          height: 10px;

          &:hover {
            fill: var(--kui-blue-400);
          }
        }
      }
    }

    .flatpickr-innerContainer {
      margin-left: -1px;

      .flatpickr-rContainer {
        .flatpickr-weekdays {
          .flatpickr-weekdaycontainer {
            .flatpickr-weekday {
              color: var(--kui-black);
              font-family: var(--kui-font-primary);
            }
          }
        }

        .flatpickr-days {
          .dayContainer {
            .flatpickr-day {
              font-family: var(--kui-font-primary);

              &.selected {
                background-color: var(--kui-gray-550);
                border-color: var(--kui-gray-550);
                border-radius: 5px;
              }

              &.today {
                border: none;
              }

              &:hover:not(.selected) {
                background-color: var(--kui-red-200);
                border-color: var(--kui-red-200);
                color: var(--kui-white);
                border-radius: 5px;
              }

              &.prevMonthDay,
              &.nextMonthDay {
                color: var(--kui-gray-350);

                &:hover {
                  color: var(--kui-gray-350);
                }
              }

              &.flatpickr-disabled {
                color: var(--kui-gray-300);

                &:hover {
                  background-color: transparent;
                  border-color: transparent;
                  color: var(--kui-gray-300);
                }
              }
            }
          }
        }
      }
    }

    .flatpickr-time {
      border-color: var(--kui-gray-350);

      .numInputWrapper {
        input {
          color: var(--kui-gray-550);
          font-family: var(--kui-font-primary);

          &:hover,
          &:focus {
            background-color: var(--kui-gray-100);
          }
        }
      }
    }
  }
}
