<ng-container *ngIf="formGroup && controlName" [formGroup]="formGroup">
  <kesma-form-control>
    <label *ngIf="label" class="nso-form-label" for="{{ id }}"> {{ label }} </label>
    <input [formControlName]="controlName" [id]="id" class="nso-form-input flatpickr" type="text" />
    <i class="icon icon-datepicker"></i>
  </kesma-form-control>
</ng-container>

<div #customContent class="flatpickr-custom-content">{{ currentYear }}. {{ currentMonth }}</div>

<div #customFooter class="flatpickr-custom-footer">
  <nso-simple-button (click)="close()" color="outline">Bezárás</nso-simple-button>
  <nso-simple-button (click)="now()" color="primary">{{ nowText }}</nso-simple-button>
</div>
