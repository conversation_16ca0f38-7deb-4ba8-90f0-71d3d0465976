import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { Ng<PERSON>orO<PERSON>, Ng<PERSON>tyle } from '@angular/common';

@Component({
  selector: 'nso-news24h',
  templateUrl: './nso-news24h.component.html',
  styleUrls: ['./nso-news24h.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgForOf, NgStyle],
})
export class NsoNews24hComponent {
  @Input() data: any;
}
