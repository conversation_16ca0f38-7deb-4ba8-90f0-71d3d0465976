@use 'shared' as *;

.news24h {
  margin-top: 118px;
}

.news-wrapper {
  display: flex;
  width: 100%;
  font-family: var(--kui-font-primary);
  flex-direction: row;
  margin: 40px 0;

  @include media-breakpoint-down(md) {
    display: block;
    margin: 22px 0;
  }

  .date {
    min-width: 165px;
    font-size: 16px;

    @include media-breakpoint-down(md) {
      display: flex;
      flex-direction: row;
      margin-bottom: 11px;
      width: 100%;
    }

    &-time {
      font-size: 16px;
      color: var(--kui-red-350);
      font-weight: 700;
    }

    &-separator {
      color: var(--kui-gray-500);
      margin: 0 6px;
      font-weight: 400;
    }

    &-day {
      color: var(--kui-gray-500);
    }
  }

  .article {
    display: flex;

    &-img {
      min-width: 222px;
      height: 166px;
      margin-right: 12px;
      @include media-breakpoint-down(md) {
        //@include imgRatio(100%, 50%);
      }
    }

    &-text {
      margin-left: 12px;
    }
    &-tag {
      font-weight: 700;
      color: var(--kui-red-400);
      font-size: 16px;
      line-height: 19px;
      margin-bottom: 15px;
    }
    &-title {
      font-family: var(--kui-font-condensed);
      font-size: 22px;
      line-height: 28.6px;
      color: var(--kui-grey-500);
      font-weight: 700;
      text-transform: uppercase;

      @include media-breakpoint-down(md) {
        font-size: 18px;
        line-height: 23px;
      }
    }
  }
}
