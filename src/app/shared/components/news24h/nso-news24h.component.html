<div class="news24h">
  <ng-container *ngFor="let item of data">
    <article class="news-wrapper">
      <div class="date">
        <span class="date-time">{{ item.time }}</span>
        <span class="date-separator"> | </span>
        <span class="date-day">{{ item.day }}</span>
      </div>
      <section class="article">
        <div class="article-img" [ngStyle]="{ 'background-image': 'url(' + item.img + ')' }"></div>
        <div class="article-text">
          <h5 class="article-tag">{{ item.tag }}</h5>
          <h4 class="article-title">{{ item.title }}</h4>
        </div>
      </section>
    </article>
    <hr />
  </ng-container>
</div>
