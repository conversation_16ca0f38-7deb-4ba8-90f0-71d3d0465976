@use 'shared' as *;

:host {
  display: block;
  margin-bottom: 20px;
  width: 100%;

  .video-card {
    &-thumbnail-box {
      position: relative;

      @include media-breakpoint-down(md) {
        width: 100%;
      }
    }
  }

  &.style-FeaturedImgColumnTitleDate {
    .video-card {
      &-thumbnail {
        object-fit: cover;
        width: 100%;
        cursor: pointer;
      }

      &-thumbnail-box {
        position: relative;
        margin-bottom: 16px;
      }

      &-title {
        font-size: 22px;
        font-family: var(--kui-font-condensed);
        text-transform: uppercase;
        line-height: 28px;
        color: var(--kui-gray-600);
        cursor: pointer;
      }

      &-column {
        @extend %basic-column-style;

        &-link {
          cursor: pointer;
        }
      }

      &-date {
        color: var(--kui-gray-550);
      }
    }
  }

  &.style-FeaturedBigTitleInsideImg,
  &.style-FeaturedTitleInsideImg {
    .video-card {
      &-thumbnail {
        object-fit: cover;
        width: 100%;
        cursor: pointer;
      }

      &-title {
        color: var(--kui-white);
        position: absolute;
        font-size: 14px;
        line-height: 18px;
        max-width: 85%;
        bottom: 16px;
        left: 9px;
        cursor: pointer;
        font-family: var(--kui-font-condensed);
        text-transform: uppercase;
      }
    }
  }

  &.style-FeaturedBigTitleInsideImg {
    .video-card {
      .icon {
        top: 40%;
      }

      &-thumbnail {
        &,
        &-box {
          min-height: 190px;
        }
      }

      &-title {
        left: 20px;
        font-size: 18px;
        line-height: 23px;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        overflow: hidden;
        -webkit-line-clamp: 2;
      }
    }
  }

  &.style-FeaturedSidedImgTitleLead {
    .video-card {
      &-figure {
        display: flex;
        align-items: flex-start;
        gap: 24px;

        @include media-breakpoint-down(md) {
          flex-direction: column;
          gap: 17px;
        }
      }

      &-thumbnail {
        @extend %basic-thumbnail-style;
        max-width: 630px;
        width: 100%;

        @include media-breakpoint-down(lg) {
          width: 435px;
        }

        @include media-breakpoint-down(md) {
          &,
          &-box {
            width: 100%;
          }
        }
      }

      &-title {
        @extend %basic-title-style;
        font-size: 22px;
        line-height: 28px;
        margin-bottom: 8px;
        color: var(--kui-white);
      }

      &-lead {
        font-weight: 400;
        font-size: 16px;
        line-height: 25px;
        color: var(--kui-white);
      }
    }
  }

  &.style-FeaturedSidedImgColumnTitleLead {
    .video-card {
      &-divider {
        border: 1px solid var(--kui-gray-200);
        margin: 0 10px;
      }

      &-thumbnail {
        transition: all 0.3s ease-in-out;
      }

      &-thumbnail-box {
        overflow: hidden;
        flex: 0 0 auto;
        max-width: 303px;
        width: 100%;

        @include media-breakpoint-down(md) {
          max-width: 100%;
        }
      }

      &:hover {
        .video-card {
          &-thumbnail {
            transform: scale(1.1);
          }
        }
      }

      &-figure {
        display: flex;
        align-items: flex-start;
        gap: 24px;

        @include media-breakpoint-down(md) {
          flex-direction: column;
          gap: 16px;
        }
      }

      &-thumbnail {
        @extend %basic-thumbnail-style;
        max-width: 303px;
        width: 100%;

        @include media-breakpoint-down(md) {
          max-width: 100%;
        }
      }

      &-data {
        margin-bottom: 8px;
      }

      &-column {
        @extend %basic-column-style;
        font-size: 16px;
        line-height: 19px;
      }

      &-date {
        font-weight: 400;
        font-size: 16px;
        line-height: 25px;
        color: var(--kui-gray-550);
      }

      &-title {
        @extend %basic-title-style;
        font-size: 22px;
        line-height: 28px;
        color: var(--kui-gray-600);
        margin-bottom: 8px;
      }

      &-lead {
        font-weight: 400;
        font-size: 16px;
        line-height: 25px;
        color: var(--kui-gray-550);
      }
    }
  }
}

.icon {
  top: 50%;
  left: 50%;
  position: absolute;
  transform: translate(-50%, -50%);

  &-video-play {
    width: 64px;
    height: 64px;

    @include media-breakpoint-down(md) {
      width: 48px;
      height: 48px;
    }
  }

  &-box {
    content: '';
    cursor: pointer;
    position: absolute;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.35), rgba(0, 0, 0, 0.2));
    display: block;
    bottom: 0;
    left: 0;
    right: 0;
    top: 0;
  }
}

%basic-column-style {
  font-family: var(--kui-font-primary);
  color: var(--kui-red-400);
  font-weight: 700;
  cursor: pointer;
}

%basic-title-style {
  font-family: var(--kui-font-condensed);
  font-weight: 700;
  text-transform: uppercase;
  cursor: pointer;
}

%basic-thumbnail-style {
  object-fit: cover;
  cursor: pointer;
}
