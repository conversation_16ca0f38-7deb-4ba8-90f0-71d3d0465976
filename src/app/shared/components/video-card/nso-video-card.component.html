<div *ngIf="data" [class]="hostClass">
  <ng-container [ngSwitch]="cardType">
    <!-- VideoCardType.FeaturedSidedImgColumnTitleLead -->
    <ng-container *ngSwitchCase="VideoCardType.FeaturedSidedImgColumnTitleLead">
      <figure class="video-card-figure">
        <div class="video-card-thumbnail-box">
          <ng-container *ngTemplateOutlet="VideoCardImage"></ng-container>
        </div>
        <figcaption>
          <div class="video-card-data">
            <span
              class="video-card-column"
              [ngStyle]="{
                color: data?.category?.color,
              }"
              [routerLink]="columnLink"
            >
              {{ data?.category?.name }}
            </span>
            <span class="video-card-divider"></span>
            <span class="video-card-date">{{ data.publishDate | publishDate }}</span>
          </div>
          <a class="video-card-link" [routerLink]="videoLink">
            <h2 class="video-card-title">{{ data?.title }}</h2>
          </a>
        </figcaption>
      </figure>
    </ng-container>

    <!-- VideoCardType.FeaturedSidedImgTitleLead -->
    <ng-container *ngSwitchCase="VideoCardType.FeaturedSidedImgTitleLead">
      <figure class="video-card-figure">
        <div class="video-card-thumbnail-box">
          <ng-container *ngTemplateOutlet="VideoCardImage"></ng-container>
        </div>
        <figcaption>
          <a class="video-card-link" [routerLink]="videoLink">
            <h2 class="video-card-title">{{ data?.title }}</h2>
          </a>
          <div class="video-card-lead">{{ data?.lead }}</div>
        </figcaption>
      </figure>
    </ng-container>

    <!-- VideoCardType.FeaturedTitleInsideImg -->
    <ng-container *ngSwitchCase="VideoCardType.FeaturedTitleInsideImg">
      <figure class="video-card-figure">
        <div class="video-card-thumbnail-box">
          <ng-container *ngTemplateOutlet="VideoCardImage"></ng-container>
          <ng-container *ngTemplateOutlet="VideoCardTitle"></ng-container>
        </div>
      </figure>
    </ng-container>

    <!-- VideoCardType.FeaturedBigTitleInsideImg -->
    <ng-container *ngSwitchCase="VideoCardType.FeaturedBigTitleInsideImg">
      <div class="video-card-thumbnail-box">
        <ng-container *ngTemplateOutlet="VideoCardImage"></ng-container>
        <ng-container *ngTemplateOutlet="VideoCardTitle"></ng-container>
      </div>
    </ng-container>

    <!-- VideoCardType.FeaturedImgColumnTitleDate -->
    <ng-container *ngSwitchCase="VideoCardType.FeaturedImgColumnTitleDate">
      <figure class="video-card-figure">
        <div class="video-card-thumbnail-box">
          <ng-container *ngTemplateOutlet="VideoCardImage"></ng-container>
        </div>
        <div class="video-card-column">
          <span class="video-card-column-link" [style.color]="data?.category?.color" [routerLink]="columnLink">
            {{ data?.category?.name }}
          </span>
          <span class="video-card-divider"></span>
          <span class="video-card-date">{{ data.publishDate | publishDate }}</span>
        </div>
        <ng-container *ngTemplateOutlet="VideoCardTitle"></ng-container>
      </figure>
    </ng-container>
  </ng-container>
</div>

<ng-template #VideoCardImage>
  <img
    withFocusPoint
    [data]="data?.thumbnailFocusedImages || data?.coverImageFocusedImages"
    class="video-card-thumbnail"
    [routerLink]="videoLink"
    [displayedUrl]="data?.thumbnail?.url || 'assets/images/nemzetisport.png'"
    [displayedAspectRatio]="{ desktop: '16:9' }"
    [alt]="data?.thumbnail?.alt"
    loading="lazy"
  />
  <div class="icon-box" [routerLink]="videoLink">
    <i class="icon icon-video-play"></i>
  </div>
</ng-template>

<ng-template #VideoCardTitle>
  <figcaption>
    <a class="video-card-link" [routerLink]="videoLink">
      <h2 class="video-card-title">{{ data?.title }}</h2>
    </a>
  </figcaption>
</ng-template>
