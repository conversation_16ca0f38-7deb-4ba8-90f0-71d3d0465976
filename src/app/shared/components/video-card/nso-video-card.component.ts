import { ChangeDetectionStrategy, Component, HostBinding, Input } from '@angular/core';
import { BaseComponent, buildColumnUrl, buildVideoUrl, FocusPointDirective, VideoCard } from '@trendency/kesma-ui';
import { VideoCardType } from '../../definitions';
import { Ng<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, NgSwitch, NgSwitchCase, NgTemplateOutlet } from '@angular/common';
import { RouterLink } from '@angular/router';
import { PublishDatePipe } from '@trendency/kesma-core';

@Component({
  selector: 'nso-video-card',
  templateUrl: './nso-video-card.component.html',
  styleUrls: ['./../../../../../node_modules/@trendency/kesma-ui/src/lib/components/video-card/video-card.component.scss', './nso-video-card.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>Case, <PERSON><PERSON>tyle, <PERSON><PERSON><PERSON>plateOutlet, RouterLink, PublishDatePipe, FocusPointDirective],
})
export class NsoVideoCardComponent extends BaseComponent<VideoCard> {
  public readonly VideoCardType = VideoCardType;
  public cardType?: VideoCardType;
  @HostBinding('class') hostClass = '';

  columnLink: string[] = [];
  videoLink: string[] = [];

  @Input() set styleID(styleID: VideoCardType) {
    this.cardType = styleID;
    this.hostClass = `video-card style-${VideoCardType[styleID]}`;
  }

  protected override setProperties(): void {
    this.videoLink = this.data ? buildVideoUrl(this.data) : [];
    this.columnLink = this.data ? buildColumnUrl(this.data) : [];
  }
}
