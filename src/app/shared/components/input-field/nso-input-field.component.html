<div
  class="nso-input nso-input-group"
  [class.ng-untouched]="_shouldForward('untouched')"
  [class.ng-touched]="_shouldForward('touched')"
  [class.ng-dirty]="_shouldForward('dirty')"
  [class.ng-valid]="_shouldForward('valid')"
  [class.ng-invalid]="_shouldForward('invalid')"
  [class.ng-pending]="_shouldForward('pending')"
>
  <ng-content select="[prefix]"></ng-content>
  <ng-content></ng-content>
  <ng-content select="[suffix]"></ng-content>
</div>
<kesma-form-control-error *ngIf="!hideErrors" [errorKey]="errorKey" [errorContext]="errorContext" class="errors"></kesma-form-control-error>
