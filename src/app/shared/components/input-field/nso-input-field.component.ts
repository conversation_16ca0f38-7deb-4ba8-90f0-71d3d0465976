import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ContentChild,
  Input,
  Renderer2,
  TemplateRef,
  ViewEncapsulation,
} from '@angular/core';
import { NsoTextInputDirective } from '../../directives';
import { AbstractControlDirective } from '@angular/forms';
import { KesmaFormControlComponent, KesmaFormControlErrorComponent } from '@trendency/kesma-ui';
import { NgIf } from '@angular/common';

@Component({
  selector: 'nso-input-field',
  templateUrl: './nso-input-field.component.html',
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [KesmaFormControlErrorComponent, NgIf],
})
export class NsoInputFieldComponent extends KesmaFormControlComponent implements AfterViewInit {
  @Input() prefix?: string | TemplateRef<void>;
  @Input() suffix?: string | TemplateRef<void>;
  @ContentChild(NsoTextInputDirective) _formFieldControl?: any;
  constructor(
    protected override readonly cdr: ChangeDetectorRef,
    protected override readonly renderer: Renderer2
  ) {
    super(cdr, renderer);
  }

  get control(): any {
    return this._formFieldControl.control;
  }

  /**
   * Determines whether a class from the AbstractControlDirective
   * should be forwarded to the host element.
   */
  _shouldForward(prop: keyof AbstractControlDirective): boolean {
    const control = this.control ?? null;
    return control && control[prop];
  }

  ngAfterViewInit(): void {
    this._formFieldControl.removeInputClass();
    this.cdr.markForCheck();
  }
}
