import { ChangeDetectionStrategy, Component, forwardRef } from '@angular/core';
import { ToggleSwitchComponent } from '@trendency/kesma-ui';
import { NG_VALUE_ACCESSOR } from '@angular/forms';

@Component({
  selector: 'nso-toggle-switch',
  templateUrl: '../../../../../node_modules/@trendency/kesma-ui/src/lib/components/toggle-switch/toggle-switch.component.html',
  styleUrls: ['../../../../../node_modules/@trendency/kesma-ui/src/lib/components/toggle-switch/toggle-switch.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: {
    '[id]': 'id',
    // Needs to be removed since it causes some a11y issues.
    '[attr.tabindex]': 'null',
    '[attr.aria-label]': 'null',
    '[attr.name]': 'null',
    '[attr.aria-labelledby]': 'null',
    '[class.focused]': '_focused',
    '[class.checked]': 'checked',
    '[class.disabled]': 'disabled',
  },
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => NsoToggleSwitchComponent),
      multi: true,
    },
  ],
})
export class NsoToggleSwitchComponent extends ToggleSwitchComponent {}
