import { ChangeDetectionStrategy, Component, EventEmitter, HostBinding, Input, OnInit, Output } from '@angular/core';
import { PasswordStrengthMeterComponent } from '@trendency/kesma-ui';
import { map, Observable } from 'rxjs';
import { As<PERSON><PERSON>ip<PERSON>, <PERSON><PERSON>orOf, NgI<PERSON>, NgS<PERSON>, NgSwitchCase, NgTemplateOutlet } from '@angular/common';

@Component({
  selector: 'nso-password-strength-meter',
  templateUrl: './nso-password-strength-meter.component.html',
  styleUrls: [
    '../../../../../node_modules/@trendency/kesma-ui/src/lib/components/password-strength-meter/password-strength-meter.component.scss',
    './nso-password-strength-meter.component.scss',
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgSwitch, NgSwitchCase, NgTemplateOutlet, NgIf, Async<PERSON>ip<PERSON>, <PERSON>ForOf],
})
export class NsoPasswordStrengthMeterComponent extends PasswordStrengthMeterComponent implements OnInit {
  @HostBinding('class') @Input() layout: 'regular' | 'password-reset' = 'regular';
  @Input() errorLabels?: string[];
  @Output() remainingErrors: EventEmitter<string[]> = new EventEmitter<string[]>();

  labelsWithErrorStates$?: Observable<any>;
  override ngOnInit(): void {
    super.ngOnInit();
    this.labelsWithErrorStates$ = this.errors$?.pipe(
      map((foundErrorKeys: string[]) => {
        this.remainingErrors.emit(foundErrorKeys);
        return this.errorsToWatch?.map((errorKey: string, index: number) => ({
          errorKey,
          label: this.errorLabels?.[index],
          isActive: foundErrorKeys.includes(errorKey),
        }));
      })
    );
  }
}
