<ng-container [ngSwitch]="layout">
  <ng-container *ngSwitchCase="'regular'">
    <ng-container *ngTemplateOutlet="top"></ng-container>
    <ng-container *ngTemplateOutlet="progressBar"></ng-container>
    <p class="list-title">A jelszónak tartalmazni kell:</p>
    <ng-container *ngTemplateOutlet="errorLabels"></ng-container>
  </ng-container>
  <ng-container *ngSwitchCase="'password-reset'">
    <p>Fiókja biztonságának érdekében bizonyosodjon meg róla, hogy új jelszava tartalmaz:</p>
    <ng-container *ngTemplateOutlet="errorLabels"></ng-container>
    <ng-container *ngTemplateOutlet="top"></ng-container>
    <ng-container *ngTemplateOutlet="progressBar"></ng-container>
  </ng-container>
</ng-container>

<ng-template #top>
  <div *ngIf="(progressPercent$ | async) || 1; let percent" class="top">
    <span class="title">Je<PERSON><PERSON><PERSON> erőssége</span>
    <span class="strength">
      <span *ngIf="percent < 50" class="strength-weak">Gyenge</span>
      <span *ngIf="percent >= 50 && percent < 75" class="strength-medium">Közepes</span>
      <span *ngIf="percent >= 75" class="strength-strong">Erős</span>
    </span>
  </div>
</ng-template>

<ng-template #progressBar>
  <div class="progress-bar-wrapper">
    <div
      *ngIf="(progressPercent$ | async) || 1; let percent"
      [style.width]="percent + '%'"
      [class.strength-medium]="percent >= 50"
      [class.strength-strong]="percent >= 75"
      class="progress-bar"
    ></div>
  </div>
</ng-template>

<ng-template #errorLabels>
  <ng-container *ngIf="labelsWithErrorStates$ | async as error">
    <ul class="list">
      <li *ngFor="let e of error" [class.bold]="e.isActive">
        <span>{{ e.label ?? e.errorKey }}</span>
      </li>
    </ul>
  </ng-container>
</ng-template>
