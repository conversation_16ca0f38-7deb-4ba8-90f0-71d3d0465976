@use 'shared' as *;

:host {
  display: block;
  background-color: var(--kui-gray-50);
  padding: 25px;
  border-radius: 5px;
  font-size: 12px;
  line-height: 14px;

  &.password-reset {
    p {
      font-size: 16px;
      line-height: 25px;
      margin: 25px 0;
    }

    .top {
      margin-top: 28px;
    }

    li {
      font-size: 15px;
      line-height: 19px;
    }
  }
}
.bold {
  font-weight: bold;
}
.top {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  .title {
    font-size: 16px;
  }
  .strength {
    text-align: right;
    color: var(--kui-red-400);
    .strength-medium {
      color: var(--kui-yellow-600);
    }
    .strength-strong {
      color: var(--kui-green-400);
    }
  }
}
.progress-bar-wrapper {
  margin: 10px 0;
  .progress-bar {
    &.strength {
      background-color: var(--kui-red-400);

      &-medium {
        background-color: var(--kui-yellow-600);
      }

      &-strong {
        background-color: var(--kui-green-400);
      }
    }
  }
}
.list-title {
  font-weight: bold;
  margin: 20px 0;
}
.list {
  line-height: 16px;
  list-style-type: disc;
  list-style-position: inside;
  li {
    margin-bottom: 20px;
    span {
      margin-left: -10px;
    }
  }
}
