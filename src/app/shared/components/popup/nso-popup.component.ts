import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';
import { NgIf } from '@angular/common';
import { NsoSimpleButtonComponent } from '../simple-button/nso-simple-button.component';

@Component({
  selector: 'nso-popup',
  templateUrl: 'nso-popup.component.html',
  styleUrls: ['nso-popup.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NsoSimpleButtonComponent],
})
export class NsoPopupComponent {
  @Input() title?: string;

  @Input() showAcceptButton = true;
  @Input() showCancelButton = true;

  @Input() acceptButtonLabel: string = 'Rendben';
  @Input() cancelButtonLabel: string = 'Mégsem';

  @Input() disabled = false;

  @Output() resultEvent: EventEmitter<boolean> = new EventEmitter<boolean>();

  handleUserChoice(answer: boolean): void {
    if (!this.disabled) {
      this.resultEvent.emit(answer);
    }
  }
}
