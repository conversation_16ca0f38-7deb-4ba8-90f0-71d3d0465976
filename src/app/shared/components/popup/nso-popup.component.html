<div class="popup">
  <div class="popup-backdrop" (click)="handleUserChoice(false)"></div>
  <div class="popup-wrapper">
    <div class="popup-title" *ngIf="title">{{ title }}</div>
    <div class="popup-content">
      <ng-content></ng-content>
    </div>
    <div class="popup-buttons-wrapper">
      <nso-simple-button class="w-100" round="round" *ngIf="showAcceptButton" [disabled]="disabled" (click)="handleUserChoice(true)">{{
        acceptButtonLabel
      }}</nso-simple-button>
      <nso-simple-button class="w-100" round="round" color="outline" *ngIf="showCancelButton" [disabled]="disabled" (click)="handleUserChoice(false)">{{
        cancelButtonLabel
      }}</nso-simple-button>
    </div>
  </div>
</div>
