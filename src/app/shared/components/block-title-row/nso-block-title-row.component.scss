@use 'shared' as *;

:host {
  display: block;
  width: 100%;
  margin-bottom: 24px;

  .block {
    &-container {
      display: flex;
      align-items: center;
      justify-content: space-between;
      column-gap: 24px;
    }

    &-title {
      flex: 1;
      font-size: 24px;
      line-height: 31px;
      font-weight: 700;
      font-family: var(--kui-font-primary);
      color: var(--kui-gray-600);

      @include media-breakpoint-down(md) {
        font-size: 20px;
        line-height: 25px;
      }

      &.link {
        color: var(--kui-red-400);
      }
    }

    &-link {
      max-width: 60%;
      font-family: var(--kui-font-primary);
      font-size: 16px;
      line-height: 19px;
      font-weight: 700;
      color: var(--kui-red-400);
    }

    &-name {
      border-bottom: 1.5px solid var(--kui-red-400);
    }
  }

  .csupasport {
    color: #ffe035 !important;
    padding: 8px 16px;
    border-left: 3px solid #1ba1c7;
    background-color: #646464;
    width: fit-content;
  }

  .hatsofuves {
    padding: 2px 4px;
    background-color: #cfc621;
    color: var(--kui-black) !important;
    width: fit-content;
  }
}
