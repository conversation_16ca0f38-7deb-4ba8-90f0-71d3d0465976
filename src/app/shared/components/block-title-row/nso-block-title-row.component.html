<ng-container *ngIf="!data?.url; else content">
  <h2 class="block-title" [class.csupasport]="isCsupasport()" [class.hatsofuves]="isHatsofuves()" [class.link]="desktopWidth >= 6">{{ data?.text }}</h2>
</ng-container>

<ng-template #content>
  <ng-container *ngIf="data?.urlName; else linkWithoutUrlName">
    <ng-container *ngTemplateOutlet="linkWithUrlName"></ng-container>
  </ng-container>

  <ng-template #linkWithoutUrlName>
    <ng-container *ngTemplateOutlet="withUrl; context: { $implicit: true }"></ng-container>
  </ng-template>
</ng-template>

<ng-template #linkWithUrlName>
  <div class="block-container">
    <h2 class="block-title" [class.link]="desktopWidth >= 6" [class.csupasport]="isCsupasport()" [class.hatsofuves]="isHatsofuves()">{{ data?.text }}</h2>
    <ng-container *ngTemplateOutlet="withUrl; context: { $implicit: false }"></ng-container>
  </div>
</ng-template>

<ng-template #withUrl let-isTitle>
  <a class="block-link" *ngIf="!isExternal; else isExternalTemplate" [routerLink]="data?.url">
    <ng-container *ngTemplateOutlet="linkContent; context: { $implicit: isTitle }"></ng-container>
  </a>

  <ng-template #isExternalTemplate>
    <ng-container *ngTemplateOutlet="externalLink; context: { $implicit: isTitle }"></ng-container>
  </ng-template>
</ng-template>

<ng-template #externalLink let-isTitle>
  <a class="block-link" [href]="data?.url" target="_blank">
    <ng-container *ngTemplateOutlet="linkContent; context: { $implicit: isTitle }"></ng-container>
  </a>
</ng-template>

<ng-template #linkContent let-isTitle>
  <h2
    class="block-title"
    [class.link]="data?.url && desktopWidth >= 6"
    [class.csupasport]="isCsupasport()"
    [class.hatsofuves]="isHatsofuves()"
    *ngIf="isTitle; else urlName"
  >
    {{ data?.text }}
  </h2>

  <ng-template #urlName>
    <span class="block-name">{{ data?.urlName }}</span>
  </ng-template>
</ng-template>
