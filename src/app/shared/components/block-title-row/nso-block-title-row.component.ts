import { ChangeDetectionStrategy, Component, computed, inject, OnInit } from '@angular/core';
import { BlockTitleRowComponent } from '@trendency/kesma-ui';
import { NgIf, NgTemplateOutlet } from '@angular/common';
import { RouterLink } from '@angular/router';
import { ColorChangeService } from '../../services';

@Component({
  selector: 'nso-block-title-row',
  templateUrl: './nso-block-title-row.component.html',
  styleUrls: ['./nso-block-title-row.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NgTemplateOutlet, RouterLink],
})
export class NsoBlockTitleRowComponent extends BlockTitleRowComponent implements OnInit {
  private readonly colorChangeService = inject(ColorChangeService);

  readonly isCsupasport = computed(() => this.colorChangeService.isCsupasport());
  readonly isHatsofuves = computed(() => this.colorChangeService.isHatsofuves());

  override ngOnInit(): void {
    super.ngOnInit();
    if (!this.data) {
      this.setData({
        text: 'Még több sport',
      });
    }
  }
}
