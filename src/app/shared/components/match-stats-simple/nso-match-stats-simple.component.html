<!-- Table header -->
<ng-template #matchStatisticsHeader>
  <header class="match-statistic-header">
    <div class="match-statistic-team-container">
      <div class="match-statistic-team-image" [ngStyle]="{ 'background-image': 'url(' + data?.teamA?.teamLogo + ')' }"></div>
      <span class="match-statistic-team-name">{{ data?.teamA?.teamName | uppercase }}</span>
      <span class="match-statistic-team-name-mobile">{{ data?.teamA?.shortName | uppercase }}</span>
    </div>
    <span class="match-statistic-title">{{ 'Mérkőzés statisztika' | uppercase }}</span>
    <div class="match-statistic-team-container">
      <span class="match-statistic-team-name away">{{ data?.teamB?.teamName | uppercase }}</span>
      <span class="match-statistic-team-name-mobile away">{{ data?.teamB?.shortName | uppercase }}</span>
      <div class="match-statistic-team-image" [ngStyle]="{ 'background-image': 'url(' + data?.teamB?.teamLogo + ')' }"></div>
    </div>
  </header>
</ng-template>

<!-- Statistic Team A and B  -->

<ng-template #statisticValueTeamA let-rowData="data">
  <ng-container
    [ngTemplateOutlet]="statistic"
    [ngTemplateOutletContext]="{ data: { current: rowData?.statisticValueTeamA, rowData, team: 'TeamA' } }"
  ></ng-container>
</ng-template>

<ng-template #statisticValueTeamB let-rowData="data">
  <ng-container
    [ngTemplateOutlet]="statistic"
    [ngTemplateOutletContext]="{ data: { current: rowData?.statisticValueTeamB, rowData, team: 'TeamB' } }"
  ></ng-container>
</ng-template>

<!-- Check if home or away team score is bigger -->

<ng-template #statistic let-data="data">
  <span
    [ngClass]="{
      'match-statistic-bigger-value':
        (data?.current > data?.rowData?.statisticValueTeamB && data?.team === 'TeamA') ||
        (data?.current > data?.rowData?.statisticValueTeamA && data?.team === 'TeamB'),
    }"
  >
    {{ data?.current }}
    <ng-container [ngTemplateOutlet]="percentage" [ngTemplateOutletContext]="{ type: data?.rowData?.statisticType }"></ng-container>
  </span>
</ng-template>

<!--if percentage character is required -->

<ng-template #percentage let-type="type">
  <ng-container *ngIf="type === statisticType.BALLPOSSESSION || type === statisticType.SUCCESSFULPASSES">% </ng-container>
</ng-template>

<kesma-data-table-generator [data]="data?.statistics" [dataTableClass]="'match-statistic'" [tableConfig]="tableConfig"></kesma-data-table-generator>
