@use 'shared' as *;

:host::ng-deep {
  display: block;
  width: 100%;
  font-family: var(--kui-font-condensed);

  .match-statistic {
    width: 100%;
    &-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      background-color: var(--kui-red-400);
      height: 74px;
      width: 100%;
      font-size: 24px;
      color: var(--kui-white);
      padding: 16px;

      @include media-breakpoint-down(md) {
        padding: 10px;
      }
    }

    &-title {
      @include media-breakpoint-down(md) {
        text-align: center;
      }
    }

    &-team-container {
      display: flex;
      align-items: center;
    }

    &-team-image {
      width: 42px;
      height: 42px;
      border-radius: 50%;
      flex-shrink: 0;
      background-size: cover;
      background-position: center;

      @include media-breakpoint-down(md) {
        height: 30px;
        width: 30px;
      }
    }

    &-team-name {
      margin-left: 10px;

      @include media-breakpoint-down(md) {
        display: none;
      }
    }

    &-team-name-mobile {
      display: none;

      @include media-breakpoint-down(md) {
        display: block;
        font-size: 18px;
        margin-left: 5px;
      }
    }

    .away {
      margin-left: 0px;
      margin-right: 10px;

      @include media-breakpoint-down(md) {
        margin-right: 5px;
      }
    }

    &-bigger-value {
      font-weight: bold;
    }

    &-statistic-type {
      font-weight: bold;
      text-align: center;
    }

    &-home-statistic-align {
      text-align: left;
      padding-left: 16px;
    }

    &-away-statistic-align {
      text-align: right;
      padding-right: 16px;
    }

    &-first-column-hidden {
      display: none;
    }

    tr {
      height: 61px;
      font-size: 16px;
    }

    tr:nth-child(even) {
      background-color: var(--kui-gray-75);
    }
  }
}
