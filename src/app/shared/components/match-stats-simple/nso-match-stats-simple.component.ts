import { ChangeDetectionStrategy, ChangeDetectorRef, Component, On<PERSON><PERSON>roy, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { BaseComponent, dataTableConfig, DataTableGeneratorComponent } from '@trendency/kesma-ui';
import { MatchStatsSimpleDefinitions, StatisticTypes } from '../../definitions';
import { Subject, debounceTime, distinctUntilChanged, fromEvent, map, startWith, takeUntil } from 'rxjs';
import { NgClass, NgIf, NgStyle, NgTemplateOutlet, UpperCasePipe } from '@angular/common';

@Component({
  selector: 'nso-match-stats-simple',
  templateUrl: './nso-match-stats-simple.component.html',
  styleUrls: ['./nso-match-stats-simple.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgStyle, UpperCasePipe, NgTemplateOutlet, Ng<PERSON><PERSON>, NgIf, DataTableGeneratorComponent],
})
export class NsoMatchStatsSimpleComponent extends BaseComponent<MatchStatsSimpleDefinitions> implements OnInit, OnDestroy {
  private readonly destroy$ = new Subject<void>();

  tableConfig: dataTableConfig[] = [];
  statisticType = StatisticTypes;
  isMobile = window.innerWidth < 450;

  currentWindowWidth$ = fromEvent(window, 'resize').pipe(
    map(() => window.innerWidth),
    startWith(window.innerWidth)
  );

  @ViewChild('matchStatisticsHeader', {
    read: TemplateRef,
    static: true,
  })
  matchStatisticsHeader?: TemplateRef<HTMLElement>;

  @ViewChild('statisticValueTeamA', {
    read: TemplateRef,
    static: true,
  })
  statisticValueTeamA?: TemplateRef<HTMLElement>;

  @ViewChild('statisticValueTeamB', {
    read: TemplateRef,
    static: true,
  })
  statisticValueTeamB?: TemplateRef<HTMLElement>;

  constructor(private readonly cd: ChangeDetectorRef) {
    super();
  }

  override ngOnInit(): void {
    super.ngOnInit();
    this.currentWindowWidth$.pipe(distinctUntilChanged(), debounceTime(10), takeUntil(this.destroy$)).subscribe((width: number) => {
      this.isMobile = width < 450;
      this.cd.markForCheck();
    });
    this.tableConfig = [
      {
        hasHeader: true,
        headerColspan: 4,
        columnDataProperty: 'teamA.teamName',
        headerClass: 'team-stats-header',
        columnClass: 'match-statistic-first-column-hidden',
        customHeaderTemplate: this.matchStatisticsHeader,
      },
      {
        hasHeader: false,
        headerColspan: 1,
        columnDataProperty: 'statisticValueTeamA',
        columnClass: 'match-statistic-home-statistic-align',
        customColumnTemplate: this.statisticValueTeamA,
      },
      {
        hasHeader: false,
        headerColspan: 1,
        columnClass: 'match-statistic-statistic-type',
        columnDataProperty: 'statisticType',
      },
      {
        hasHeader: false,
        headerColspan: 1,
        columnDataProperty: 'statisticValueTeamB',
        columnClass: 'match-statistic-away-statistic-align',
        customColumnTemplate: this.statisticValueTeamB,
      },
    ];
    this.cd.detectChanges();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
