import { ChangeDetectionStrategy, Component } from '@angular/core';
import { ProtectedContentNavigatorComponent } from '@trendency/kesma-ui';
import { AsyncPipe, NgIf } from '@angular/common';
import { RouterLink } from '@angular/router';
import { FormatPipeModule } from 'ngx-date-fns';

@Component({
  selector: 'nso-protected-content-navigator',
  templateUrl: './nso-protected-content-navigator.component.html',
  styleUrls: ['./nso-protected-content-navigator.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, AsyncPipe, RouterLink, FormatPipeModule],
})
export class NsoProtectedContentNavigatorComponent extends ProtectedContentNavigatorComponent {}
