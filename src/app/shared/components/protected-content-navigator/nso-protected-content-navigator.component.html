<div class="navigator" *ngIf="isComponentVisible$ | async">
  <ng-container *ngIf="previousArticleLink; else columnLink">
    <div class="navigator-link">
      <a [routerLink]="previousArticleLink"><PERSON><PERSON><PERSON><PERSON> napilap</a>
    </div>
  </ng-container>
  <div class="navigator-publish-date">{{ article?.publishDate | dfnsFormat: 'yyyy. MM. dd.' }}</div>
  <div class="navigator-link">
    <a [routerLink]="nextArticleLink" *ngIf="nextArticleLink">Követke<PERSON><PERSON> napilap</a>
  </div>
</div>

<ng-template #columnLink>
  <div class="navigator-prev-link">
    <a [routerLink]="['/', 'rovat', 'napilap']"><PERSON><PERSON><PERSON><PERSON><PERSON> napilapok</a>
  </div>
</ng-template>
