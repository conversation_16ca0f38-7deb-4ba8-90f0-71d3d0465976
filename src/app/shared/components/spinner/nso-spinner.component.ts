import { ChangeDetectionStrategy, Component } from '@angular/core';
import { SpinnerComponent } from '@trendency/kesma-ui';

@Component({
  selector: 'nso-spinner',
  templateUrl: '../../../../../node_modules/@trendency/kesma-ui/src/lib/components/spinner/spinner.component.html',
  styleUrls: ['../../../../../node_modules/@trendency/kesma-ui/src/lib/components/spinner/spinner.component.scss'],
  styles: [':host { --spinner-color: var(--kui-red-200) }'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class NsoSpinnerComponent extends SpinnerComponent {}
