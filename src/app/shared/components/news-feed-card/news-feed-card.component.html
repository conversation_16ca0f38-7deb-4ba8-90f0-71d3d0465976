<div class="news-feed-title">
  {{ data?.title }}
</div>
<div class="news-feed">
  <div class="news-feed-thumbnail-box">
    <img class="news-feed-thumbnail" [src]="data?.headerImage || '/assets/images/nemzetisport.png'" alt="Hírfolyam képe" />
  </div>
  <div class="news-feed-right-column">
    <ol class="news-feed-articles">
      <ng-container *ngFor="let article of data?.secondaryArticles | slice: 0 : 3; first as first">
        <li class="news-feed-list-item">
          <div class="news-feed-circle"></div>
          <div>
            <a [routerLink]="buildArticleUrl(article)" class="news-feed-article-title" [class.highlighted]="first">
              {{ article?.title }}
            </a>
            <div class="news-feed-article-lead" *ngIf="first && article?.lead as lead">{{ lead }}</div>
          </div>
        </li>
      </ng-container>
    </ol>
    <nso-simple-button [wide]="true" round="round" [routerLink]="['/', 'hirfolyam', data?.slug]">
      <strong>Tovább a hírfolyamra</strong>
    </nso-simple-button>
  </div>
</div>
