import { ChangeDetectionStrategy, Component } from '@angular/core';
import { BaseComponent, buildArticleUrl, DossierData } from '@trendency/kesma-ui';
import { NgForOf, NgIf, SlicePipe } from '@angular/common';
import { RouterLink } from '@angular/router';
import { NsoSimpleButtonComponent } from '../simple-button/nso-simple-button.component';

@Component({
  selector: 'app-news-feed-card',
  templateUrl: 'news-feed-card.component.html',
  styleUrls: ['news-feed-card.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgForOf, SlicePipe, RouterLink, NgIf, NsoSimpleButtonComponent],
})
export class NewsFeedCardComponent extends BaseComponent<Partial<DossierData>> {
  buildArticleUrl = buildArticleUrl;
}
