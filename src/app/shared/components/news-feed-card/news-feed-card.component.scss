@use 'shared' as *;

:host {
  display: block;
  margin-bottom: 20px;

  .news-feed {
    display: flex;
    background-color: var(--kui-gray-600);

    @include media-breakpoint-down(lg) {
      flex-direction: column;
    }

    &-title {
      background-color: var(--kui-red-400);
      font-family: var(--kui-font-primary);
      color: var(--kui-white);
      padding: 10px 20px;
      font-weight: bold;
    }

    &-article-title {
      @extend %max-three-line;
      color: var(--kui-white);
      line-height: 22px;
      font-weight: bold;

      &.highlighted {
        font-size: 18px;
      }
    }

    &-article-lead {
      @extend %max-three-line;
      color: var(--kui-gray-250);
      margin-top: 10px;
      font-size: 14px;
    }

    &-thumbnail-box,
    &-right-column {
      flex-basis: 50%;
    }

    &-list-item {
      display: flex;
      align-items: flex-start;
      gap: 20px;
    }

    &-circle {
      clip-path: circle();
      background-color: red;
      margin-top: 4px;
      padding: 4px;
    }

    &-right-column {
      padding: 24px;
      display: flex;
      justify-content: space-between;
      flex-direction: column;
      gap: 20px;
    }

    &-thumbnail {
      height: 100%;
      width: 100%;
      object-fit: cover;
      aspect-ratio: 4 / 3;
    }

    &-articles {
      display: flex;
      flex-direction: column;
      gap: 24px;
    }
  }
}

%max-three-line {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
