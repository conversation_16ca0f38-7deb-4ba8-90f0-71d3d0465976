import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { ArticleCard } from '@trendency/kesma-ui';
import { NgForOf } from '@angular/common';
import { NsoArticleCardComponent } from '../article-card/nso-article-card.component';
import { ArticleCardType } from '../../definitions';

@Component({
  selector: 'app-category-article-list[articles]',
  templateUrl: './category-article-list.component.html',
  styleUrls: ['./category-article-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgForOf, NsoArticleCardComponent],
})
export class CategoryArticleListComponent {
  @Input() articles: ArticleCard[];

  public readonly ArticleCardType = ArticleCardType;
  public readonly cardType = ArticleCardType.FeaturedSidedImgColumnTitleLead;
}
