@use 'shared' as *;

:host {
  display: block;
  margin-bottom: 20px;
  width: 100%;
  font-family: var(--kui-font-primary);

  .article-header {
    &-column {
      color: var(--kui-red-400);
      font-weight: 700;
      cursor: pointer;
      font-size: 16px;
      line-height: 19px;
      margin-bottom: 7px;

      .article-header-date {
        font-weight: 400;
        font-size: 16px;
        line-height: 25px;
        color: var(--kui-gray-550);
      }
    }
    &-divider {
      border: 1px solid var(--kui-gray-200);
      margin: 0 10px;
    }

    &-title {
      font-family: var(--kui-font-condensed);
      font-weight: 700;
      line-height: 52.8px;
      font-size: 48px;
      letter-spacing: -2px;
      text-transform: uppercase;
      margin-bottom: 20px;

      @include media-breakpoint-down(md) {
        font-size: 38px;
        line-height: 110%;
        letter-spacing: -0.02em;
      }
    }
    &-info,
    &-info-start {
      display: flex;
      align-items: center;
    }

    &-info {
      justify-content: space-between;
      flex-wrap: wrap;
      gap: 20px 10px;

      @media screen and (max-width: 390px) {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
      }
    }

    &-avatar {
      width: 48px;
      height: 48px;
      border: 1px solid var(--kui-gray-200);
      object-fit: cover;
      border-radius: 50%;
      margin-right: 10px;
    }

    &-author {
      color: var(--kui-red-400);
      font-style: normal;
      font-weight: 700;
      font-size: 16px;
      line-height: 130%;

      @include media-breakpoint-down(md) {
        font-size: 15.602px;
      }

      &-localization {
        display: flex;
        align-items: center;
      }
    }

    &-social-and-date-infos {
      display: none;
      @include media-breakpoint-down(md) {
        display: block;
      }
    }

    &-date {
      font-weight: 700;
      font-size: 16px;
      line-height: 120%;
      align-items: center;
      letter-spacing: 0.01em;
      color: var(--kui-gray-550);

      &.with-multi-authors {
        margin-top: 10px;
      }

      @include media-breakpoint-down(md) {
        font-weight: 400;
        font-size: 11.7015px;
      }
    }

    &-multi-authors-container {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      gap: 10px;
    }

    &-multi-author-container {
      display: flex;
      flex-direction: row;
      align-items: center;
    }

    &-right-top {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 5px;
    }
  }

  ::ng-deep {
    nso-social-buttons {
      @include media-breakpoint-up(lg) {
        display: none;
      }
    }

    nso-social-buttons > .icon {
      width: 40px;
      height: 40px;
    }
  }
}
