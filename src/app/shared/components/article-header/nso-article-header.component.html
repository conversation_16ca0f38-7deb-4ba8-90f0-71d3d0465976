<div class="article-header">
  <div *ngIf="showMeta" class="article-header-column">
    <span [routerLink]="columnLink" [style.color]="articleCardData?.category?.color" class="article-header-column-link">
      {{ articleCardData?.category?.name }}
    </span>
    <span class="article-header-divider"></span>
    <span *ngIf="showDateMeta && data?.publishDate" class="article-header-date">{{ data?.publishDate | publishDate }}</span>
    <span *ngIf="showLengthMeta" class="article-header-date">{{ articleCardData.length }} perc</span>
  </div>

  <h1 class="article-header-title">{{ data?.title }}</h1>
  <div class="article-header-info">
    <div class="article-header-info-start">
      <ng-container *ngIf="articleCardData?.publicAuthorM2M?.length === 1; then headerWithOneAuthor; else headerWithMoreAuthor"></ng-container>
    </div>

    <div class="article-header-right-top">
      <ng-content select="[social]"></ng-content>
      <div *ngIf="(isMobile$ | async) === false" class="article-header-date">
        {{ data?.publishDate | dfnsFormat: 'yyyy.MM.dd. HH:mm' }}
      </div>
    </div>
  </div>
</div>

<ng-template #headerBottomLeftDesktop>
  <ng-container *ngIf="articleCardData.publicAuthorM2M?.[0] as publicAuthor">
    <img
      [alt]="publicAuthor.fullName || 'Szerző avatar'"
      [src]="publicAuthor.avatar || 'assets/images/nemzetisport.png'"
      class="article-header-avatar"
      loading="lazy"
    />
    <ng-container *ngTemplateOutlet="publicAuthor?.slug ? authorWithSlug : authorWithoutSlug; context: { author: publicAuthor }"></ng-container>
  </ng-container>
</ng-template>

<ng-template #headerBottomLeftMobile>
  <ng-container *ngIf="articleCardData.publicAuthorM2M?.[0] as publicAuthor">
    <img
      [alt]="publicAuthor.fullName || 'Szerző avatar'"
      [src]="publicAuthor.avatar || 'assets/images/nemzetisport.png'"
      class="article-header-avatar"
      loading="lazy"
    />
    <div>
      <ng-container *ngTemplateOutlet="publicAuthor?.slug ? authorWithSlug : authorWithoutSlug; context: { author: publicAuthor }"></ng-container>
      <div class="article-header-date">
        {{ data?.publishDate | dfnsFormat: 'yyyy.MM.dd. HH:mm' }}
      </div>
    </div>
  </ng-container>
</ng-template>

<ng-template #headerWithOneAuthor>
  <ng-container *ngTemplateOutlet="(isMobile$ | async) ? headerBottomLeftMobile : headerBottomLeftDesktop"></ng-container>
  <div class="article-header-author-localization">
    <span *ngIf="data?.publicAuthorLocalization" class="article-header-author"> • {{ data?.publicAuthorLocalization }} </span>
  </div>
</ng-template>

<ng-template #headerWithMoreAuthor>
  <div>
    <div class="article-header-multi-authors-container">
      <ng-container *ngFor="let author of articleCardData?.publicAuthorM2M; trackBy: trackByFn">
        <div class="article-header-multi-author-container">
          <img
            [alt]="author?.fullName || 'Szerző avatar'"
            [src]="author?.avatar || 'assets/images/nemzetisport.png'"
            class="article-header-avatar"
            loading="lazy"
          />
          <ng-container *ngTemplateOutlet="author?.slug ? authorWithSlug : authorWithoutSlug; context: { author }"></ng-container>
        </div>
      </ng-container>
      <div class="article-header-author-localization">
        <span *ngIf="data?.publicAuthorLocalization" class="article-header-author"> • {{ data?.publicAuthorLocalization }} </span>
      </div>
    </div>
    <div *ngIf="isMobile$ | async" class="article-header-date with-multi-authors">
      {{ data?.publishDate | dfnsFormat: 'yyyy.MM.dd. HH:mm' }}
    </div>
  </div>
</ng-template>

<ng-template #authorWithSlug let-author="author">
  <a [routerLink]="['/', 'szerzo', author.slug]" class="article-header-author">{{ author?.fullName }}</a>
</ng-template>
<ng-template #authorWithoutSlug let-author="author">
  <span class="article-header-author">{{ author?.fullName }}</span>
</ng-template>
