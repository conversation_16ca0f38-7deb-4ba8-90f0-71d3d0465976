import { ChangeDetectionStrategy, Component, inject, Input } from '@angular/core';
import { Article, ArticleCard, ArticleHeaderComponent, buildColumnUrl } from '@trendency/kesma-ui';
import { PublishDatePipe, UtilService } from '@trendency/kesma-core';
import { debounceTime, fromEvent, of, startWith } from 'rxjs';
import { map } from 'rxjs/operators';
import { AsyncPipe, NgForOf, NgIf, NgTemplateOutlet } from '@angular/common';
import { RouterLink } from '@angular/router';
import { FormatPipeModule } from 'ngx-date-fns';

@Component({
  selector: 'nso-article-header',
  templateUrl: './nso-article-header.component.html',
  styleUrls: ['./nso-article-header.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, RouterLink, PublishDatePipe, AsyncPipe, FormatPipeModule, NgTemplateOutlet, NgForOf],
})
export class NsoArticleHeaderComponent extends ArticleHeaderComponent {
  private readonly utils = inject(UtilService);
  @Input() showMeta = false;
  @Input() showDateMeta = false;
  @Input() showLengthMeta = false;

  readonly isMobile$ = this.utils.isBrowser()
    ? fromEvent(window, 'resize').pipe(
        debounceTime(300),
        map(() => window.innerWidth <= 768),
        startWith(false)
      )
    : of(false);

  columnLink: string[] = [];

  get articleCardData(): ArticleCard {
    return this.data as ArticleCard;
  }

  protected override setProperties(): void {
    this.columnLink = this.data ? buildColumnUrl(this.data as Article) : [];
  }
}
