@use 'shared' as *;

.article-page-content {
  margin-bottom: $block-bottom-margin;
  line-height: 25px;

  h3 {
    text-transform: uppercase;
    font-weight: bold;
    font-size: 24px;
    line-height: 120%;
    margin-bottom: 20px;
  }
  p {
    margin-bottom: 1em;
    font-weight: 400;
    font-size: 16px;
    line-height: 25px;
  }
  p,
  li {
    font-size: 16px;
    line-height: 160%;
  }

  ol {
    list-style-type: auto;
    padding-left: 40px;
    margin: 30px 0;
    li {
      margin: 0.5em 0;
      padding-left: 10px;
    }
  }

  ul {
    margin: 30px 0;
    li {
      margin: 15px 0;
      padding-left: 60px;
      position: relative;

      &:before {
        position: absolute;
        height: 3px;
        width: 30px;
        content: ' ';
        display: block;
        top: 16px;
        left: 0;
      }
    }
  }
}
