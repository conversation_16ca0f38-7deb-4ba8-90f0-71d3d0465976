import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { AuthorInfoComponent } from '@trendency/kesma-ui';
import { NgIf, Ng<PERSON><PERSON>, Ng<PERSON><PERSON>Case, NgTemplateOutlet } from '@angular/common';

@Component({
  selector: 'nso-author-info',
  templateUrl: './../../../../../node_modules/@trendency/kesma-ui/src/lib/components/author-info/author-info.component.html',
  styleUrls: [
    './../../../../../node_modules/@trendency/kesma-ui/src/lib/components/author-info/author-info.component.scss',
    './nso-author-info.component.scss',
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NgSwitch, NgSwitchCase, NgTemplateOutlet],
})
export class NsoAuthorInfoComponent extends AuthorInfoComponent {
  @Input() override showAuthorDetails = true;
}
