@use 'shared' as *;

:host {
  display: block;
  background: unset;
  width: 100%;
  font-family: var(--kui-font-primary);
  padding: 0;

  .author {
    &-top {
      display: flex;
      align-items: center;
      margin-bottom: 40px;
      max-width: unset;
      width: 100%;
      gap: 10px;
    }

    &-name {
      font-size: 40px;
      color: var(--kui-red-400);
      font-family: var(--kui-font-condensed);
      line-height: 48px;
      font-weight: 700;
      margin-bottom: 0;
    }

    &-image {
      width: 100px;
      height: 100px;
      border: 1px solid var(--kui-gray-200);
      margin: 0;
    }

    &-description {
      width: 100%;
      max-width: unset;

      p {
        font-weight: 400;
        font-size: 16px;
        line-height: 25px;
        color: var(--kui-gray-500);
      }
    }

    &-details {
      display: flex;
      line-height: 25px;
      font-size: 16px;
      font-weight: 400;
      gap: 10px;

      @include media-breakpoint-down(md) {
        flex-direction: column;
        gap: 0;
      }

      & > .author-divider {
        display: block;
        width: 1px;
        height: 26px;
        background: var(--kui-gray-150);

        @include media-breakpoint-down(md) {
          display: none;
        }
      }
    }

    &-divider,
    &-social {
      display: none;
    }
  }
}
