<div *ngIf="data" class="tag-container">
  <ng-container *ngFor="let tag of data">
    <ng-container *ngIf="tag.slug; else noTagLink">
      <a [routerLink]="['/', 'cimke', tag.slug]" class="tag-container-tag">
        {{ tag.title }}
      </a>
    </ng-container>
    <ng-template #noTagLink>
      <span class="tag-container-tag no-link">{{ tag.title }}</span>
    </ng-template>
  </ng-container>
</div>
