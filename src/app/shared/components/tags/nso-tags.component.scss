@use 'shared' as *;

:host {
  display: block;
}

.tag-container {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
  gap: 8px;
  max-width: 250px;

  &-tag {
    color: var(--kui-white);
    font-size: 14px;
    padding: 8px 16px;
    background-color: var(--kui-black);
    border-radius: 24px;
    cursor: pointer;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    &.no-link {
      cursor: default;
    }
  }
}
