import { ChangeDetectionStrategy, Component } from '@angular/core';
import { BaseComponent, Tag } from '@trendency/kesma-ui';
import { NgForOf, NgIf } from '@angular/common';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'nso-tags',
  templateUrl: './nso-tags.component.html',
  styleUrls: ['./nso-tags.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NgForOf, RouterLink],
})
export class NsoTagsComponent extends BaseComponent<Tag[]> {}
