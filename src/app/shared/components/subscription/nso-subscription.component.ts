import { ChangeDetectionStrategy, Component, computed, inject, Input } from '@angular/core';
import { NsoButtonComponent } from '../button/nso-button.component';
import { RouterLink } from '@angular/router';
import { ColorChangeService } from '../../services';
import { IconComponent } from '@trendency/kesma-ui';

@Component({
  selector: 'nso-subscription',
  templateUrl: './nso-subscription.component.html',
  styleUrls: ['./nso-subscription.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [IconComponent, NsoButtonComponent, RouterLink],
})
export class NsoSubscriptionComponent {
  private readonly colorChangeService = inject(ColorChangeService);

  @Input() isSidebar = false;
  @Input() desktopWidth = 12;

  readonly isCsupasport = computed(() => this.colorChangeService.isCsupasport());
  readonly isHatsofuves = computed(() => this.colorChangeService.isHatsofuves());
  readonly redToYellow = computed(() => this.colorChangeService.redToYellow());
  readonly whiteToGray = computed(() => this.colorChangeService.whiteToGray());
  readonly grayToLightGray = computed(() => this.colorChangeService.grayToLightGray());
}
