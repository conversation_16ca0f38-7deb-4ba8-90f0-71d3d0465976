@use 'shared' as *;

:host {
  display: block;
  width: 100%;

  .subscription {
    &-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      background: var(--kui-red-400);
      padding: 16px;

      &.in-sidebar {
        @media screen and (min-width: 992px) and (max-width: 1150px) {
          flex-direction: column;
        }
      }

      .nso-logo {
        width: 76px;
        height: 34px;
      }

      .title {
        font-family: var(--kui-font-condensed);
        font-size: 32px;
        line-height: 41.6px;
        font-weight: 700;
        color: var(--kui-white);
        text-transform: uppercase;
      }
    }

    &-body {
      background: var(--kui-gray-550);
      padding: 16px 24px;

      .text {
        color: var(--kui-white);
        line-height: 25.6px;
        margin-bottom: 16px;
      }

      nso-button {
        &::ng-deep {
          button {
            font-family: var(--kui-font-primary);
            font-size: 16px;
            padding: 10px 34px;
            line-height: 25.6px;
          }
        }
      }
    }
  }
  .csupasport {
    nso-button {
      &::ng-deep {
        button {
          background-color: #ffe035;
          color: #646464;
        }
      }
    }
  }

  .hatsofuves {
    nso-button {
      &::ng-deep {
        button {
          background-color: #cfc621;
          color: var(--kui-black);
        }
      }
    }
  }
}
