@use 'shared' as *;

:host {
  position: relative;
}

.comment-widget {
  font-family: var(--kui-font-primary);
  max-width: 627px;

  &-title {
    font-family: var(--kui-font-condensed);
    font-size: 38px;
    font-weight: 700;
    margin-bottom: 30px;
    text-transform: uppercase;
  }

  &-row {
    display: flex;
    flex-direction: row;
    @include media-breakpoint-down(md) {
      flex-direction: column;
    }
  }

  &-login {
    font-family: var(--kui-font-primary);
    font-weight: 700;
    font-size: 24px;
    line-height: 31px;
    text-align: center;
    @include media-breakpoint-down(md) {
      font-size: 16px;
      line-height: 19px;
    }

    &-content {
      height: 170px;
      border: 1px solid var(--kui-gray-200);
      display: flex;
      justify-content: center;
      align-items: center;
    }

    &-footer {
      background: var(--kui-gray-100);
      display: flex;
      justify-content: flex-end;
      align-items: center;
      margin-top: -10px;
      padding: 10px;
      height: 55px;
      gap: 30px;

      & > * {
        margin: 0;
      }
    }

    &.logged-in .comment-widget-login-content {
      display: block;
      height: auto;

      textarea {
        width: 100%;
      }
    }

    a {
      color: var(--kui-red-400);
      text-decoration: underline;
      cursor: pointer;
    }

    p {
      margin: 0 60px;
      line-height: 31.2px;
    }
  }

  &-note {
    background: var(--kui-gray-100);
    font-family: var(--kui-font-primary);
    font-size: 12px;
    line-height: 16px;
    padding: 15px;
    max-width: 304px;
    max-height: 160px;
    position: absolute;
    right: -30px;
    transform: translateX(100%);

    &-link {
      color: var(--kui-red);
    }

    @include media-breakpoint-down(md) {
      margin-left: 0;
      width: 100%;
      max-width: 100%;
      margin-top: 23px;
      position: static;
      transform: unset;
    }
  }
}

.no-decoration {
  text-decoration: none;
  color: var(--kui-gray-500);
  font-weight: 400;
}
