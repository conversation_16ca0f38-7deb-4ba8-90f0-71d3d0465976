import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';
import { ReactiveFormsModule, UntypedFormBuilder } from '@angular/forms';
import { Router } from '@angular/router';
import { ButtonStyleInfo, CommentAnswerFormComponent } from '@trendency/kesma-ui';
import { NsoCountingTextAreaComponent } from '../counting-text-area/nso-counting-text-area.component';
import { NgIf } from '@angular/common';
import { NsoSimpleButtonComponent } from '../simple-button/nso-simple-button.component';

@Component({
  selector: 'nso-comment-answer-form',
  templateUrl: './nso-comment-answer-form.component.html',
  styleUrls: ['./nso-comment-answer-form.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [ReactiveFormsModule, NsoCountingTextAreaComponent, NgIf, NsoSimpleButtonComponent],
})
export class NsoCommentAnswerFormComponent extends CommentAnswerFormComponent {
  @Input() isLoggedIn = true;
  @Input() canCancel = false;
  @Input() hasLegal = true;
  @Input() submitText = 'Hozzászólok';

  @Output() canceled = new EventEmitter<void>();

  cancelButtonStyle: ButtonStyleInfo = {
    button: {
      'text-decoration': 'none',
      height: '100%',
    },
  };

  submitButtonStyle: ButtonStyleInfo = {
    button: {
      padding: '8px 32px',
    },
  };

  constructor(
    formBuilder: UntypedFormBuilder,
    private readonly router: Router
  ) {
    super(formBuilder);
  }

  onCancel(): void {
    this.canceled.emit();
  }

  promptLogin(to: 'bejelentkezes' | 'regisztracio'): void {
    this.router.navigate([`/${to}`], { queryParams: { redirect: this.router.url } }).then();
  }
}
