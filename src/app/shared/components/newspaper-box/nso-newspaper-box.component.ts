import { ChangeDetectionStrategy, Component } from '@angular/core';
import { ArticleCard, BaseComponent, buildArticleUrl } from '@trendency/kesma-ui';
import { UtilService } from '@trendency/kesma-core';
import { NgIf, NgTemplateOutlet } from '@angular/common';
import { RouterLink } from '@angular/router';
import { NsoSimpleButtonComponent } from '../simple-button/nso-simple-button.component';

@Component({
  selector: 'nso-newspaper-box',
  templateUrl: './nso-newspaper-box.component.html',
  styleUrls: ['./nso-newspaper-box.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, RouterLink, NgTemplateOutlet, NsoSimpleButtonComponent],
})
export class NsoNewspaperBoxComponent extends BaseComponent<ArticleCard> {
  buildArticleUrl = buildArticleUrl;

  constructor(private readonly utils: UtilService) {
    super();
  }

  jumpToNsoClub(): void {
    if (!this.utils.isBrowser()) {
      return;
    }

    window.open('https://napilap.nemzetisport.hu/elofizetes', '_blank');
  }
}
