<div *ngIf="data" class="newspaper">
  <div class="newspaper-title">A mai lapból ajánlju<PERSON></div>
  <div class="newspaper-description">{{ data?.lead }}</div>
  <a [routerLink]="buildArticleUrl(data)" class="newspaper-paper">
    <img [alt]="data?.title" [src]="data?.thumbnail ?? '/assets/images/nemzetisport.png'" loading="lazy" />
    <p>{{ data?.title }}</p>
  </a>
  <div class="newspaper-actions">
    <div class="newspaper-links">
      <ng-container
        *ngTemplateOutlet="newspaperLink; context: { href: 'https://napilap.nemzetisport.hu/', text: 'nyomtatott napilap előfizetőknek' }"
      ></ng-container>
      <ng-container
        *ngTemplateOutlet="
          newspaperLink;
          context: {
            href: 'https://nsklub.hu/Pages/Publication/ItemList/nemzetisport-napilap-elofizetes/',
            text: 'digitális napilap előfizetőknek és vásárlóknak',
          }
        "
      ></ng-container>
    </div>
    <nso-simple-button (click)="jumpToNsoClub()" round="round">ELŐFIZETÉS NYOMTATOTT NAPILAPRA</nso-simple-button>
  </div>
</div>

<ng-template #newspaperLink let-text="text" let-href="href">
  <div class="newspaper-link">
    <i class="icon icon-arrow-right-red"></i>
    <a target="_blank" [href]="href">{{ text }}</a>
  </div>
</ng-template>
