@use 'shared' as *;

:host {
  display: block;
  width: 100%;
}

.newspaper {
  padding: 20px;
  display: block;
  background: var(--kui-gray-550);
  color: var(--kui-white);
  width: 100%;

  &-title {
    background: var(--kui-red-400);
    font-family: var(--kui-font-condensed);
    font-size: 24px;
    line-height: 28px;
    font-weight: 700;
    text-transform: uppercase;
    display: inline-block;
    margin-bottom: 30px;
    padding: 10px 16px;
  }

  &-description {
    font-family: var(--kui-font-condensed);
    text-transform: uppercase;
    font-size: 24px;
    line-height: 28px;
    font-weight: bold;
    margin-bottom: 30px;
    padding: 0;
  }

  &-paper {
    display: block;
    text-align: center;
    cursor: pointer;

    img {
      object-fit: cover;
      width: 100%;
    }

    p {
      color: var(--kui-gray-550);
      background-color: var(--kui-gray-100);
      font-weight: bold;
      font-size: 16px;
      line-height: 20px;
      margin: 0;
      font-family: var(--kui-font-primary);
      padding: 10px;
    }
  }

  &-actions {
    margin-top: 20px;
  }

  &-links {
    margin: 20px 0 40px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;

    @include media-breakpoint-up(sm) {
      text-align: center;
    }
  }

  &-link {
    display: flex;
    align-items: center;
    gap: 5px;
    width: 100%;

    @media screen and (min-width: 992px) and (max-width: 1287px) {
      position: relative;
    }

    @include media-breakpoint-between(sm, lg) {
      justify-content: center;
    }

    a {
      color: var(--kui-gray-300);
      text-decoration: underline;
      font-weight: normal;
      cursor: pointer;
      transition: color 300ms ease-in-out;

      &:hover {
        color: var(--kui-white);
      }
    }
  }
}

.icon {
  min-height: 20px;
  min-width: 20px;
  @media screen and (min-width: 992px) and (max-width: 1287px) {
    position: absolute;
    left: -20px;
  }
}
