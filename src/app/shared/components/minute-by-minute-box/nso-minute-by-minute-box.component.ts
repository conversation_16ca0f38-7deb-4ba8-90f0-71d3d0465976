import { ChangeDetectionStrategy, Component } from '@angular/core';
import { BaseComponent } from '@trendency/kesma-ui';
import { MinuteByMinuteBoxDefinition } from '../../definitions';
import { NsoMinuteByMinuteCardComponent } from './nso-minute-by-minute-card/nso-minute-by-minute-card.component';
import { NgForOf } from '@angular/common';

@Component({
  selector: 'nso-minute-by-minute',
  templateUrl: './nso-minute-by-minute-box.component.html',
  styleUrls: ['./nso-minute-by-minute-box.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NsoMinuteByMinuteCardComponent, NgForOf],
})
export class NsoMinuteByMinuteBoxComponent extends BaseComponent<MinuteByMinuteBoxDefinition> {}
