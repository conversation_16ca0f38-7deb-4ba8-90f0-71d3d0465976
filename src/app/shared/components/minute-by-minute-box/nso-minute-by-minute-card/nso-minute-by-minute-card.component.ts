import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { MinuteByMinuteCardDefinition } from '../../../definitions';

@Component({
  selector: 'nso-minute-by-minute-card',
  templateUrl: './nso-minute-by-minute-card.component.html',
  styleUrls: ['./nso-minute-by-minute-card.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class NsoMinuteByMinuteCardComponent {
  @Input() cardData?: MinuteByMinuteCardDefinition;
}
