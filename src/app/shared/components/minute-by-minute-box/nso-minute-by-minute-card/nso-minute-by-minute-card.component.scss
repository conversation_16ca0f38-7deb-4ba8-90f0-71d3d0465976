@use 'shared' as *;

:host {
  display: flex;
  width: 100%;
  flex-direction: column;
  border: 1px solid var(--kui-gray-200);
  padding: 20px 32px 32px 32px;
  font-family: var(--kui-font-primary);

  @include media-breakpoint-down(md) {
    padding: 10px 16px 16px 16px;
  }
}

.nso-mbym-card-header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding-bottom: 16px;

  @include media-breakpoint-down(md) {
    flex-direction: row-reverse;
    justify-content: start;
    gap: 20px;
  }

  &-time {
    font-size: 16px;
    font-style: normal;
    font-weight: 700;

    @include media-breakpoint-down(md) {
      font-size: 14px;
      font-weight: 350;
    }
  }
  &-author {
    font-size: 16px;
    font-style: normal;
    font-weight: 400;

    @include media-breakpoint-down(md) {
      font-size: 14px;
      font-weight: 600;
    }
  }
}

.nso-mbym-card-title {
  font-family: var(--kui-font-condensed);
  font-size: 22px;
  font-style: normal;
  font-weight: 700;
  text-transform: uppercase;
  color: var(--kui-gray-500);
  padding-bottom: 12px;
}

.nso-mbym-card-content {
  color: var(--kui-gray-500);
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 160%;
}
