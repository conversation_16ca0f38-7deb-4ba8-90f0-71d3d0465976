import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { Comment } from '@trendency/kesma-ui';
import { NgForOf, NgIf } from '@angular/common';
import { RouterLink } from '@angular/router';
import { NsoSimpleButtonComponent } from '../simple-button/nso-simple-button.component';
import { NsoCommentCardComponent } from './components/nso-comment-card/nso-comment-card.component';

@Component({
  selector: 'nso-comment-widget',
  templateUrl: './nso-comment-widget.component.html',
  styleUrls: ['./nso-comment-widget.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, RouterLink, NsoSimpleButtonComponent, NsoCommentCardComponent, NgForOf],
})
export class NsoCommentWidgetComponent {
  @Input() data: Comment[] = [];
  @Input() isLoggedIn: boolean = false;
  @Input() articleTitle: string = '';
}
