@use 'shared' as *;

.comment-widget {
  font-family: var(--kui-font-primary);
  max-width: 627px;
  &-title {
    font-family: var(--kui-font-condensed);
    font-size: 38px;
    font-weight: 700;
    margin-bottom: 30px;
    text-transform: uppercase;
  }
  &-row {
    position: relative;
  }
  &-login {
    font-family: var(--kui-font-primary);
    font-weight: 700;
    font-size: 24px;
    text-align: center;
    margin-bottom: 25px;
    @include media-breakpoint-up(xl) {
      margin-bottom: 37px;
    }
    &-content {
      min-height: 170px;
      border: 1px solid var(--kui-gray-200);
      display: flex;
      justify-content: center;
      align-items: center;
      textarea {
        width: 100%;
        height: 100%;
        resize: none;
      }
    }
    &-footer {
      background: var(--kui-gray-100);
      display: flex;
      justify-content: flex-end;

      nso-simple-button {
        margin: 10px 10px;
        width: 147px;
      }
    }
    &.logged-in .comment-widget-login-content {
      display: block;
      height: auto;
    }
    a {
      color: var(--kui-red-400);
      text-decoration: underline;
    }
    p {
      margin: 0 60px;
      line-height: 31.2px;
    }
  }
  &-note {
    background: var(--kui-gray-100);
    font-family: var(--kui-font-primary);
    font-size: 12px;
    line-height: 16px;
    margin-bottom: 20px;
    padding: 15px 17px 18px 15px;
    &-link {
      color: var(--kui-red-400);
    }
    @include media-breakpoint-up(xl) {
      position: absolute;
      left: 650px;
      top: 0;
      max-width: 304px;
      width: 100%;
    }
  }
  &-ordering {
    font-size: 16px;
    border-bottom: 1px solid var(--kui-gray-300);
    margin-bottom: 26px;
    padding-bottom: 25px;
    &-select {
      font-size: 16px;
      font-weight: 700;
      background: none;
      border: none;
      option {
        font-weight: 400;
      }
    }
  }
}
