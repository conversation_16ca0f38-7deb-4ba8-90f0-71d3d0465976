<div class="comment-widget">
  <h2 class="comment-widget-title">
    {{ articleTitle }}
  </h2>
  <div class="comment-widget-row">
    <div class="comment-widget-login">
      <div class="comment-widget-login-content">
        <p *ngIf="!isLoggedIn"><a routerLink="/bejelentkezes">Jelentkezz be</a>, hogy hozz<PERSON>zólhass a cikkeinkhez!</p>
        <textarea *ngIf="isLoggedIn" id="comment-textarea" name="comment-textarea" rows="10" cols="50"></textarea>
      </div>
      <div class="comment-widget-login-footer">
        <nso-simple-button round="round" [disabled]="!isLoggedIn">Hozzászólok</nso-simple-button>
      </div>
    </div>
    <div class="comment-widget-note">
      Tilos a hozzászólásban linket elhelyezni. Ez alól kivétel az online videó-közvetítések linkjei, és hivatalos sportszövetségek linkjei. A szabály
      megszegése végleges tiltást, és a felhasználó összes hozzászólásának törlését vonja maga után. Bármilyen moderációval kapcsolatos kérdése, észrevétele
      van, elérhet minket a
      <a class="comment-widget-note-link" href="mailto:<EMAIL>">moderator&#64;nemzetisport.hu</a> e-mail címen.
    </div>
  </div>
  <div class="comment-widget-ordering">
    <span class="comment-widget-ordering-title"> Rendezés: </span>
    <select class="comment-widget-ordering-select" name="" id="">
      <option>Legnépszerűbb</option>
      <option>Legújabb</option>
      <option>Legrégebbi</option>
    </select>
  </div>
  <ng-container *ngFor="let comment of data; let isLast = last">
    <nso-comment-card [data]="comment" [isLast]="isLast"></nso-comment-card>
  </ng-container>
</div>
