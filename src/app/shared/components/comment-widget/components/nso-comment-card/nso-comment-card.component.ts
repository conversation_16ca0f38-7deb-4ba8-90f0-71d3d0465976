import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';
import { CommentCardComponent } from '@trendency/kesma-ui';
import { NgClass, NgIf } from '@angular/common';
import { FormatPipeModule } from 'ngx-date-fns';
import { NsoCommentAnswerFormComponent } from '../../../comment-answer-form/nso-comment-answer-form.component';

@Component({
  selector: 'nso-comment-card',
  templateUrl: './nso-comment-card.component.html',
  styleUrls: ['./nso-comment-card.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, FormatPipeModule, NsoCommentAnswerFormComponent, NgClass],
})
export class NsoCommentCardComponent extends CommentCardComponent {
  @Input() level = 0;
  @Input() isLast = false;
  @Input() childrenCount = 0;
  @Input() childrenOpen = false;
  @Input() canUpdate = false;
  @Input() isUpdating = false;

  @Output() report = new EventEmitter<void>();
  @Output() update = new EventEmitter<string>();
  @Output() childrenOpenChange = new EventEmitter<boolean>();

  override maxResponseLevel = 0;

  avatarPlaceholder = 'assets/images/icons/logo-red.svg';

  onReport(): void {
    this.report.emit();
  }

  onUpdate(comment: string): void {
    this.update.emit(comment);
  }

  onSwitchChildrenOpen(): void {
    this.childrenOpen = !this.childrenOpen;
    this.childrenOpenChange.emit(this.childrenOpen);
  }
}
