@use 'shared' as *;

:host {
  display: block;
}
.comment-card {
  &-top-divider {
    display: flex;
    flex-direction: row;
    width: 100%;
    height: 45px;
    margin-bottom: 10px;
    &-vertical-line {
      width: 1px;
      height: 100%;
      background-color: var(--kui-gray-300);
      margin: 0 20px 0 25px;
    }
    &-horizontal-line {
      width: 100%;
      height: 1px;
      background-color: var(--kui-gray-300);
      margin: auto 10px;

      @include media-breakpoint-down(md) {
        display: none;
      }
    }
  }
  &-item {
    display: flex;

    @include media-breakpoint-down(md) {
      flex-direction: column;
    }
    &-avatar {
      display: flex;
      flex-direction: column;
      margin-right: 15px;
      min-width: 50px;

      @include media-breakpoint-down(md) {
        margin-bottom: 10px;
      }

      img {
        border-radius: 50%;
        height: 50px;
        width: 50px;
        margin-right: 15px;
      }

      &-vertical-line {
        margin: 10px auto;
        height: 100%;

        @include media-breakpoint-down(md) {
          display: none;
        }
      }
    }
    &-line {
      height: 100%;
      width: 2px;
      color: var(--kui-gray-300);
      margin: 0 auto;
    }
    &-card {
      color: var(--kui-gray-450);
      width: 100%;

      &-edit {
        display: block;
        margin-bottom: 20px;
      }
      &-header {
        margin-bottom: 10px;
      }
      &-name {
        font-weight: bold;
        margin-right: 15px;
        font-size: 16px;
      }
      &-date {
        color: var(--kui-gray-300);
        font-size: 16px;
      }
      &-body {
        color: var(--kui-gray-450);
        line-height: 1.5;
        margin-bottom: 20px;
        white-space: pre-wrap;
        font-size: 16px;
      }
      &-footer {
        margin-bottom: 20px;
        font-size: 14px;

        @include media-breakpoint-down(md) {
          padding-bottom: 20px;
          border-bottom: 1px solid var(--kui-gray-300);
        }

        &.last {
          padding-bottom: 20px;
          border-bottom: 1px solid var(--kui-gray-300);
        }
      }
      &-feedback {
        display: flex;
        flex-wrap: wrap;
        gap: 15px 30px;

        &-item {
          font-weight: 400;
          color: var(--kui-gray-300);
          cursor: pointer;
          user-select: none;
          font-size: 14px;

          &.text-active {
            font-weight: 700;
          }
        }
        .icon {
          height: 12px;
          width: 12px;
          margin-left: 5px;
          fill: var(--kui-gray-300);
          transition: all 0.2s ease-in-out;
        }
      }
    }
    &-subcomment {
      @include media-breakpoint-down(md) {
        &.with-line {
          border-left: 1px solid var(--kui-gray-300);
        }
        margin-left: 25px;
        padding-left: 25px;
      }
    }
  }
}

.down {
  transform: rotate(90deg);
}

.comment-card-item-card-name,
.comment-card-item-card-date,
.comment-card-item-card-feedback {
  white-space: nowrap;
}
