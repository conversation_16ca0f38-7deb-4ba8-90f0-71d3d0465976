<div *ngIf="level !== 0" class="comment-card-top-divider">
  <div class="comment-card-top-divider-vertical-line"></div>
  <div class="comment-card-top-divider-horizontal-line"></div>
</div>
<div class="comment-card-item" *ngIf="data">
  <div class="comment-card-item-avatar">
    <img class="comment-card-item-avatar" src="{{ data?.author?.avatar ?? avatarPlaceholder }}" alt="{{ data?.author?.username }}" loading="lazy" />
    <div *ngIf="!isLast" class="comment-card-item-avatar-vertical-line"></div>
  </div>
  <div class="comment-card-item-card">
    <div class="comment-card-item-card-header">
      <span class="comment-card-item-card-name">{{ data?.author?.username }}</span>
      <span class="comment-card-item-card-date">{{ data?.createdAt | dfnsFormat: 'yyyy. MM. dd. HH:mm' }}</span>
    </div>
    <p class="comment-card-item-card-body" *ngIf="!isUpdating">{{ data?.text }}</p>
    <nso-comment-answer-form
      class="comment-card-item-card-edit"
      *ngIf="isUpdating"
      [hasLegal]="false"
      [canCancel]="true"
      (canceled)="isUpdating = !isUpdating"
      (onSubmit)="onUpdate($event)"
      [value]="data?.text ?? ''"
      [minLength]="3"
      submitText="Mentés"
    >
    </nso-comment-answer-form>
    <div [ngClass]="{ last: isLast && (data?.childComments ?? []).length <= 0 }" class="comment-card-item-card-footer">
      <div class="comment-card-item-card-feedback">
        <div class="comment-card-item-card-feedback-item" (click)="onSwitchChildrenOpen()" *ngIf="childrenCount > 0">
          <i class="icon icon-chevron-grey" [class.down]="childrenOpen"></i>
          {{ childrenCount }} válasz {{ childrenOpen ? 'elrejtése' : 'megjelenítése' }}
        </div>
        <div [class.text-active]="data?.myReaction === 'like'" class="comment-card-item-card-feedback-item" (click)="onReaction(true)">
          Kedvelem
          <i class="icon icon-checkmark-grey"></i>
          <ng-container *ngIf="data?.likeCount && data.likeCount > 0">
            {{ data.likeCount }}
          </ng-container>
        </div>
        <div class="comment-card-item-card-feedback-item" [class.text-active]="responseFormShown" (click)="onSwitchAnswerInput()">
          Válasz
          <i class="icon icon-message"></i>
        </div>
        <div class="comment-card-item-card-feedback-item" *ngIf="canUpdate" (click)="isUpdating = !isUpdating">Szerkesztés</div>
        <div class="comment-card-item-card-feedback-item" (click)="onReport()">Jelentem</div>
      </div>
    </div>
  </div>
</div>
