@use 'shared' as *;

.article-page-quote {
  margin-bottom: $block-bottom-margin;
  background: var(--kui-gray);
  padding: 29px 114px;

  @include media-breakpoint-down(md) {
    padding: 16px 57px;
  }

  .quote {
    font-style: italic;
    font-weight: normal;
    font-size: 24px;
    line-height: 160%;
    letter-spacing: -0.01em;
    color: var(kui-gray-500);
    position: relative;

    &:before,
    &:after {
      color: var(--kui-gray-500);
      position: absolute;
      display: block;
      font-size: 48px;
    }
    &:before {
      top: 0;
      content: '“';
      left: -30px;
    }
    &:after {
      bottom: 0;
      content: '”';
      right: 0;
    }
  }
}
