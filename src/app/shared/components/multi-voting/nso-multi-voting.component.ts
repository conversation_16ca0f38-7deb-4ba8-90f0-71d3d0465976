import { ChangeDetectionStrategy, Component, Input, OnInit } from '@angular/core';
import { MultiVoteAnswer, MultiVoteComponent, MultiVoteData, MultiVoteQuestion } from '@trendency/kesma-ui';
import { AsyncPipe, DecimalPipe, NgIf, Ng<PERSON>tyle } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { NsoSimpleButtonComponent } from '../simple-button/nso-simple-button.component';

export enum MultiVoteProgress {
  SHOW_VOTE,
  SHOW_THANKS,
  SHOW_RESULTS,
}

@Component({
  selector: 'nso-multi-voting',
  templateUrl: './nso-multi-voting.component.html',
  styleUrls: ['./nso-multi-voting.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, AsyncPipe, ReactiveFormsModule, NgStyle, DecimalPipe, NsoSimpleButtonComponent],
})
export class NsoMultiVotingComponent extends MultiVoteComponent implements OnInit {
  @Input() desktopWidth = 12;
  currentStatus = MultiVoteProgress.SHOW_VOTE;
  readonly progressEnum = MultiVoteProgress;
  readonly colorArray = [
    '#FF6633',
    '#FF33FF',
    '#00B3E6',
    '#E6B333',
    '#3366E6',
    '#999966',
    '#809980',
    '#999933',
    '#FF3380',
    '#4D80CC',
    '#FF4D4D',
    '#99E6E6',
    '#6666FF',
  ];

  get voteData(): MultiVoteData {
    return this.vm.state.voteData;
  }

  get sum(): number {
    return this.voteData?.voteCountSum ?? 0;
  }

  override ngOnInit(): void {
    super.ngOnInit();
    if (this.vm.state.showResults) {
      this.setCurrentStatus();
    }
  }

  override onVote(): void {
    this.setCurrentStatus();
    super.onVote();
  }

  setCurrentStatus(): void {
    switch (this.currentStatus) {
      case MultiVoteProgress.SHOW_VOTE:
        this.currentStatus = MultiVoteProgress.SHOW_THANKS;
        break;
      case MultiVoteProgress.SHOW_THANKS:
        if (this.voteData.isResultVisible) {
          this.currentStatus = MultiVoteProgress.SHOW_RESULTS;
        }
        break;
    }
  }

  calculatePercent(question: MultiVoteQuestion, answer: MultiVoteAnswer, isWidth = false): number {
    const voteSum = question.answers.reduce((acc, curr) => acc + (curr.voteCount ?? 0), 0);
    if (voteSum === 0) {
      return isWidth ? 100 / question.answers.length : 0;
    }
    return ((answer.voteCount ?? 0) / voteSum) * 100;
  }
}
