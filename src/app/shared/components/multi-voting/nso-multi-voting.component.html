<ng-container *ngIf="vm.state$ | async as state">
  <section class="header">
    <h1 class="header-title">SZAVAZÁS</h1>
    <div class="separator"></div>
  </section>

  <form [formGroup]="form" class="body">
    <ul>
      @for (question of state.voteData?.questions ?? []; track question.id) {
        <li>
          <span class="question">{{ question.question }}</span>
          <div class="input-item" [class.narrow-width]="desktopWidth < 5" [class.result]="state.showResults">
            @for (answer of question.answers ?? []; track answer.id) {
              @if (state.showResults) {
                <div class="result-chart" [ngStyle]="{ width: calculatePercent(question, answer, true) + '%', 'background-color': colorArray[$index] }">
                  <span class="result-text">
                    {{ answer.value }} <br />
                    {{ calculatePercent(question, answer) | number: '1.0-0' }}%
                  </span>
                </div>
              } @else {
                <label class="form-control" [class.in-small-width]="desktopWidth < 6 && state.hasExpired">
                  <input
                    [id]="question.id + '-' + answer.id"
                    [class.expired]="state.hasExpired"
                    [name]="question.id"
                    type="radio"
                    [value]="answer.id"
                    [formControlName]="question.id"
                  />
                  <span class="answer">
                    {{ answer.value }}
                  </span>
                </label>
              }
            }
          </div>
        </li>
      }
    </ul>

    <div class="thank-you" [class.in-view]="currentStatus === progressEnum.SHOW_THANKS">
      <p>KÖSZÖNJÜK, HOGY SZAVAZOTT!</p>
    </div>
  </form>

  <section class="footer">
    <ng-container *ngIf="!isExpired">
      <nso-simple-button
        [wide]="true"
        *ngIf="
          (currentStatus === progressEnum.SHOW_VOTE && !state.showResults) || (currentStatus === progressEnum.SHOW_THANKS && state?.voteData?.isResultVisible)
        "
        round="round"
        color="primary"
        [disabled]="!form.valid && currentStatus === progressEnum.SHOW_VOTE"
        (click)="onVote()"
      >
        {{ currentStatus === progressEnum.SHOW_VOTE ? 'SZAVAZOK' : 'MEGNÉZEM A SZAVAZATOKAT' }}
      </nso-simple-button>
    </ng-container>

    <ng-container
      *ngIf="
        (((currentStatus === progressEnum.SHOW_VOTE && state.showResults) || state.hasExpired) && state?.voteData?.isResultVisible) ||
        currentStatus === progressEnum.SHOW_RESULTS
      "
    >
      <div class="separator"></div>
      <div class="result">
        <span class="result-text">Összes szavazat</span>
        <span class="result-number">{{ sum }}</span>
      </div>
    </ng-container>
  </section>
</ng-container>
