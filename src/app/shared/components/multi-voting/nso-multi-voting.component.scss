@use 'shared' as *;
:host {
  background-color: var(--kui-gray-550);
  color: var(--kui-white);
  display: block;
}

.header {
  background-color: var(--kui-red-400);
  padding: 16px 24px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 8px;

  & > * {
    text-align: center;
    text-transform: uppercase;
    font-style: normal;
    color: var(--kui-white);
    font-family: var(--kui-font-condensed);
    font-weight: 700;
    line-height: 130%;
  }

  &-title {
    font-size: 32px;
  }
}

.question {
  font-size: 22px;
}

.thank-you {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 0;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 10;
  background-color: var(--kui-gray-550);
  p {
    display: none;
  }
}

.in-view {
  width: 100%;
  padding: 0 20px;
  text-align: center;
  p {
    display: block;
  }
}

nso-simple-button {
  font-family: var(--kui-font-primary);
  font-size: 16px;
  font-style: normal;
  font-weight: 700;
  line-height: 160%;
}

.footer {
  padding: 16px 24px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 16px;

  .result {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;

    &-text {
      font-family: var(--kui-font-primary);
      font-style: normal;
      font-weight: 700;
      font-size: 16px;
      line-height: 120%;
      letter-spacing: 0.01em;
    }

    &-number {
      font-family: var(--kui-font-condensed);
      font-style: normal;
      font-weight: 700;
      font-size: 32px;
      line-height: 110%;
      letter-spacing: -0.02em;
      text-transform: uppercase;
    }
  }
}

.answer {
  display: inline-flex;
  flex-direction: column;
  gap: 10px;

  :first-child {
    @include media-breakpoint-down(md) {
      width: 100%;
    }
  }
}

.highlighted-vote-count {
  font-weight: bold;
  width: 100%;
  display: inline-block;
  text-align: end;
  margin-top: 5px;
}

.body {
  padding: 18px 26px 0 26px;
  position: relative;

  ul {
    display: flex;
    flex-direction: column;
    gap: 19px;

    .input-item {
      display: flex;
      gap: 6px;
      flex-wrap: nowrap;
      justify-content: space-evenly;
      margin-top: 8px;

      &.narrow-width {
        flex-wrap: wrap;
      }

      .highlighted-vote-count {
        width: unset;
        margin-left: auto;
      }

      &.result {
        gap: 0;
        justify-content: flex-start;
      }
    }
  }
}

.separator {
  border-bottom: 1px solid var(--kui-white, #fff);
}

/** Radio Buttons */
.form-control {
  font-family: var(--kui-font-primary);
  font-size: 16px;
  font-weight: 400;
  line-height: 1.6;
  display: grid;
  grid-template-columns: 20px;
  grid-template-rows: 20px auto;
  gap: 4px;
  align-content: center;
  align-items: center;
  justify-content: center;
  justify-items: center;

  &.in-small-width {
    display: block;
  }
}

input[type='radio'] {
  /* Remove most all native input styles */
  appearance: none;
  margin: 0;

  font: inherit;
  color: var(--kui-red-400);
  width: 20px;
  height: 20px;
  border: 2px solid var(--kui-white);
  border-radius: 50%;
  transform: translateY(-0.075em);

  display: grid;
  place-content: center;

  &::before {
    content: '';
    width: 0.65em;
    height: 0.65em;
    border-radius: 50%;
    transform: scale(0);
    transition: 150ms all ease-in-out;
    box-shadow: inset 1em 1em var(--form-control-color);
  }

  &:checked {
    border: 2px solid var(--kui-red-400);
    &::before {
      transform: scale(1);
      background-color: var(--kui-red-400);
    }
  }

  &.expired {
    display: none;
  }
}

.result-chart {
  padding: 6px 6px 0;
  min-width: 30px;
}
