import { ChangeDetectionStrategy, Component, Inject, OnInit, Optional } from '@angular/core';
import { RESPONSE, SeoService, UtilService } from '@trendency/kesma-core';
import { defaultMetaInfo } from '../../../constants';
import type { Response } from 'express';
import { NsoErrorPageComponent } from '../../error-page/nso-error-page.component';

@Component({
  selector: 'app-404',
  templateUrl: './404.component.html',
  styleUrls: ['./404.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NsoErrorPageComponent],
})
export class Error404Component implements OnInit {
  constructor(
    private readonly seo: SeoService,
    private readonly utilsService: UtilService,
    @Inject(RESPONSE) @Optional() private readonly response: Response
  ) {}

  ngOnInit(): void {
    this.seo.setMetaData({
      ...defaultMetaInfo,
      title: `404 - ${defaultMetaInfo.ogTitle}`,
      ogTitle: `404 - ${defaultMetaInfo.ogTitle}`,
    });
    if (!this.utilsService.isBrowser() && this.response) {
      this.response.status(404);
    }
  }
}
