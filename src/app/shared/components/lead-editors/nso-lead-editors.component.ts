import { ChangeDetectionStrategy, Component } from '@angular/core';
import { BaseComponent } from '@trendency/kesma-ui';
import { NgForOf } from '@angular/common';

@Component({
  selector: 'nso-lead-editors',
  templateUrl: './nso-lead-editors.component.html',
  styleUrls: ['./nso-lead-editors.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgForOf],
})
export class NsoLeadEditorsComponent extends BaseComponent<string[]> {}
