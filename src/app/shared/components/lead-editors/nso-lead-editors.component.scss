@use 'shared' as *;

:host {
  display: block;
  margin-bottom: 20px;
  width: 100%;

  .lead-editors {
    &-list {
      display: flex;
      flex-direction: column;
      column-gap: 35px;
      background: var(--kui-gray-100);
      border: 1px solid var(--kui-gray-200);
      padding: 14px 24px;
    }

    &-item {
      margin: 16px 0;
      font-family: var(--kui-font-primary);
      font-weight: 700;
      font-size: 16px;
      color: var(--kui-red-400);
      line-height: 20px;
    }
  }
}
