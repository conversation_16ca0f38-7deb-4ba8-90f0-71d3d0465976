@use 'shared' as *;

:host {
  display: block;
  margin-bottom: 20px;
  border: 1px solid var(--kui-gray-200);

  .related-articles {
    display: flex;
    flex-wrap: wrap;
    background: var(--kui-white);
    padding: 24px 32px;
    gap: 16px;

    &-card {
      width: calc(50% - 8px);

      @include media-breakpoint-down(md) {
        width: 100%;
      }
    }

    &-title {
      background: var(--kui-gray-100);
      border-bottom: 1px solid var(--kui-gray-200);
      padding: 16px 32px;
      font-weight: bold;
      font-size: 24px;
      line-height: 28.8px;
      color: var(--kui-gray-550);
      text-transform: uppercase;
      font-family: var(--kui-font-condensed);
      white-space: nowrap;
    }
  }
}
