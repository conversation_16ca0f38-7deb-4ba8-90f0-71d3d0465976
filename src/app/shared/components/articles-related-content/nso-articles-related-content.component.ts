import { ChangeDetectionStrategy, Component } from '@angular/core';
import { ArticleCard, BaseComponent } from '@trendency/kesma-ui';
import { ArticleCardType } from '../../definitions';
import { NsoArticleCardComponent } from '../article-card/nso-article-card.component';

@Component({
  selector: 'nso-articles-related-content',
  templateUrl: './nso-articles-related-content.component.html',
  styleUrls: ['./nso-articles-related-content.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NsoArticleCardComponent],
})
export class NsoArticlesRelatedContentComponent extends BaseComponent<ArticleCard[]> {
  readonly articleCardType: ArticleCardType = ArticleCardType.FeaturedImgTitleLead;
}
