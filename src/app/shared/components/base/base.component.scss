@use 'shared' as *;

.content-wrap {
  width: calc(100% - 140px);
  margin: auto;

  &.content-wrap-full-width {
    width: 100%;
    background-color: var(--kui-gray-75);
  }

  @include media-breakpoint-down(md) {
    width: 100%;
  }
}

.nso-header {
  @include media-breakpoint-down(md) {
    display: block;
    padding-top: 49px;
  }
}

.border {
  border-left: 6px solid #32abcd;
  border-right: 6px solid #97cc29;
}

.header-banner-container {
  position: relative;

  .csupasport-banner,
  .hatsofuves-banner {
    object-fit: cover;
    width: 100%;

    @include media-breakpoint-down(md) {
      height: 180px;
    }
  }

  .csupasport-banner {
    object-position: left;

    @include media-breakpoint-down(sm) {
      object-position: -170px;
    }
  }

  .hatsofuves-banner {
    object-position: 80% 50%;

    @include media-breakpoint-down(xs) {
      object-position: -770px;
    }
  }

  .csupasport-logo,
  .hatsofuves-logo {
    position: absolute;
    right: 22px;
    top: 50%;
    transform: translateY(-50%);

    @include media-breakpoint-up(lg) {
      position: absolute;
      right: 264px;
    }
  }

  .csupasport-logo {
    width: 148px;
    height: 74px;

    @include media-breakpoint-up(lg) {
      width: 215px;
      height: 108px;
    }
  }

  .hatsofuves-logo {
    width: 122px;
    height: 122px;
  }
}
