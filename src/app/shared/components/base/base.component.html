<app-header [mainMenu]="mainMenu" [secondaryMenu]="topMenu" [user]="user"></app-header>
@if (headerBanner(); as banner) {
  <div class="header-banner-container" [class.border]="isCsupasport()">
    <img [src]="'/assets/images/' + banner + '-banner.webp'" [alt]="banner + ' banner'" [class]="banner + '-banner'" />
    <img [src]="'/assets/images/' + banner + '-logo.webp'" [alt]="banner + ' logo'" [class]="banner + '-logo'" />
  </div>
}
<app-strossle-advert *ngIf="isIos()" [element]="{ id: 'Nso_app_1' }"></app-strossle-advert>

<div class="content-wrap" [class.content-wrap-full-width]="(isFullWidth$ | async) === true">
  <router-outlet></router-outlet>
</div>

<app-strossle-advert *ngIf="isIos()" [element]="{ id: 'Nso_app_anchor' }"></app-strossle-advert>

<app-footer [data]="footer"></app-footer>
