import { AsyncPipe, DOCUMENT, NgIf } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, computed, inject, OnDestroy, OnInit, Signal } from '@angular/core';
import { ActivatedRoute, ActivatedRouteSnapshot, NavigationEnd, Router, RouterOutlet } from '@angular/router';
import { UtilService } from '@trendency/kesma-core';
import { BreakingBlock, InitResolverData, SimplifiedMenuItem, User } from '@trendency/kesma-ui';
import { Subject } from 'rxjs';
import { filter, map, startWith } from 'rxjs/operators';
import { AuthService, ColorChangeService, IosAppService } from '../../services';
import { FooterComponent } from '../footer/footer.component';
import { HeaderComponent } from '../header/header.component';
import { StrossleAdvertComponent } from '../strossle-advert/strossle-advert.component';

declare let __tcfapi: (command: string, version?: number, callback?: (response: any, success: boolean) => void, param?: any) => void;

@Component({
  selector: 'app-base',
  templateUrl: './base.component.html',
  styleUrls: ['./base.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [HeaderComponent, AsyncPipe, RouterOutlet, FooterComponent, StrossleAdvertComponent, NgIf],
})
export class BaseComponent implements OnInit, OnDestroy, AfterViewInit {
  mainMenu: SimplifiedMenuItem[] = [];
  topMenu: SimplifiedMenuItem[] = [];
  footer0: SimplifiedMenuItem[] = [];
  footer1: SimplifiedMenuItem[] = [];
  footer: SimplifiedMenuItem[] = [];

  breakingNews?: BreakingBlock;
  isAdblockerActive: boolean;

  readonly #iosAppService: IosAppService = inject(IosAppService);
  isIos: Signal<boolean> = this.#iosAppService.isIosApp;

  isFullWidth$ = this.router.events.pipe(
    filter((e) => e instanceof NavigationEnd),
    startWith(null),
    map(() => this.route.snapshot),
    map((route) => {
      while (route.firstChild) {
        route = route.firstChild;
      }
      return route;
    }),
    map((snapshot: ActivatedRouteSnapshot) => {
      return snapshot.data?.['isFullWidth'] === true;
    })
  );

  private readonly unsubscribe$: Subject<boolean> = new Subject();
  private readonly document: Document = inject(DOCUMENT);
  private readonly colorChangeService = inject(ColorChangeService);
  readonly isCsupasport = computed(() => this.colorChangeService.isCsupasport());
  readonly headerBanner = computed(() =>
    this.colorChangeService.isCsupasport() ? 'csupasport' : this.colorChangeService.isHatsofuves() ? 'hatsofuves' : undefined
  );

  constructor(
    private readonly route: ActivatedRoute,
    private readonly router: Router,
    private readonly utils: UtilService,
    private readonly changeRef: ChangeDetectorRef,
    private readonly authService: AuthService,
    private readonly utilsService: UtilService
  ) {}

  get user(): User | undefined {
    return this.authService.currentUser;
  }

  ngOnInit(): void {
    // Check user at first page load (async, not to block UI) to display header differently if user is logged in
    this.authService.isAuthenticated().subscribe(() => {
      this.changeRef.markForCheck();
    });
    // any is necessary due to missing overlap of `NavigationEnd` and result of `router.events`

    const responseData: InitResolverData = this.route.snapshot.data?.['data'] ?? {};
    this.breakingNews = responseData?.init?.breakingNews;

    const {
      menu: { header_0, header_1, footer_0, footer_1, footer },
    } = responseData || {};
    this.mainMenu = header_0 ?? [];
    this.topMenu = header_1 ?? [];
    this.footer0 = footer_0 ?? [];
    this.footer1 = footer_1 ?? [];
    this.footer = footer ?? [];

    this.changeRef.detectChanges();
  }

  public ngAfterViewInit(): void {
    this.adblockerActiveStatus();
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next(true);
    this.unsubscribe$.complete();
  }

  private adblockerActiveStatus(): boolean | void {
    if (!this.utils.isBrowser()) {
      //Manually override to return false, because the ado does not exist on SSR.
      return;
    }
    return (this.isAdblockerActive = typeof (<any>window).ado !== 'object');
  }
}
