import { ChangeDetectionStrategy, Component, Input, OnInit } from '@angular/core';
import { BasicDossier, DossierArticle, DossierData } from '@trendency/kesma-ui';
import { map, Observable, share } from 'rxjs';
import { ApiService } from '../../services';
import { NewsFeedCardComponent } from '../news-feed-card/news-feed-card.component';
import { AsyncPipe, NgIf } from '@angular/common';

@Component({
  selector: 'app-article-dossier',
  templateUrl: './article-dossier.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NewsFeedCardComponent, NgIf, AsyncPipe],
})
export class ArticleDossierComponent implements OnInit {
  @Input() dossier: BasicDossier;
  @Input() excludedArticleSlug: string;

  articles$?: Observable<Partial<DossierData>>;

  constructor(private readonly apiService: ApiService) {}

  ngOnInit(): void {
    const { slug } = this.dossier;
    const fromIndex = 0;
    const dossierCount = 4;

    this.articles$ = this.apiService.getDossier(slug, fromIndex, dossierCount).pipe(
      share(),
      map(({ data }) => this.excludeArticleBySlug(data, this.excludedArticleSlug)),
      map((articles) => this.mapDossierArticleToDossierData(articles))
    );
  }

  private excludeArticleBySlug(articles: DossierArticle[], slug: string): DossierArticle[] {
    return articles.filter((article) => article.slug !== slug);
  }

  private mapDossierArticleToDossierData(articles: DossierArticle[]): Partial<DossierData> {
    const { title, slug, coverImage } = this.dossier;

    return {
      title,
      slug,
      headerImage: coverImage?.thumbnailUrl,
      secondaryArticles: articles.map((article) => {
        const { slug, title, lead, publishDate } = article;

        return {
          slug,
          title,
          lead,
          publishDate: String(publishDate),
          columnSlug: article?.columnSlug,
        };
      }),
    };
  }
}
