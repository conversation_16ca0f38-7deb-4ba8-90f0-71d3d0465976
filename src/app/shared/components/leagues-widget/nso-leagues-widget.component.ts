import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { NgForOf, NgIf } from '@angular/common';

@Component({
  selector: 'nso-leagues-widget',
  templateUrl: './nso-leagues-widget.component.html',
  styleUrls: ['./nso-leagues-widget.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgForOf, NgIf],
})
export class NsoLeaguesWidgetComponent {
  @Input() data: any;
}
