import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';
import { NgIf } from '@angular/common';

@Component({
  selector: 'nso-password-eye-toggle',
  templateUrl: './nso-password-eye-toggle.component.html',
  styleUrls: ['./nso-password-eye-toggle.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf],
})
export class NsoPasswordEyeToggleComponent {
  @Input() isPasswordShown = false;
  @Output() visibilityChanged = new EventEmitter<boolean>();

  toggleVisibility(): void {
    this.isPasswordShown = !this.isPasswordShown;
    this.visibilityChanged.emit(this.isPasswordShown);
  }
}
