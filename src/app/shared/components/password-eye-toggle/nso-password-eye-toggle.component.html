<button (click)="toggleVisibility()" class="toggle-button">
  <svg *ngIf="!isPasswordShown" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15Z" fill="currentColor" />
    <path
      d="M18.5072 6.61781C16.4578 5.2125 14.2645 4.5 11.9887 4.5C9.94078 4.5 7.94438 5.10937 6.05484 6.30375C4.14937 7.51078 2.28141 9.70312 0.75 12C1.98844 14.0625 3.6825 16.1831 5.44687 17.3991C7.47094 18.7931 9.67172 19.5 11.9887 19.5C14.2856 19.5 16.4817 18.7936 18.5184 17.4005C20.3114 16.1719 22.0177 14.0541 23.25 12C22.0134 9.96422 20.3016 7.84875 18.5072 6.61781ZM12 16.5C11.11 16.5 10.24 16.2361 9.49993 15.7416C8.75991 15.2471 8.18314 14.5443 7.84254 13.7221C7.50195 12.8998 7.41283 11.995 7.58647 11.1221C7.7601 10.2492 8.18868 9.44736 8.81802 8.81802C9.44736 8.18868 10.2492 7.7601 11.1221 7.58647C11.995 7.41283 12.8998 7.50195 13.7221 7.84254C14.5443 8.18314 15.2471 8.75991 15.7416 9.49993C16.2361 10.24 16.5 11.11 16.5 12C16.4986 13.1931 16.0241 14.3369 15.1805 15.1805C14.3369 16.0241 13.1931 16.4986 12 16.5Z"
      fill="currentColor"
    />
  </svg>

  <svg *ngIf="isPasswordShown" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <!-- Generator: Adobe Illustrator 26.0.3, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
    <svg
      version="1.1"
      id="Layer_1"
      xmlns="http://www.w3.org/2000/svg"
      xmlns:xlink="http://www.w3.org/1999/xlink"
      x="0px"
      y="0px"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      style="enable-background: new 0 0 24 24"
      xml:space="preserve"
    >
      <path
        fill="currentColor"
        d="M12,17.3c-2.7,0-5-2.2-5.2-4.9L3,9.4c-0.5,0.6-1,1.3-1.3,2.1c-0.1,0.2-0.1,0.4-0.1,0.5s0,0.4,0.1,0.5
	c2,3.9,5.8,6.6,10.3,6.6c1,0,1.9-0.1,2.8-0.4l-1.9-1.5C12.6,17.3,12.3,17.3,12,17.3z M23.3,19.5l-4-3.2c1.2-1,2.2-2.3,2.9-3.8
	c0.1-0.2,0.1-0.4,0.1-0.5c0-0.2,0-0.4-0.1-0.5c-2-3.9-5.8-6.6-10.3-6.6c-1.9,0-3.7,0.5-5.3,1.4L2.1,2.6C2,2.6,1.9,2.6,1.9,2.5
	c-0.1,0-0.1,0-0.2,0c-0.1,0-0.1,0-0.2,0.1c-0.1,0-0.1,0.1-0.2,0.2L0.6,3.7C0.5,3.8,0.4,4,0.4,4.1c0,0.2,0.1,0.3,0.2,0.4l21.3,16.8
	c0.1,0,0.1,0.1,0.2,0.1c0.1,0,0.1,0,0.2,0c0.1,0,0.1,0,0.2-0.1s0.1-0.1,0.2-0.2l0.7-0.9c0.1-0.1,0.1-0.3,0.1-0.4
	C23.5,19.7,23.5,19.6,23.3,19.5z M16.7,14.2l-1.4-1.1c0.1-0.4,0.2-0.7,0.2-1.1c0-0.5-0.1-1.1-0.3-1.6c-0.2-0.5-0.6-0.9-1-1.3
	c-0.4-0.3-0.9-0.6-1.4-0.7c-0.5-0.1-1.1-0.1-1.6,0.1c0.2,0.3,0.3,0.7,0.3,1c0,0.1,0,0.2-0.1,0.4L8.7,7.9c0.9-0.8,2.1-1.2,3.3-1.2
	c0.7,0,1.4,0.1,2,0.4c0.6,0.3,1.2,0.7,1.7,1.2c0.5,0.5,0.9,1.1,1.1,1.7c0.3,0.6,0.4,1.3,0.4,2C17.2,12.8,17,13.5,16.7,14.2
	L16.7,14.2z"
      />
    </svg>
  </svg>
</button>
