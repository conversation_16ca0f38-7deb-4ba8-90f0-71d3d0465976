import { environment } from '../environments/environment';
import { DateFnsConfigurationService } from 'ngx-date-fns';
import { hu } from 'date-fns/locale';
import { register as registerSwiperElement } from 'swiper/element/bundle';
import { ApplicationConfig, importProvidersFrom, provideZoneChangeDetection } from '@angular/core';
import { AppEnvironment, provideEncodedTransferState } from '@trendency/kesma-core';
import { provideAnimations } from '@angular/platform-browser/animations';
import { provideRouter, withEnabledBlockingInitialNavigation, withInMemoryScrolling } from '@angular/router';
import { appRoutes } from './app.routing';
import { provideHttpClient, withInterceptors, withInterceptorsFromDi } from '@angular/common/http';
import { authInterceptor, elasticIndexWarningInterceptor, portalHeaderHttpInterceptor } from './shared';
import { GoogleTagManagerModule } from 'angular-google-tag-manager';
import { adDebugFactory, DEV_AD_DEBUG } from '@trendency/kesma-ui';

const GTAG_PROVIDER = [{ provide: 'googleTagManagerId', useValue: environment.googleTagManager }];
const huConfig = new DateFnsConfigurationService();
huConfig.setLocale(hu);

registerSwiperElement();

export const appConfig: ApplicationConfig = {
  providers: [
    provideEncodedTransferState(),
    provideAnimations(),
    provideZoneChangeDetection({ eventCoalescing: true }),
    provideRouter(
      appRoutes,
      withEnabledBlockingInitialNavigation(),
      withInMemoryScrolling({
        scrollPositionRestoration: 'top',
        anchorScrolling: 'enabled',
      })
    ),
    provideHttpClient(withInterceptorsFromDi(), withInterceptors([portalHeaderHttpInterceptor, elasticIndexWarningInterceptor, authInterceptor])),
    importProvidersFrom(GoogleTagManagerModule),
    {
      provide: AppEnvironment,
      useValue: environment,
    },
    {
      provide: DateFnsConfigurationService,
      useValue: huConfig,
    },
    {
      provide: DEV_AD_DEBUG,
      useFactory: adDebugFactory,
      deps: [AppEnvironment],
    },
    ...GTAG_PROVIDER,
  ],
};
