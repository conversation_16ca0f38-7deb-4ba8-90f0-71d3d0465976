@use 'shared' as *;

$olympics-blue: #022366;

.category-page {
  display: flex;
  flex-direction: column;
  margin-top: 40px;

  .mobile-wrapper {
    @include media-breakpoint-down(md) {
      max-width: calc(100% - 30px);
      margin: 0 auto;
    }
  }

  .with-aside {
    display: flex;
    justify-content: space-between;
    gap: var(--nso-gutter-width);

    @include media-breakpoint-down(md) {
      flex-direction: column;
    }
  }

  .wrapper {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  &.olympics {
    margin-top: 80px;

    ::ng-deep {
      .article-card {
        &-column,
        &-title {
          color: $olympics-blue;
        }
      }
      .style-FeaturedSidedBottomColumnTitleDate {
        .article-card {
          &-thumbnail {
            @include media-breakpoint-up(xl) {
              height: 200px !important;
            }
          }
        }
      }
    }
  }
}

.positionier {
  max-width: calc(100% - 30px);
  margin: 0 auto;
}

:host {
  padding: 40px 0;

  .title {
    font-size: 44px;
    margin-bottom: 30px;
    text-transform: uppercase;
    border-bottom: 1px solid var(--kui-black);
  }

  nso-pager {
    display: flex;
    justify-content: center;
    margin-top: 50px;
  }
}
