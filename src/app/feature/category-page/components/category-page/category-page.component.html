<section class="category-page" *ngIf="pageData$ | async as pageData" [class.olympics]="pageData.columnSlug === 'parizs2024'">
  <div class="wrapper">
    <!--
    <nso-sport-radio
      [playing]="(radioService.isPlaying$ | async) || false"
      (playingChange)="radioService.radioClick()"
      (volumeChange)="radioService.volumeChange($event)"
    ></nso-sport-radio>
    -->
    <ng-container *ngIf="pageData?.layoutData as layoutData">
      <app-layout [layoutType]="LayoutPageType.COLUMN" [structure]="layoutData.struct" [cachePrefix]="pageData.columnSlug" [configuration]="layoutData.content">
      </app-layout>
    </ng-container>
    @if (isCsupasport() || isHatsofuves()) {
      <app-csupasport-tags [isCsupasport]="isCsupasport()"></app-csupasport-tags>
    }

    <div class="with-aside">
      <div class="left-column mobile-wrapper">
        <app-category-article-list id="cimke_tartalom" *ngIf="articles$ | async as articles" [articles]="articles"> </app-category-article-list>

        <ng-container *ngIf="limitables$ | async as limitable">
          <nso-pager
            *ngIf="limitable?.pageMax! > 0"
            [rowAllCount]="limitable?.rowAllCount!"
            [rowOnPageCount]="limitable?.rowOnPageCount!"
            [hasFirstLastButton]="true"
            [showTotalPagesPositionAtRight]="true"
            [isCountPager]="true"
            [allowAutoScrollToTop]="false"
          >
          </nso-pager>
        </ng-container>

        <div class="rtl-container">
          <div class="rltdwidget" data-widget-id="6710261"></div>
        </div>
      </div>

      <aside *ngIf="pageData.columnSlug !== 'parizs2024'">
        <app-sidebar [hasAds]="!hasLayout" [categorySlug]="pageData.columnSlug"> </app-sidebar>
      </aside>
    </div>
  </div>
</section>
