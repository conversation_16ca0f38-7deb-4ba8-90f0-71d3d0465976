import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { RouterLink } from '@angular/router';
import { Tag } from '@trendency/kesma-ui';

@Component({
  selector: 'app-csupasport-tags',
  imports: [RouterLink],
  templateUrl: './csupasport-tags.component.html',
  styleUrl: './csupasport-tags.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CsupasportTagsComponent {
  isCsupasport = input<boolean>(true);
  csupaSportTags: Tag[] = [
    { slug: '/cimke/futas', title: '<PERSON><PERSON><PERSON>' },
    { slug: '/cimke/vizi-sportok', title: '<PERSON><PERSON><PERSON> sportok' },
    { slug: '/cimke/turazas', title: 'T<PERSON><PERSON><PERSON><PERSON><PERSON>' },
    { slug: '/cimke/kerekparozas', title: 'Kerékpá<PERSON>zás' },
    { slug: '/cimke/fitnesz', title: '<PERSON>t<PERSON><PERSON>' },
    { slug: '/cimke/extrem-sportok', title: 'Extrém sportok' },
    { slug: '/cimke/eletmod', title: '<PERSON>letmód' },
    { slug: '/cimke/programajanlo', title: 'Programajánló' },
  ];

  hatsoFuvesTags: Tag[] = [
    { slug: '/cimke/bemutatkozas', title: 'Bemutatkozás' },
    { slug: '/cimke/futballmento-tura', title: 'Futballmentő túra' },
    { slug: '/cimke/konyv', title: 'Könyv' },
    { slug: '/cimke/kiallitas', title: 'Kiállítás' },
    { slug: '/cimke/harmadik-felido-bisztro', title: 'Harmadik félidő bisztró' },
    { slug: '/cimke/szatmari-utazas', title: 'Szatmári utazás' },
    { slug: '/cimke/sajto', title: 'Sajtó' },
  ];
  get tags(): Tag[] {
    return this.isCsupasport() ? this.csupaSportTags : this.hatsoFuvesTags;
  }
}
