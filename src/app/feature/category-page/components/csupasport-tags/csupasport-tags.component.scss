@use 'shared' as *;

:host {
  display: flex;
  column-gap: 12px;
  row-gap: 8px;
  flex-wrap: wrap;

  @include media-breakpoint-down(sm) {
    column-gap: 8px;
  }
}

.csupasport,
.hatsofuves {
  padding: 11px 24px;
  border-radius: 100px;
  font-style: normal;
  font-size: 18px;
  font-weight: 700;
  line-height: 130%;
  text-transform: uppercase;
  font-family: var(--kui-font-saira-condensed);

  @include media-breakpoint-down(sm) {
    padding: 11px 20px;
  }
}

.csupasport {
  background-color: #ffe036;
  color: var(--kui-gray-500);
}

.hatsofuves {
  background-color: var(--kui-black);
  color: #cfc621;
}
