import { Injectable } from '@angular/core';
import { Params, Router } from '@angular/router';
import { ApiResponseMetaList, ApiResult, ArticleCard, LayoutService, LayoutWithExcludeIds, RedirectService } from '@trendency/kesma-ui';
import { Observable, throwError } from 'rxjs';
import { catchError, map, switchMap, tap } from 'rxjs/operators';
import { ArticleResponse, articlesPageSize, CategoryResolverResponse } from './category.definitions';
import { ApiService, mapCategoryResponse } from '../../../shared';

@Injectable({ providedIn: 'root' })
export class CategoryService {
  public constructor(
    private readonly apiService: ApiService,
    private readonly layoutService: LayoutService,
    private readonly router: Router,
    private readonly redirectService: RedirectService
  ) {}

  public getRequestForCategoryLayout(params: Params, queryParams: Params): Observable<CategoryResolverResponse> {
    const { categorySlug } = params;
    const { page } = queryParams;
    const pageIndex = page ? page - 1 : 0;

    return this.layoutService.getLayoutWithExcludeIds(categorySlug).pipe(
      tap((data: LayoutWithExcludeIds) => {
        if (!data.columnTitle) {
          this.router.navigate(['/', '404'], {
            state: { errorResponse: JSON.stringify(data) },
            skipLocationChange: true,
          });
        }
      }),
      switchMap((layoutResponse: LayoutWithExcludeIds) => {
        return this.apiService.getCategoryArticles(categorySlug, pageIndex, articlesPageSize, undefined, undefined, layoutResponse.excludedIds).pipe(
          catchError((error) => {
            this.router.navigate(['/', '404'], {
              state: { errorResponse: JSON.stringify(error) },
              skipLocationChange: true,
            });
            return throwError(error);
          }),
          map((res) => {
            if (this.redirectService.shouldBeRedirect(pageIndex, res?.data)) {
              this.redirectService.redirectOldUrl(`rovat/${categorySlug}`, false, 302);
            }
            return res as ArticleResponse;
          }),
          map((categoryResponse: ApiResult<ArticleCard[], ApiResponseMetaList>) => mapCategoryResponse(categoryResponse, categorySlug, '', '', layoutResponse))
        );
      })
    );
  }

  public getRequestForCategoryByDate(params: Params, queryParams: Params): Observable<CategoryResolverResponse> {
    const { categorySlug, year, month } = params;
    const { page } = queryParams;
    const pageIndex = page ? page - 1 : 0;

    if (isNaN(year) || isNaN(month)) {
      this.router
        .navigate(['/', '404'], {
          skipLocationChange: true,
        })
        .then();
    }

    const request$ = this.layoutService.getLayoutWithExcludeIds(categorySlug).pipe(
      switchMap((layoutResponse) =>
        this.apiService.getCategoryArticles(categorySlug, pageIndex, articlesPageSize, year, month, []).pipe(
          map((res) => {
            if (this.redirectService.shouldBeRedirect(pageIndex, res?.data)) {
              this.redirectService.redirectOldUrl(month ? `rovat/${categorySlug}/${year}/${month}` : `rovat/${categorySlug}/${month}`, false, 302);
            }
            return res as ArticleResponse;
          }),
          map((categoryResponse: ApiResult<ArticleCard[], ApiResponseMetaList>) =>
            mapCategoryResponse(categoryResponse, categorySlug, year, month, layoutResponse as LayoutWithExcludeIds, true)
          ),
          catchError((error) => {
            this.router
              .navigate(['/', '404'], {
                state: { errorResponse: JSON.stringify(error) },
                skipLocationChange: true,
              })
              .then();
            return throwError(() => error);
          })
        )
      )
    );

    return request$;
  }
}
