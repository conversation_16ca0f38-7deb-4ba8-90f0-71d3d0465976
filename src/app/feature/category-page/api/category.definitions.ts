import { ApiResponseMetaList, ArticleCard, AutoFill, Layout, LayoutMeta, SelectedArticle } from '@trendency/kesma-ui';

export type LayoutServiceResponse = {
  data: Layout;
  meta: LayoutMeta;
};

export const articlesPageSize = 16; // JUST AS TAGS + SEARCH SUBPAGES

export type CategoryRouteType = 'category-layout' | 'category-year' | 'category-month';

export type ArticleResponse = Readonly<{
  data: ArticleCard[];
  meta: ApiResponseMetaList;
}>;

export type CategoryResolverResponse = {
  layoutApiResponse: Layout | null;
  columnParentTitle?: string;
  columnParentSlug?: string;
  excludedIds: string[];
  columnTitle: string;
  category: ArticleResponse;
  slug: string;
  year: string;
  month: string;
};

/*export type Layout = {
  struct: LayoutStruct[];
  content: LayoutContent[];
};*/

export type LayoutContent = {
  autoFill: AutoFill;
  hasImage: boolean;
  layoutElementId: string;
  selectedArticles: SelectedArticle[];
  selectedOpinions: selectedOpinions[];
};

type selectedOpinions = {
  id: string;
  data: Data;
};

type Data = {
  id: string;
  slug: string;
  title: string;
  excerpt?: string;
  columnId: string;
  isActive?: string;
  sponsorId?: string[];
  columnSlug: string;
  priorityId?: string;
  columnTitle: string;
  publishDate: string;
  sponsorTitle?: string[];
  thumbnailUrl?: string;
  priorityTitle?: string;
  columnParentId?: any;
  reading_length?: string;
  columnParentSlug?: any;
  publicAuthorName?: string;
  columnParentTitle?: any;
  publicAuthorAvatarThumbnailUrl?: string;
  lead?: string;
};
