import { Routes } from '@angular/router';
import { CategoryPageComponent } from './components/category-page/category-page.component';
import { CategoryResolver } from './api/category.resolver';
import { PageValidatorGuard } from '@trendency/kesma-ui';

export const categoryRoutes: Routes = [
  {
    path: '',
    pathMatch: 'full',
    component: CategoryPageComponent,
    canActivate: [PageValidatorGuard],
    runGuardsAndResolvers: 'paramsOrQueryParamsChange',
    resolve: {
      pageData: CategoryResolver,
    },
    data: { categoryRouteType: 'category-layout' },
    providers: [CategoryResolver],
  },
  {
    path: ':year',
    pathMatch: 'full',
    component: CategoryPageComponent,
    canActivate: [PageValidatorGuard],
    resolve: {
      pageData: CategoryResolver,
    },
    data: { categoryRouteType: 'category-year' },
    providers: [CategoryResolver],
  },
  {
    path: ':year/:month',
    pathMatch: 'full',
    component: CategoryPageComponent,
    canActivate: [PageValidatorGuard],
    resolve: {
      pageData: CategoryResolver,
    },
    data: { categoryRouteType: 'category-month' },
    providers: [CategoryResolver],
  },
];
