import { Schedule } from '../../team-page/team-end-page.definitions';
import { ArticleCard, ArticleSearchResult, BackendArticle } from '@trendency/kesma-ui';
import { LimitableMeta } from '@trendency/kesma-ui/lib/definitions/api-result';
import { ChampionshipSchedule } from 'src/app/shared';

export type ChampionshipMainPage = {
  articlesByCategory: BackendArticle[];
  articlesByKeyword: ArticleSearchResult[];
  limitable: LimitableMeta;
};

export type ChampionshipTransfer = {
  articlesWithLimitable: ChampionshipTransferArticles;
  category: string;
};

export type ChampionshipTransferArticles = Readonly<{
  articles: ArticleCard[];
  limitable: LimitableMeta;
}>;

export type ChampionshipDetails = Readonly<{
  id: string;
  title: string;
  logo: string;
  position: number;
  publicTitle: string;
  tabellaStatus: boolean;
  column: Column;
  season: Season;
}>;

export type ChampionshipSchedules = Readonly<{
  schedules: ChampionshipSchedule[];
}>;

export type ChampionshipScheduleResponse = {
  scheduleDates: string[];
  categorySlug: string;
};

export type ChampionshipAppointments = Readonly<{
  date: Date;
  clubs: Clubs[];
}>;

export type Season = Readonly<{
  id: string;
  liveSport: LiveSport;
  seasonEnd: number;
  seasonStart: number;
  title: string;
}>;

export type LiveSport = Readonly<{
  id: string;
  title: string;
}>;

export type Column = Readonly<{
  id: string;
  slug: string;
  title: string;
}>;

export type Clubs = Readonly<{
  name1: string;
  image1: string;
  time: string;
  name2: string;
  image2: string;
  local: string;
  channel: string;
}>;

export type ChampionshipFilter = {
  season?: string;
  round?: string;
};

export type BackendSeason = Readonly<{
  seasons: Season[];
}>;

export type BackendSchedules = Readonly<{
  schedules: Schedule[];
}>;
