import { Injectable } from '@angular/core';
import { BehaviorSubject, map, Observable, Subject } from 'rxjs';
import { BackendSchedules, BackendSeason } from './championship.definitions';
import { buildPhpArrayParam, ReqService } from '@trendency/kesma-core';
import { ApiResponseMetaList, ApiResult, ArticleSearchResult } from '@trendency/kesma-ui';
import { backendArticlesSearchResultsToArticleSearchResArticles } from '../../../shared';

@Injectable({ providedIn: 'root' })
export class ChampionshipService {
  private readonly selectedChampionshipItem$: Subject<string> = new Subject();
  private readonly championshipId$: BehaviorSubject<string> = new BehaviorSubject<string>('');

  selectedChampionshipItemObs$: Observable<string> = this.selectedChampionshipItem$.asObservable();
  currentChampionshipSlug = '';

  constructor(private readonly reqService: ReqService) {}

  getSeasons(): Observable<ApiResult<BackendSeason>> {
    return this.reqService.get<ApiResult<BackendSeason>>(`/sport/season/all`);
  }

  getMatchesByCompetition(competitionSlug: string): Observable<ApiResult<BackendSchedules>> {
    return this.reqService.get<ApiResult<BackendSchedules>>(`/sport/schedule/by-competition/${competitionSlug}`);
  }

  setSelectedAppointment(appointment: string): void {
    this.selectedChampionshipItem$.next(appointment);
  }

  get championshipId(): string {
    return this.championshipId$.value;
  }

  setChampionshipId(id: string): void {
    this.championshipId$.next(id);
  }

  searchArticleByTagsAndColumn(
    tags: string[],
    column: string,
    page = 0,
    itemsPerPage = 16,
    orderByAsc = true
  ): Observable<ApiResult<ArticleSearchResult[], ApiResponseMetaList>> {
    return this.reqService
      .get(`/content-page/search`, {
        params: {
          ...buildPhpArrayParam(tags, 'tags'),
          rowCount_limit: itemsPerPage.toString(),
          page_limit: page.toString(),
          'date_order[0]': orderByAsc ? 'asc' : 'desc',
          column,
        },
      })
      .pipe(
        map(({ data, meta }: any /*ApiResult<BackendArticleSearchResult[], ApiResponseMetaList>*/) => ({
          data: data.map(backendArticlesSearchResultsToArticleSearchResArticles),
          meta,
        }))
      );
  }
}
