import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { Observable, of, switchMap, throwError } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import { ApiService, backendArticlesSearchResultsToArticleSearchResArticles } from '../../../shared';
import { AllColumns, RedirectService } from '@trendency/kesma-ui';
import { ChampionshipMainPage } from './championship.definitions';

@Injectable()
export class ChampionshipMainPageResolver {
  constructor(
    private readonly apiService: ApiService,
    private readonly router: Router,
    private readonly redirectService: RedirectService
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<ChampionshipMainPage> {
    const categorySlug = route.params['categorySlug'];
    const currentPage = route.queryParams['page'] ? parseInt(route.queryParams['page'], 10) - 1 : 0;
    const maxResultsPerPage = 20;

    const columns$ = this.apiService.getColumns();
    const categoryArticles$ = this.apiService.getCategoryArticles(categorySlug, currentPage, maxResultsPerPage).pipe(catchError((err) => of(err)));
    const keywordArticles$ = this.apiService.searchByKeyword(categorySlug, currentPage, maxResultsPerPage);

    return columns$.pipe(
      map((res) => res.data.some((column: AllColumns) => column.slug === categorySlug)),
      switchMap((isCategory) => {
        const neededArticles$ = isCategory ? categoryArticles$ : keywordArticles$;

        // return categoryArticles if Championship is a category too, otherwise return articles by slug
        return neededArticles$.pipe(
          map((articles) => ({
            articlesByCategory: isCategory ? articles?.data : null,
            articlesByKeyword: !isCategory ? articles?.data?.map(backendArticlesSearchResultsToArticleSearchResArticles) : null,
            limitable: articles?.meta?.limitable,
          })),
          tap(({ articlesByKeyword }) => {
            if (this.redirectService.shouldBeRedirect(currentPage, articlesByKeyword)) {
              this.redirectService.redirectOldUrl(`bajnoksag/${categorySlug}`, false, 302);
            }
          }),
          catchError((err) => {
            this.router
              .navigate(['/', '404'], {
                skipLocationChange: true,
              })
              .then(null);
            return throwError(err);
          })
        );
      })
    );
  }
}
