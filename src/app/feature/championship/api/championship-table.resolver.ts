import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { catchError, map, Observable, throwError } from 'rxjs';
import { ChampionshipService } from './championship.service';
import { ApiService, TeamsLeageTable } from '../../../shared';

@Injectable()
export class ChampionshipTableResolver {
  constructor(
    private readonly apiService: ApiService,
    private readonly championshipService: ChampionshipService,
    private readonly router: Router
  ) {}

  resolve(): Observable<TeamsLeageTable> {
    const championshipId = this.championshipService.championshipId;
    return this.apiService.getChampionshipTable(championshipId).pipe(
      map(({ data }) => data),
      catchError((error) => {
        this.router.navigate(['/', '404'], {
          state: { errorResponse: JSON.stringify(error) },
          skipLocationChange: true,
        });

        return throwError(() => error);
      })
    );
  }
}
