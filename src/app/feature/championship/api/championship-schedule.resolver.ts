import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { catchError, map, Observable, throwError } from 'rxjs';
import { ChampionshipScheduleResponse } from './championship.definitions';
import { format } from 'date-fns';
import { ApiService, ChampionshipSchedule } from '../../../shared';

@Injectable()
export class ChampionshipScheduleResolver {
  constructor(
    private readonly apiService: ApiService,
    private readonly router: Router
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<ChampionshipScheduleResponse> {
    const championshipName: string = route?.parent?.parent?.params['categorySlug'];
    const today = format(new Date(), 'yyyy.MM.dd');
    return this.apiService.getChampionshipFutureMatches(championshipName).pipe(
      map(({ data }) => data),
      map(({ schedules }) => schedules),
      map((events: ChampionshipSchedule[]) => {
        const dates = [...new Set(events.map((event) => format(new Date(event?.scheduleDate?.date), 'yyyy.MM.dd')))];

        if (!dates.includes(today)) {
          dates.unshift(today);
        }

        return {
          scheduleDates: dates,
          categorySlug: championshipName,
        };
      }),
      catchError((error) => {
        this.router.navigate(['/', '404'], {
          state: { errorResponse: JSON.stringify(error) },
          skipLocationChange: true,
        });

        return throwError(() => error);
      })
    );
  }
}
