import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot } from '@angular/router';
import { forkJoin, map, Observable } from 'rxjs';
import { ApiService, searchResultToArticleCard } from '../../../shared';
import { ChampionshipTransfer } from './championship.definitions';
import { AllColumns, RedirectService } from '@trendency/kesma-ui';
import { ChampionshipService } from './championship.service';
import { tap } from 'rxjs/operators';

@Injectable()
export class ChampionshipTransferResolver {
  readonly tag = 'atigazolas';

  constructor(
    private readonly championshipService: ChampionshipService,
    private readonly apiService: ApiService,
    private readonly redirectService: RedirectService
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<ChampionshipTransfer> {
    const currentPage = route.queryParams['page'] ? parseInt(route.queryParams['page'], 10) - 1 : 0;
    const slug: string = route?.parent?.parent?.params['categorySlug'];
    return forkJoin({
      articlesWithLimitable: this.championshipService.searchArticleByTagsAndColumn([this.tag], slug, currentPage).pipe(
        map((res) => {
          return {
            articles: res.data.map((data) => searchResultToArticleCard(data)),
            limitable: res.meta.limitable,
          };
        }),
        tap(({ articles }) => {
          if (this.redirectService.shouldBeRedirect(currentPage, articles)) {
            this.redirectService.redirectOldUrl(`bajnoksag/${slug}/atigazolasok`, false, 302);
          }
        })
      ),
      category: this.apiService.getColumns().pipe(
        map((res) => {
          const slug = route?.parent?.params['categorySlug'];
          return res.data.some((column: AllColumns) => column.slug === slug) ? slug : '';
        })
      ),
    });
  }
}
