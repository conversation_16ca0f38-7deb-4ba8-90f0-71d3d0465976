import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { catchError, map, Observable, tap, throwError } from 'rxjs';
import { ChampionshipDetails } from './championship.definitions';
import { ChampionshipService } from './championship.service';
import { ApiService } from '../../../shared';

@Injectable()
export class ChampionshipResolver {
  constructor(
    private readonly apiService: ApiService,
    private readonly championshipService: ChampionshipService,
    private readonly router: Router
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<ChampionshipDetails> {
    const championshipName: string = route.params['categorySlug'];
    return this.apiService.getChampionship(championshipName).pipe(
      map(({ data }) => data),
      tap(({ id }) => this.championshipService.setChampionshipId(id)),
      catchError((error) => {
        this.router.navigate(['/', '404'], {
          state: { errorResponse: JSON.stringify(error) },
          skipLocationChange: true,
        });

        return throwError(() => error);
      })
    );
  }
}
