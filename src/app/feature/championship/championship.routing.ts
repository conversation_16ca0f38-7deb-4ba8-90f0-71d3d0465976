import { Routes } from '@angular/router';
import { ChampionshipComponent } from './components/championship/championship.component';
import { ChampionshipResolver } from './api/championship.resolver';
import { ChampionshipCalendarComponent } from './components/championship-calendar/championship-calendar.component';
import { ChampionshipTransferComponent } from './components/championship-transfer/championship-transfer.component';
import { ChampionshipResultsComponent } from './components/championship-results/championship-results.component';
import { ChampionshipTransferResolver } from './api/championship-transfer.resolver';
import { ChampionshipScheduleResolver } from './api/championship-schedule.resolver';
import { ChampionshipMainPageComponent } from './components/championship-main-page/championship-main-page.component';
import { ChampionshipMainPageResolver } from './api/championship-main-page.resolver';
import { PageValidatorGuard } from '@trendency/kesma-ui';

export const championshipRoutes: Routes = [
  {
    path: '',
    component: ChampionshipComponent,
    resolve: {
      data: ChampionshipResolver,
    },
    providers: [ChampionshipResolver],
    children: [
      {
        path: '',
        component: ChampionshipMainPageComponent,
        resolve: { articles: ChampionshipMainPageResolver },
        canActivate: [PageValidatorGuard],
        providers: [ChampionshipMainPageResolver],
      },
      {
        path: 'eredmenyek',
        component: ChampionshipResultsComponent,
      },
      /* Supporton jelezték, hogy le kell venni.
      {
        path: 'tabella',
        component: ChampionshipTableComponent,
        resolve: {
          data: ChampionshipTableResolver,
        },
      },
        */
      {
        path: 'atigazolasok',
        component: ChampionshipTransferComponent,
        runGuardsAndResolvers: 'paramsOrQueryParamsChange',
        resolve: { data: ChampionshipTransferResolver },
        canActivate: [PageValidatorGuard],
        providers: [ChampionshipTransferResolver],
      },
      {
        path: 'menetrend',
        component: ChampionshipCalendarComponent,
        resolve: { data: ChampionshipScheduleResolver },
        providers: [ChampionshipScheduleResolver],
      },
    ],
  },
];
