import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import { map } from 'rxjs';
import { defaultMetaInfo } from 'src/app/shared/constants/meta.consts';
import { ArticleCardType, createNSOTitle, NsoArticleCardComponent, NsoPagerComponent } from '../../../../shared';
import { ChampionshipTransfer } from '../../api/championship.definitions';
import { ChampionshipService } from '../../api/championship.service';
import { LimitableMeta } from '@trendency/kesma-ui/lib/definitions/api-result';
import { AsyncPipe, NgForOf, NgIf } from '@angular/common';
import { SidebarComponent } from '../../../layout/components/sidebar/sidebar.component';
import { createCanonicalUrlForPageablePage } from '@trendency/kesma-ui';

@Component({
  selector: 'app-championship-transfer',
  templateUrl: './championship-transfer.component.html',
  styleUrls: ['./championship-transfer.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, AsyncPipe, NgForOf, NsoArticleCardComponent, NsoPagerComponent, SidebarComponent],
})
export class ChampionshipTransferComponent implements OnInit {
  readonly ArticleCardType = ArticleCardType;

  transfer$ = this.route.data.pipe(map((res) => res['data'] as ChampionshipTransfer));
  limitables$ = this.route.data.pipe(map((res) => res['data']['articlesWithLimitable']['limitable'] as LimitableMeta));

  constructor(
    private readonly route: ActivatedRoute,
    private readonly seo: SeoService,
    private readonly championshipService: ChampionshipService
  ) {}

  ngOnInit(): void {
    this.setMetaData();
  }

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage(`bajnoksag/${this.championshipService.currentChampionshipSlug}/atigazolasok`);
    if (canonical) this.seo.updateCanonicalUrl(canonical);
    const title = createNSOTitle(`${this.championshipService.currentChampionshipSlug.toUpperCase()} - Átigazolások`);
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    };
    this.seo.setMetaData(metaData);
  }
}
