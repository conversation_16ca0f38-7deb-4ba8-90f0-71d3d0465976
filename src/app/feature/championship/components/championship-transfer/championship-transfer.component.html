<section>
  <div class="wrapper with-aside" *ngIf="transfer$ | async as transfer">
    <div class="left-column">
      <nso-article-card
        nso-article-card
        *ngFor="let article of transfer.articlesWithLimitable.articles"
        class="article-card"
        [data]="article"
        [styleID]="ArticleCardType.FeaturedSidedImgColumnTitleLead"
      ></nso-article-card>

      <ng-container *ngIf="limitables$ | async as limitable">
        <nso-pager
          *ngIf="limitable?.pageMax! > 0"
          [rowAllCount]="limitable?.rowAllCount!"
          [rowOnPageCount]="limitable?.rowOnPageCount!"
          [hasFirstLastButton]="true"
          [showTotalPagesPositionAtRight]="true"
          [isCountPager]="true"
        >
        </nso-pager>
      </ng-container>
    </div>
    <aside>
      <app-sidebar [categorySlug]="transfer?.category"></app-sidebar>
    </aside>
  </div>
</section>
