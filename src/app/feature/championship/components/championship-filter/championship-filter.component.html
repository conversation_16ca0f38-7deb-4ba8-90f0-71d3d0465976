<ng-container *ngIf="selectedElement$ | async"></ng-container>

<div class="championship-filter">
  <span class="label">{{ label }}</span>
  <div class="select" (click)="isOpen = !isOpen">
    <ng-container>
      <span class="selected-item" [class.open]="isOpen">
        <ng-container *ngIf="championshipPage === 'calendar' && isToday; else other"> MA ({{ selectedItem }}) </ng-container>
        <ng-template #other>
          {{ selectedItem }}
        </ng-template>
      </span>
    </ng-container>
    <i class="icon arrow" [class.open]="isOpen"></i>
    <div class="dropdown" *ngIf="isOpen">
      <ng-container *ngFor="let item of source">
        <div class="item" *ngIf="item !== selectedItem" (click)="selectItem(item)">
          {{ item }}
        </div>
      </ng-container>
    </div>
  </div>
</div>
