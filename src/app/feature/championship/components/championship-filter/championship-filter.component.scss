@use 'shared' as *;

:host {
  .championship-filter {
    display: flex;
    flex-direction: column;
    gap: 8px;
    background: white;

    .label {
      font-size: 12px;
      font-weight: 700;
    }

    .select {
      display: flex;
      align-items: center;
      gap: 8px;
      cursor: pointer;
      position: relative;
      color: var(--kui-red-400);
      font-weight: bold;

      .selected-item.open {
        opacity: 0.5;
      }

      .arrow {
        background-image: url('/assets/images/icons/icon-nso-arrow-down-red.svg');
        width: 24px;
        height: 24px;

        &.open {
          transform: rotate(180deg);
        }
      }
    }

    .dropdown {
      position: absolute;
      top: 24px;
      padding-top: 8px;
      background: white;
      width: 100%;
      display: flex;
      flex-direction: column;
      gap: 16px;
      z-index: 1;

      .item:hover {
        transform: scale(1.05);
        transition: all 0.5;
      }
    }
  }
}
