import { ChangeDetectionStrategy, Component, ElementRef, EventEmitter, HostListener, Input, OnInit, Output } from '@angular/core';
import { isToday } from 'date-fns';
import { Observable, tap } from 'rxjs';
import { ChampionshipService } from '../../api/championship.service';
import { AsyncPipe, NgForOf, NgIf } from '@angular/common';

@Component({
  selector: 'app-championship-filter',
  templateUrl: './championship-filter.component.html',
  styleUrls: ['./championship-filter.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, AsyncPipe, NgForOf],
})
export class ChampionshipFilterComponent implements OnInit {
  @Input() label: string;
  @Input() source: string[] | undefined;
  @Input() championshipPage?: string;

  selectedItem: string;
  isOpen = false;
  isToday?: boolean;

  selectedElement$: Observable<string> = this.championshipService.selectedChampionshipItemObs$.pipe(tap((appointment) => (this.selectedItem = appointment)));

  @HostListener('document:click', ['$event'])
  clickout(event: PointerEvent): void {
    if (!this.eRef.nativeElement.contains(event.target)) {
      this.isOpen = false;
    }
  }

  @Output() filterChanged = new EventEmitter();

  constructor(
    private readonly eRef: ElementRef,
    private readonly championshipService: ChampionshipService
  ) {}

  ngOnInit(): void {
    if (!this.selectedItem) {
      this.selectItem(this.source ? this.source[0] : '');
    }
  }

  selectItem(item: string): void {
    this.isToday = isToday(new Date(item));
    this.selectedItem = item;
    this.filterChanged.emit(item);
  }
}
