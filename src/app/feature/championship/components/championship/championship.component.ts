import { ChangeDetectionStrategy, Component, OnDestroy, OnInit } from '@angular/core';
import { ChampionshipDetails } from '../../api/championship.definitions';
import { ActivatedRoute, RouterOutlet } from '@angular/router';
import { map, Observable, Subject } from 'rxjs';
import { RadioService } from 'src/app/shared/services/radio.service';
import { ChampionshipService } from '../../api/championship.service';
import { takeUntil } from 'rxjs/operators';
import { SubPageTabComponent, TabType } from '../../../../shared';
import { AsyncPipe } from '@angular/common';

@Component({
  selector: 'app-championship',
  templateUrl: './championship.component.html',
  styleUrls: ['./championship.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [SubPageTabComponent, AsyncPipe, RouterOutlet],
})
export class ChampionshipComponent implements OnInit, OnDestroy {
  private readonly destroy$: Subject<boolean> = new Subject();

  championshipTabs: TabType[] = [
    {
      tabName: 'Főoldal',
      tabSlug: this.mainPageRoute,
    },
    {
      tabName: 'Mérkőzés / Eredmények',
      tabSlug: 'eredmenyek',
    },
    {
      tabName: 'Menetrend',
      tabSlug: 'menetrend',
    },
    /* Supporton jelezték, hogy le kell venni.
    {
      tabName: 'Tabella',
      tabSlug: 'tabella',
    },
     */
    {
      tabName: 'Átigazolások',
      tabSlug: 'atigazolasok',
    },
  ];

  championshipData$?: Observable<ChampionshipDetails> = this.activatedRoute.data.pipe(map(({ data }) => data));
  pageType = 'championship';

  constructor(
    private readonly activatedRoute: ActivatedRoute,
    private readonly championshipService: ChampionshipService,
    public readonly radioService: RadioService
  ) {}

  ngOnInit(): void {
    this.activatedRoute.params.pipe(takeUntil(this.destroy$)).subscribe((params) => {
      this.championshipService.currentChampionshipSlug = params['categorySlug'];
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next(true);
    this.destroy$.complete();
  }

  get mainPageRoute(): string {
    return `/bajnoksag/${this.activatedRoute.snapshot.params['categorySlug']}`;
  }
}
