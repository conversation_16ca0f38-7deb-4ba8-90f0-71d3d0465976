<section class="championship">
  <div class="wrapper">
    <app-sub-page-tab
      [pageTabs]="championshipTabs"
      [isChampionshipPage]="true"
      [championshipType]="championshipData$ | async"
      [pageType]="pageType"
    ></app-sub-page-tab>

    <!--
    <nso-sport-radio
      [playing]="(radioService.isPlaying$ | async) || false"
      (playingChange)="radioService.radioClick()"
      (volumeChange)="radioService.volumeChange($event)"
    ></nso-sport-radio>
    -->
  </div>

  <router-outlet></router-outlet>
</section>
