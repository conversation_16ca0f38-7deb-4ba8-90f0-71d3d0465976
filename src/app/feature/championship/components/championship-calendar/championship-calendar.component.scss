@use 'shared' as *;

:host {
  display: block;
  margin-bottom: 40px;
}

.championship-calendar {
  &-header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40px;

    @include media-breakpoint-down(sm) {
      flex-direction: column;
      align-items: flex-start;
    }

    &-title {
      font-family: var(--kui-font-condensed);
      font-size: 38px;
      font-style: normal;
      font-weight: 700;
      line-height: 110%;
      letter-spacing: -0.76px;
      color: var(--kui-red-400);

      @include media-breakpoint-down(sm) {
        font-size: 32px;
        margin-bottom: 20px;
      }
    }
  }

  &-no-events {
    display: flex;
    flex-direction: row;
    font-weight: 700;
    width: 100%;
  }

  &-footer {
    display: flex;
    justify-content: space-between;
    margin-top: 40px;
  }
}

.sidebar-aside {
  @include media-breakpoint-down(sm) {
    display: none !important;
  }
}

nso-simple-button {
  margin: 0;
  font-weight: 400;
}

.simple-button-text {
  font-weight: 400;
  font-family: var(--kui-font-primary);
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 160%;

  display: flex;
  flex-direction: row;
  align-items: center;

  @include media-breakpoint-down(xs) {
    font-size: 12px;
  }

  img {
    width: 24px;
    height: 24px;
  }
}
