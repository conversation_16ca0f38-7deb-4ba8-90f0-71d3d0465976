import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { map, Observable, tap } from 'rxjs';
import { ChampionshipScheduleResponse } from '../../api/championship.definitions';
import { format, isToday } from 'date-fns';
import { ChampionshipService } from '../../api/championship.service';
import { backendDateToDate, buildMatchUrl, createCanonicalUrlForPageablePage } from '@trendency/kesma-ui';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import { defaultMetaInfo } from 'src/app/shared/constants/meta.consts';
import { ApiService, ChampionshipSchedule, createNSOTitle, NsoSimpleButtonComponent, NsoTimeTableComponent } from '../../../../shared';
import { ChampionshipFilterComponent } from '../championship-filter/championship-filter.component';
import { AsyncPipe, NgIf } from '@angular/common';
import { SidebarComponent } from '../../../layout/components/sidebar/sidebar.component';

@Component({
  selector: 'app-championship-calendar',
  templateUrl: './championship-calendar.component.html',
  styleUrls: ['./championship-calendar.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [ChampionshipFilterComponent, AsyncPipe, NgIf, NsoTimeTableComponent, NsoSimpleButtonComponent, SidebarComponent],
})
export class ChampionshipCalendarComponent implements OnInit {
  championshipAppointments: string[] = [];
  selectedAppointment: string;
  selectedAppointmentIndex = 0;
  categorySlug: string;

  championShipData$: Observable<ChampionshipScheduleResponse> = this.route.data.pipe(
    map(({ data }) => data),
    tap(({ scheduleDates, categorySlug }) => {
      this.championshipAppointments = scheduleDates;
      this.categorySlug = categorySlug;
    })
  );
  actualTimeTable$?: Observable<ChampionshipSchedule[] | undefined>;

  constructor(
    private readonly route: ActivatedRoute,
    private readonly apiService: ApiService,
    private readonly championshipService: ChampionshipService,
    private readonly seo: SeoService
  ) {}

  ngOnInit(): void {
    this.setMetaData();
  }

  moveToNextOrPreviousDay(direction: string): void {
    switch (direction) {
      case 'previous':
        if (this.selectedAppointmentIndex > 0) {
          this.selectedAppointmentIndex--;
        }
        break;
      case 'next':
        if (this.selectedAppointmentIndex < this.championshipAppointments.length - 1) {
          this.selectedAppointmentIndex++;
        }
        break;
    }
    this.championshipService.setSelectedAppointment(this.championshipAppointments?.[this.selectedAppointmentIndex]);
    this.getActualAppointment(this.selectedAppointmentIndex);
  }

  changeAppointment(appointmentDate: string): void {
    const index = this.championshipAppointments.findIndex((appointment) => appointment === appointmentDate);
    this.getActualAppointment(index);
    this.selectedAppointmentIndex = index;
  }

  getActualAppointment(index: number): void {
    this.selectedAppointment = this.championshipAppointments?.[index];
    this.selectedAppointment = format(new Date(this.selectedAppointment), 'yyyy-MM-dd');
    this.actualTimeTable$ = this.apiService.getChampionshipMatchesByDate(this.categorySlug, this.selectedAppointment).pipe(
      map(({ data }) => data),
      map(({ schedules }) => schedules),
      map((matches: ChampionshipSchedule[]) => {
        // filter out those dates, which are still to come, if the match day is today
        const today = isToday(new Date(this.selectedAppointment));
        if (today) {
          matches = matches?.filter((match) => new Date(match?.scheduleDate?.date) > new Date());
        }
        return matches.map((match: ChampionshipSchedule) => ({
          ...match,
          scheduleDate: {
            ...match.scheduleDate,
            //This is actually a string, however for some reason in the type, it is a Date object.
            date: <Date>backendDateToDate(match.scheduleDate.date as unknown as string),
          },
          link: buildMatchUrl(match),
        }));
      })
    );
  }

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage(`bajnoksag/${this.championshipService.currentChampionshipSlug}/menetrend`);
    if (canonical) this.seo.updateCanonicalUrl(canonical);
    const title = createNSOTitle(`${this.championshipService.currentChampionshipSlug.toUpperCase()} - Menetrend`);
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    };
    this.seo.setMetaData(metaData);
  }
}
