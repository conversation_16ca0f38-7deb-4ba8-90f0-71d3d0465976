<section>
  <div class="wrapper with-aside">
    <div class="left-column">
      <!-- Header -->
      <div class="championship-calendar-header">
        <span class="championship-calendar-header-title">Menetrend</span>
        <!-- Dropdown -->
        <app-championship-filter
          label="Játéknap"
          championshipPage="calendar"
          (filterChanged)="changeAppointment($event)"
          [source]="(championShipData$ | async)?.scheduleDates"
        ></app-championship-filter>
      </div>
      <!-- Table -->
      <ng-container *ngIf="actualTimeTable$ | async as actualTimeTable">
        <ng-container *ngIf="actualTimeTable?.length; else noAppointment">
          <nso-time-table [data]="actualTimeTable"></nso-time-table>
        </ng-container>
      </ng-container>
      <ng-template #noAppointment>
        <div class="championship-calendar-no-events">
          <span>A mai napra nincs esemény</span>
        </div>
      </ng-template>
      <div class="championship-calendar-footer">
        <nso-simple-button (click)="moveToNextOrPreviousDay('previous')" [disabled]="this.selectedAppointmentIndex === 0"
          ><span class="simple-button-text"
            ><img src="/assets/images/icons/icon-nso-arrow-left.svg" alt="icon-arrow-left" /> Előző játéknap</span
          ></nso-simple-button
        >
        <nso-simple-button
          (click)="moveToNextOrPreviousDay('next')"
          [disabled]="this.selectedAppointmentIndex === this.championshipAppointments.length - 1 || !this.championshipAppointments.length"
          ><span class="simple-button-text">Következő játéknap <img src="/assets/images/icons/icon-nso-arrow-right.svg" alt="icon-arrow-right" /></span>
        </nso-simple-button>
      </div>
    </div>
    <aside class="sidebar-aside">
      <app-sidebar [categorySlug]="categorySlug"></app-sidebar>
    </aside>
  </div>
</section>
