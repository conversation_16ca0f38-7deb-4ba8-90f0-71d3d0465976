import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import {
  ArticleCard,
  createCanonicalUrlForPageablePage,
  LayoutApiData,
  LayoutPageType,
  LimitableMeta,
  mapBackendArticleDataToArticleCard,
} from '@trendency/kesma-ui';
import { map, Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { CategoryService } from '../../../category-page/api/category.service';
import { categoriesMetaInfo, CategoryArticleListComponent, createNSOTitle, defaultMetaInfo, NsoPagerComponent } from '../../../../shared';
import { AsyncPipe, NgIf } from '@angular/common';
import { LayoutComponent } from '../../../layout/components/layout/layout.component';
import { SidebarComponent } from '../../../layout/components/sidebar/sidebar.component';

@Component({
  selector: 'app-championship-main-page',
  templateUrl: './championship-main-page.component.html',
  styleUrls: ['./championship-main-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, AsyncPipe, LayoutComponent, CategoryArticleListComponent, NsoPagerComponent, SidebarComponent],
})
export class ChampionshipMainPageComponent implements OnInit {
  articles$: Observable<ArticleCard[]>;
  limitables$: Observable<LimitableMeta>;
  pageData$: Observable<{
    columnTitle: string;
    columnSlug: string;
    layoutData: LayoutApiData;
  }>;
  hasChampionshipCategory: boolean;

  readonly LayoutPageType = LayoutPageType;

  constructor(
    private readonly route: ActivatedRoute,
    private readonly categoryService: CategoryService,
    private readonly seo: SeoService
  ) {}

  ngOnInit(): void {
    const categorySlug = this.route.snapshot.data['data'].slug;
    this.hasChampionshipCategory = !!this.route.snapshot.data['articles']['articlesByCategory']?.length;

    if (this.hasChampionshipCategory) {
      this.pageData$ = this.categoryService.getRequestForCategoryLayout({ categorySlug }, {}).pipe(
        map((res) => {
          const columnSlug: string = res['slug'];

          return {
            columnTitle: res['columnTitle'],
            layoutData: res['layoutApiResponse'],
            columnSlug,
          };
        }),
        tap((res) => this.setMetaData(res.columnTitle, res.columnSlug))
      ) as Observable<{
        columnTitle: string;
        columnSlug: string;
        layoutData: LayoutApiData;
      }>;
    } else {
      this.articles$ = this.route.data.pipe(
        tap((res) => this.setMetaData(res['data'].title, res['data'].slug)),
        map((res) =>
          res['articles']['articlesByKeyword'].map(mapBackendArticleDataToArticleCard).map((data: ArticleCard) => ({
            ...data,
            thumbnail: {
              alt: data.thumbnail?.alt,
            },
          }))
        )
      );
      this.limitables$ = this.route.data.pipe(map((res) => res['articles']['limitable']));
    }
  }

  private setMetaData(title: string, slug: string): void {
    const canonical = createCanonicalUrlForPageablePage('bajnoksag', this.route.snapshot);
    canonical && this.seo.updateCanonicalUrl(canonical);

    // If category has a predefined metadata to be used.
    if (slug in categoriesMetaInfo) {
      const overrideMeta = categoriesMetaInfo[slug];
      const title = createNSOTitle(overrideMeta.title);
      const meta: IMetaData = {
        ...defaultMetaInfo,
        title: title,
        ogTitle: title,
        description: overrideMeta.description,
        ogDescription: overrideMeta.description,
        keywords: overrideMeta.keywords,
      };
      this.seo.setMetaData(meta);
    } else {
      //If not, use the default ones.
      title = createNSOTitle(title.toUpperCase());
      const metaData: IMetaData = {
        ...defaultMetaInfo,
        title: title,
        ogTitle: title,
      };
      this.seo.setMetaData(metaData);
    }
  }
}
