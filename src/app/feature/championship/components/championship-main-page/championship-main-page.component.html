<section class="championship-main-page">
  <div class="wrapper with-aside">
    <ng-container *ngIf="hasChampionshipCategory">
      <ng-container *ngIf="pageData$ | async as pageData">
        <ng-container *ngIf="pageData?.layoutData as layoutData">
          <app-layout [layoutType]="LayoutPageType.COLUMN" [structure]="layoutData.struct" [configuration]="layoutData.content"> </app-layout>
        </ng-container>

        <span class="divider"></span>
        <!--
        TODO: cikk aj<PERSON>?
      -->
        <span class="divider"></span>
      </ng-container>
    </ng-container>

    <ng-container *ngIf="!hasChampionshipCategory">
      <div class="left-column">
        <app-category-article-list *ngIf="articles$ | async as articles" [articles]="articles"> </app-category-article-list>

        <ng-container *ngIf="limitables$ | async as limitable">
          <nso-pager
            *ngIf="limitable?.pageMax! > 0"
            [rowAllCount]="limitable?.rowAllCount!"
            [rowOnPageCount]="limitable?.rowOnPageCount!"
            [hasFirstLastButton]="true"
            [showTotalPagesPositionAtRight]="true"
            [isCountPager]="true"
          >
          </nso-pager>
        </ng-container>
      </div>

      <aside>
        <app-sidebar> </app-sidebar>
      </aside>
    </ng-container>
  </div>
</section>
