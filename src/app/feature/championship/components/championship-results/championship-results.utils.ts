import { ApiResult, backendDateToDate, buildMatchUrl } from '@trendency/kesma-ui';
import { BackendSchedules, BackendSeason } from '../../api/championship.definitions';
import { ResultsPageMatchTable } from '../../../../shared';

export const NON_LIVING_STATUSES = ['ended', 'ready', 'not_started', 'postponed'];

export function mapToMatches(matches: ApiResult<BackendSchedules>): ResultsPageMatchTable[] {
  const result: ResultsPageMatchTable[] = [];
  matches?.data?.schedules?.map((match, index) => {
    const currentSchedule = result?.find((schedule) => new Date(match?.scheduleDate?.date).toLocaleDateString() === schedule?.matchDate?.toLocaleDateString());

    if (!currentSchedule) {
      result[index] = {
        matchDate: new Date(match?.scheduleDate?.date),
        matches: [],
      };
    }

    (currentSchedule || result[index])?.matches.push({
      teamA: {
        name: match?.homeTeam?.title,
        teamLogo: match?.homeTeam?.team?.logo || '/assets/images/placeholder.png',
        liveScore: Number(match?.homeScore),
        score: Number(match?.homeScore),
      },
      teamB: {
        name: match?.awayTeam?.title,
        teamLogo: match?.awayTeam?.team?.logo || '/assets/images/placeholder.png',
        liveScore: Number(match?.awayScore),
        score: Number(match?.awayScore),
      },
      isLive: !NON_LIVING_STATUSES.includes(match?.scheduleStatus as string),
      channel: match?.tvStation?.title,
      startDate: backendDateToDate(match?.scheduleDate?.date) as Date,
      hasEnded: match?.scheduleStatus === 'ended',
      info: match?.information,
      link: buildMatchUrl(match),
    });
  });
  return result.filter((match) => match !== null);
}

export function mapToSeasons(seasons: ApiResult<BackendSeason>): string[] {
  return seasons?.data?.seasons?.map((season) => `${season.title}`);
}
