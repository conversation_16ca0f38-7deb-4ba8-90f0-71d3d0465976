<section>
  <div class="wrapper">
    <div class="championship-filter">
      <!--
      <app-championship-filter
        label="Forduló"
        [source]="rounds"
        (filterChanged)="handleFilter('round', $event)">
      </app-championship-filter>
      -->
      <!--
      <app-championship-filter
        *ngIf="seasons"
        label="Szezon"
        [source]="seasons"
        (filterChanged)="handleFilter('season', $event)">
      </app-championship-filter>
      -->
    </div>
    <div class="championship-results">
      <nso-results-page-match-table *ngFor="let match of matches" [data]="match"></nso-results-page-match-table>
    </div>
  </div>
</section>
