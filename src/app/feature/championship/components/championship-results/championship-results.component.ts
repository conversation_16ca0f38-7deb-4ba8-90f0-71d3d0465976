import { Component, ChangeDetectionStrategy, OnInit, ChangeDetectorRef } from '@angular/core';
import { ChampionshipFilter } from '../../api/championship.definitions';
import { ChampionshipService } from '../../api/championship.service';
import { forkJoin } from 'rxjs';
import { mapToMatches, mapToSeasons } from './championship-results.utils';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import { defaultMetaInfo } from 'src/app/shared/constants/meta.consts';
import { createNSOTitle, NsoResultsPageMatchTableComponent, ResultsPageMatchTable } from '../../../../shared';
import { NgForOf } from '@angular/common';
import { createCanonicalUrlForPageablePage } from '@trendency/kesma-ui';

@Component({
  selector: 'app-championship-results',
  templateUrl: './championship-results.component.html',
  styleUrls: ['./championship-results.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NsoResultsPageMatchTableComponent, NgForOf],
})
export class ChampionshipResultsComponent implements OnInit {
  seasons?: string[];
  matches?: ResultsPageMatchTable[];
  filter: ChampionshipFilter = {};

  constructor(
    private readonly championshipService: ChampionshipService,
    private readonly cdr: ChangeDetectorRef,
    private readonly seo: SeoService
  ) {}

  ngOnInit(): void {
    forkJoin({
      seasons: this.championshipService.getSeasons(),
      matches: this.championshipService.getMatchesByCompetition(this.championshipService.currentChampionshipSlug),
    }).subscribe(({ seasons, matches }) => {
      this.seasons = mapToSeasons(seasons);
      this.matches = mapToMatches(matches);
      this.cdr.markForCheck();
    });
    this.setMetaData();
  }

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage(`bajnoksag/${this.championshipService.currentChampionshipSlug}/eredmenyek`);
    if (canonical) this.seo.updateCanonicalUrl(canonical);
    const title = createNSOTitle(`${this.championshipService.currentChampionshipSlug.toUpperCase()} - Mérkőzések`);
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    };
    this.seo.setMetaData(metaData);
  }

  /*
  handleFilter(type: 'round' | 'season', value: string): void {
    this.filter[type] = value;
  }
  */
}
