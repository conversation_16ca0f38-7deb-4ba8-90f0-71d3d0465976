import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import { map, Observable } from 'rxjs';
import { ActivatedRoute } from '@angular/router';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import { defaultMetaInfo } from 'src/app/shared/constants/meta.consts';
import { createNSOTitle, NsoFullPageLeagueTableComponent, TeamsLeageTable } from '../../../../shared';
import { ChampionshipService } from '../../api/championship.service';
import { AsyncPipe } from '@angular/common';
import { createCanonicalUrlForPageablePage } from '@trendency/kesma-ui';

@Component({
  selector: 'app-championship-table',
  templateUrl: './championship-table.component.html',
  styleUrls: ['./championship-table.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NsoFullPageLeagueTableComponent, AsyncPipe],
})
export class ChampionshipTableComponent implements OnInit {
  leagueTable$: Observable<TeamsLeageTable>;
  leagueSlug$?: Observable<string> = this.route.parent?.params.pipe(map((params) => params['categorySlug']));

  constructor(
    private readonly route: ActivatedRoute,
    private readonly seo: SeoService,
    private readonly championshipService: ChampionshipService
  ) {}

  ngOnInit(): void {
    this.leagueTable$ = this.route.data.pipe(map(({ data }) => data));

    this.setMetaData();
  }

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage(`bajnoksag/${this.championshipService.currentChampionshipSlug}/tabella`);
    if (canonical) this.seo.updateCanonicalUrl(canonical);
    const title = createNSOTitle(`${this.championshipService.currentChampionshipSlug.toUpperCase()} - Tabella`);
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    };
    this.seo.setMetaData(metaData);
  }
}
