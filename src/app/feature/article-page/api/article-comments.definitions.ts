import { Article } from '@trendency/kesma-ui';

export type ArticleCommentsResolverData = {
  article: Article;
  meta?: {
    url?: string;
  };
};

export type CommentType = 'comment' | 'article';

export interface BackendArticleSocial {
  readonly commentCount: number;
  readonly dislikeCount: number;
  readonly likeCount: number;
  readonly disableLikesAndDislikes: boolean;
  readonly disableComments: boolean;
}
