import { HttpErrorResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import {
  ApiResponseMetaList,
  ApiResult,
  Article,
  ArticleResolverData,
  ArticleRouteParams,
  ArticleSearchResult,
  RecommendationsData,
  Tag,
} from '@trendency/kesma-ui';
import { catchError, forkJoin, map, Observable, of, share, switchMap, throwError } from 'rxjs';
import { TagsPageService } from '../../tags-page/tags-page.service';
import { ArticleService } from './article-page.service';
import { ApiService } from '../../../shared';

@Injectable()
export class ArticlePageResolver {
  constructor(
    private readonly router: Router,
    private readonly articleService: ArticleService,
    private readonly tagsService: TagsPageService,
    private readonly apiService: ApiService
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<ArticleResolverData> {
    const params: ArticleRouteParams = route.params as ArticleRouteParams;

    let { year, month } = params;

    const previewHash = params.previewHash;
    const categorySlug = params.categorySlug;
    const articleSlug = params.previewHash ? 'cikk-elonezet' : params.articleSlug;
    const previewType = params.previewType ? params.previewType : 'accepted';
    const isYear = !isNaN(parseInt(year as string));
    const isMonth = !isNaN(parseInt(month as string));

    year = isYear && year ? year : undefined;
    month = isYear && month ? month : undefined;

    if ((!isYear || !isMonth) && !previewHash) {
      this.router
        .navigate(['/', '404'], {
          skipLocationChange: true,
        })
        .then();
    }

    const url = `${categorySlug}/${year}/${month}/${articleSlug}`;

    let request$: Observable<[ApiResult<Article>, ApiResult<RecommendationsData>, ApiResult<ArticleSearchResult[]>]>;

    if (previewHash) {
      request$ = forkJoin([
        this.articleService.getArticlePreview(articleSlug, previewHash, previewType), // article
        of({} as ApiResult<RecommendationsData>), // recommendations
        of({} as ApiResult<ArticleSearchResult[]>), // related articles
      ]);
    } else {
      // share() operator provides only 1 request from BE

      const article$ = this.articleService.getArticle(categorySlug, year as string, month as string, articleSlug).pipe(share());
      const recommendations$ = this.articleService.getArticleRecommendations(articleSlug, categorySlug);
      // itemsPerPage must be 9 because response can contain this article too
      const relatedArticles$ = (slugs: string[]): Observable<ApiResult<ArticleSearchResult[], ApiResponseMetaList>> =>
        this.tagsService.searchArticleByTags(slugs, 0, 9);
      // const freshArticles$ = this.apiService.getFreshArticles([], [], []);

      request$ = forkJoin([
        article$.pipe(
          switchMap((articleResult) =>
            this.apiService.getSocialData(articleResult?.data?.id || '').pipe(
              map(
                (socialResult) =>
                  ({
                    ...articleResult,
                    data: {
                      ...articleResult.data,
                      ...socialResult,
                    },
                  }) as ApiResult<Article>
              )
            )
          ),
          switchMap((article) => {
            if (!article?.data?.isOpinion) {
              return of({ ...article });
            }

            const publicAuthor = article.data?.publicAuthor || 'Nemzeti Sport Online';
            return this.apiService.getOpinionAuthor(publicAuthor).pipe(
              map((articles) => ({
                ...article,
                articles,
              }))
            );
          })
        ),
        recommendations$,
        article$.pipe(
          map((res: ApiResult<Article>) => res.data.tags.map((tag: Tag) => tag.slug)),
          switchMap((slugs: string[]) =>
            forkJoin([relatedArticles$(slugs)]).pipe(
              map(([relatedArticles]) => {
                const mergedArticles = [...relatedArticles.data].filter((article) => article.slug !== articleSlug).slice(0, 8);

                return {
                  ...relatedArticles,
                  data: mergedArticles,
                } as ApiResult<ArticleSearchResult[]>;
              })
            )
          )
        ),
      ]);
    }

    return request$
      .pipe(
        catchError((error: HttpErrorResponse | Error) => {
          console.error('⚠ Error during resolving %s: ', url, error);

          this.router
            .navigate(['/', '404'], {
              state: { errorResponse: JSON.stringify(error) },
              skipLocationChange: true,
            })
            .then();

          return throwError(() => error);
        })
      )
      .pipe(
        map(([article, recommendations, relatedArticles]) => ({
          article,
          recommendations,
          relatedArticles,
          year,
          month,
          articleSlug,
          categorySlug,
          url,
        }))
      );
  }
}
