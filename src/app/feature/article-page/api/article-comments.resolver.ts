import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { catchError, map, Observable, share, throwError } from 'rxjs';
import { ArticleCommentsResolverData } from './article-comments.definitions';
import { ArticleRouteParams } from '@trendency/kesma-ui';
import { ArticleService } from './article-page.service';
import { HttpErrorResponse } from '@angular/common/http';
import { tap } from 'rxjs/operators';

@Injectable()
export class ArticleCommentsResolver {
  constructor(
    private readonly articleService: ArticleService,
    private readonly router: Router
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<ArticleCommentsResolverData> {
    const params: ArticleRouteParams = route.params as ArticleRouteParams;

    const { year, month } = params;
    const { articleSlug, categorySlug } = params;

    const url = `${categorySlug}/${year}/${month}/${articleSlug}`;

    if (isNaN(parseInt(year as string)) || isNaN(parseInt(month as string))) {
      this.router
        .navigate(['/', '404'], {
          skipLocationChange: true,
        })
        .then();
      return throwError(() => new Error('Redirecting...'));
    }

    return this.articleService.getArticle(categorySlug, year as string, month as string, articleSlug).pipe(
      share(),
      tap((articleResponse) => {
        if (articleResponse.data.isCommentsDisabled) {
          const articleUrl = this.router.getCurrentNavigation()?.finalUrl?.toString().split('/').slice(0, -1);
          this.router
            .navigate(articleUrl ?? ['/', '404'], {
              state: { errorResponse: JSON.stringify('Comments are disabled for this article') },
              skipLocationChange: false,
            })
            .then();
        }
      }),
      catchError((error: HttpErrorResponse | Error) => {
        console.error('⚠ Error during resolving %s: ', url, error);

        this.router
          .navigate(['/', '404'], {
            state: { errorResponse: JSON.stringify(error) },
            skipLocationChange: true,
          })
          .then();

        return throwError(() => error);
      }),
      map((articleResponse) => ({
        article: articleResponse.data,
        meta: {
          url,
        },
      }))
    );
  }
}
