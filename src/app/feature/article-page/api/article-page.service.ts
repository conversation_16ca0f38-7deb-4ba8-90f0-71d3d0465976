import { Injectable } from '@angular/core';
import { ReqService } from '@trendency/kesma-core';
import { Observable } from 'rxjs';
import { map, switchMap } from 'rxjs/operators';
import {
  AllColumnsResponse,
  ApiResponseMeta,
  ApiResult,
  Article,
  ArticleBody,
  ArticleBodyDetails,
  ArticleBodyType,
  BackendArticle,
  BackendRecommendationsData,
  backendRecommendedArticleToArticleCard,
  externalRecommendationToArticleCard,
  previewBackendArticleToArticleCard,
  RecommendationsData,
} from '@trendency/kesma-ui';
import { ApiService, backendArticlesToArticles } from '../../../shared';
import { NsoAppArticle } from '../definitions/nso-app-article';

@Injectable({
  providedIn: 'root',
})
export class ArticleService {
  constructor(
    private readonly reqService: ReqService,
    private readonly apiService: ApiService
  ) {}

  getArticlePreview(articleSlug: string, previewHash: string, previewType: string): Observable<ApiResult<Article>> {
    const timestamp = Math.floor(new Date().getTime());
    return this.reqService
      .get<ApiResult<BackendArticle, ApiResponseMeta>>(
        `/content-page/article/${articleSlug}/preview/view?previewHash=${previewHash}&t=${timestamp.toString()}`,
        {
          params: {
            previewType,
          },
        }
      )
      .pipe(
        map(({ data, meta }) => ({
          data: backendArticlesToArticles(data),
          meta,
        }))
      );
  }

  getArticle(category: string, year: string, month: string, articleSlug: string, token?: string | null): Observable<ApiResult<Article>> {
    const tokenQuery: string = token ? `?token=${token}` : ``;
    return this.reqService
      .get<ApiResult<BackendArticle>>(`/content-page/article/${category}/${year}/${(month + '').padStart(2, '0')}/${articleSlug}${tokenQuery}`)
      .pipe(
        map(({ data, meta }: ApiResult<BackendArticle>) => ({
          data: backendArticlesToArticles(data),
          meta,
        }))
      );
  }

  getNsoAppArticleById(id: string): Observable<NsoAppArticle[]> {
    return this.reqService.get<NsoAppArticle[]>(`/sport/nso-app/article?cid=${id}`);
  }

  getArticleRecommendations(articleSlug: string, categorySlug?: string): Observable<ApiResult<RecommendationsData>> {
    if (categorySlug) {
      return this.apiService.getCategoryArticles(categorySlug, 0, 5).pipe(
        switchMap((categoryArticles) => {
          return this.reqService.get<ApiResult<BackendRecommendationsData>>(`/content-page/article/${articleSlug}/recommendation`).pipe(
            map(({ data, meta }: ApiResult<BackendRecommendationsData>) => ({
              data: {
                ...data,
                categoryArticles: categoryArticles.data,
                highPriorityArticles: data.highPriorityArticles?.map(backendRecommendedArticleToArticleCard),
                lowPriorityArticles: data.lowPriorityArticles?.map(backendRecommendedArticleToArticleCard),
                externalRecommendation: data.externalRecommendation?.map(externalRecommendationToArticleCard),
                lastThreeDaysMostReadArticles: data.lastThreeDaysMostReadArticles?.map(backendRecommendedArticleToArticleCard),
              },
              meta,
            }))
          );
        })
      ) as Observable<ApiResult<RecommendationsData>>;
    } else {
      return this.reqService.get<ApiResult<BackendRecommendationsData>>(`/content-page/article/${articleSlug}/recommendation`).pipe(
        map(({ data, meta }: ApiResult<BackendRecommendationsData>) => ({
          data: {
            ...data,
            highPriorityArticles: data.highPriorityArticles?.map(backendRecommendedArticleToArticleCard),
            lowPriorityArticles: data.lowPriorityArticles?.map(backendRecommendedArticleToArticleCard),
            externalRecommendation: data.externalRecommendation?.map(externalRecommendationToArticleCard),
            lastThreeDaysMostReadArticles: data.lastThreeDaysMostReadArticles?.map(backendRecommendedArticleToArticleCard),
          },
          meta,
        }))
      );
    }
  }

  public prepareArticleBody(body: ArticleBody[], showCompletePage = true): ArticleBody[] {
    return body
      ?.filter((bodyPart: ArticleBody) => !(!showCompletePage && bodyPart.type === ArticleBodyType.Advert))
      ?.map((bodyPart: ArticleBody) => ({
        ...bodyPart,
        details: (bodyPart.details ?? []).map((detail: ArticleBodyDetails) => ({
          ...detail,
          ...this.prepareArticleBodyDetail(detail, bodyPart.type),
        })),
      }));
  }

  public prepareArticleBodyDetail(detail: ArticleBodyDetails, type: ArticleBodyType): ArticleBodyDetails {
    let newDetail: ArticleBodyDetails;
    switch (type) {
      case ArticleBodyType.Article:
        newDetail = {
          ...detail,
          value: previewBackendArticleToArticleCard(detail.value),
        };
        break;
      default:
        newDetail = { ...detail };
    }
    return newDetail;
  }

  public getAllColumns(itemsPerPage = 99999999, ids?: string[], contentTypes?: string[]): Observable<AllColumnsResponse> {
    const params: Partial<ApiResponseMeta> = {
      rowCount_limit: itemsPerPage.toString(),
    };

    if (ids) {
      params['ids[]'] = ids;
    }

    if (contentTypes) {
      params['content_type[]'] = contentTypes;
    }

    return this.reqService.get<AllColumnsResponse>('/source/content-group/columns', {
      params: params,
    });
  }
}
