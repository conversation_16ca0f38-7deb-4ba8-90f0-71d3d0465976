import { Injectable } from '@angular/core';
import { ReqService } from '@trendency/kesma-core';
import { ApiResult, Article, ArticleCard, BackendArticle, buildArticleUrl, MinuteToMinuteBlock } from '@trendency/kesma-ui';
import { map } from 'rxjs/operators';
import { Observable, of } from 'rxjs';
import { formatDate } from '@angular/common';

type HasMinuteToMinuteBlocks = { minuteToMinuteBlocks?: MinuteToMinuteBlock[] };

@Injectable({
  providedIn: 'root',
})
export class MinuteToMinuteService {
  private readonly minuteToMinuteCache: Record<
    string,
    {
      value: Observable<MinuteToMinuteBlock[]>;
      expires: number;
    }
  > = {};

  constructor(private readonly req: ReqService) {}

  /**
   * Extracts minute to minute blocks from an article or article card
   * @param article The article or article card to extract the blocks from
   * @param cacheTimeSec The time in seconds to cache the result, defaults to 30 seconds
   */
  getBlocks(article?: Article | ArticleCard, cacheTimeSec = 30): Observable<MinuteToMinuteBlock[]> {
    if (!article) {
      console.error('[MinuteToMinuteService::getMinuteToMinuteBlocks@26] called without article!');
      return of([]);
    }

    const articleId = article.id ?? article.slug ?? '';
    if (this.minuteToMinuteCache[articleId] && this.minuteToMinuteCache[articleId].expires > Date.now()) {
      // Layout CD will be triggered many times, so we need to cache the result, to avoid unnecessary API calls
      return this.minuteToMinuteCache[articleId].value;
    }
    /**
     * Req is cached, so we can't poll the server for new data, therefore we need to add a timestamp to the request
     * However, this means we miss Varnish with each request,
     * Therefore we create a timestamp with a 30s interval (always ending in 00 or 30), so we only miss Varnish every 30 seconds
     * Browser cache will always be skipped this way
     */
    const now = new Date();
    const cachedSec = Math.floor(now.getSeconds() / cacheTimeSec) * cacheTimeSec;
    const request$ = this.req
      .get<ApiResult<BackendArticle>>(`/content-page/article/${buildArticleUrl(article).slice(1).join('/')}`, {
        params: {
          timestamp: formatDate(now, `yyyy-MM-ddTHH:mm:${cachedSec}`, 'en'),
        },
      })
      .pipe(map((article) => article?.data?.minuteToMinuteBlocks ?? []));

    this.minuteToMinuteCache[articleId] = {
      value: request$,
      expires: Date.now() + cacheTimeSec * 1000,
    };

    return request$;
  }

  /**
   // eslint-disable-next-line max-len
   * Extracts minute to minute blocks from an observable source.
   * The source is expected to emit objects with a `minuteToMinuteBlocks` property or a custom extractor can be provided
   * @param source The source to extract the blocks from
   * @param extractor A custom extractor to get an object with `minuteToMinuteBlocks`
   */
  extractBlocksFrom<T>(source: Observable<T>, extractor?: (data: T) => HasMinuteToMinuteBlocks): Observable<MinuteToMinuteBlock[]> {
    extractor ??= (data): HasMinuteToMinuteBlocks => data as HasMinuteToMinuteBlocks;
    return source?.pipe(
      map(extractor),
      map((hasMM) => hasMM?.minuteToMinuteBlocks ?? [])
    );
  }
}
