import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { catchError, map, Observable, share, throwError } from 'rxjs';
import { ArticleCommentsResolverData } from './article-comments.definitions';
import { Article } from '@trendency/kesma-ui';
import { ArticleService } from './article-page.service';
import { HttpErrorResponse } from '@angular/common/http';

@Injectable()
export class ArticleCommentsByIdResolver {
  constructor(
    private readonly articleService: ArticleService,
    private readonly router: Router
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<ArticleCommentsResolverData> {
    const queryParams = route.queryParams;

    const id = queryParams['cikk_id'];

    if (!id) {
      this.router.navigate(['/', '404'], {
        state: {},
        skipLocationChange: true,
      });
    }
    return this.articleService
      .getNsoAppArticleById(id)
      .pipe(share())
      .pipe(
        catchError((error: HttpErrorResponse | Error) => {
          console.error('⚠ Error during resolving %s: ', error);

          this.router
            .navigate(['/', '404'], {
              state: { errorResponse: JSON.stringify(error) },
              skipLocationChange: true,
            })
            .then();

          return throwError(() => error);
        }),
        map((res) => {
          const article = res[0];
          const date = new Date(article.date);
          const [, categorySlug, year, month, slug] = article.link.split('/');
          return {
            article: {
              id,
              title: article.title,
              lead: article.lead,
              avatar: '',
              primaryColumn: {
                id: '',
                title: article.realcsatorna,
                slug: categorySlug,
              },
              columnSlug: categorySlug,
              tags: [],
              body: [],
              minuteToMinute: 'not',
              isOpinion: false,
              withoutAds: false,
              isNotebook: false,
              isEditorMessageable: true,
              isAdultsOnly: false,
              publishDate: date,
              year: parseInt(year),
              month: parseInt(month),
              slug,
            } as unknown as Article,
            meta: {
              url: article.link,
            },
          };
        })
      );
  }
}
