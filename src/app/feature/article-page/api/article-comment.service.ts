import { Injectable } from '@angular/core';
import { Observable, switchMap } from 'rxjs';
import {
  ApiResult,
  CommentListResponse,
  getOrderParams,
  mapToCommentListResponse,
  Ordering,
  populateReactionsForComments,
  ReactionEvent,
} from '@trendency/kesma-ui';
import { catchError, map } from 'rxjs/operators';
import { CommentType } from './article-comments.definitions';
import { ApiService, AuthService, SecureApiService } from '../../../shared';

@Injectable({
  providedIn: 'root',
})
export class ArticleCommentsService {
  constructor(
    private readonly publicApi: ApiService,
    private readonly secureApi: SecureApiService,
    private readonly authService: AuthService
  ) {}

  submitComment(articleId: string, text: string): Observable<ApiResult<never>> {
    return this.secureApi.submitCommentFor(articleId, 'article', text);
  }

  submitReply(commentId: string, text: string): Observable<ApiResult<never>> {
    return this.secureApi.submitCommentFor(commentId, 'comment', text);
  }

  getCommentsFor(commentId: string, type: CommentType, pageNumber = 0, perPage = 10, sort: Ordering = 'latest'): Observable<CommentListResponse> {
    const commonParams = {
      ...getOrderParams(sort),
      page_limit: pageNumber,
      rowCount_limit: perPage,
    };
    return this.authService.currentUserSubject.pipe(
      switchMap((user) =>
        !user?.uid
          ? this.publicApi.getCommentsFor(commentId, commonParams, type).pipe(mapToCommentListResponse())
          : this.secureApi.getCommentsFor(commentId, commonParams, user.uid, type).pipe(
              mapToCommentListResponse(),
              switchMap((commentResponse) =>
                this.secureApi.getMyVotesFor(commentId, {}, user.uid, type).pipe(map((myVotes) => populateReactionsForComments(myVotes.data, commentResponse)))
              ),
              catchError(() => this.publicApi.getCommentsFor(commentId, commonParams, type).pipe(mapToCommentListResponse()))
            )
      )
    );
  }

  reaction(commentId: string, reaction: ReactionEvent): Observable<ApiResult<never>> {
    return this.secureApi.voteComment(commentId, reaction.liked ? 'like' : 'dislike');
  }

  report(commentId: string): Observable<ApiResult<never>> {
    return this.secureApi.voteComment(commentId, 'report');
  }

  update(commentId: string, text: string): Observable<ApiResult<never>> {
    return this.secureApi.submitCommentFor(commentId, 'comment', text, true);
  }
}
