export type NsoAppArticleImage = {
  link: string;
  caption?: string;
  source?: string;
  author?: string;
};
export type NsoAppArticle = {
  date: string;
  author?: string;
  lead?: string;
  body: string;
  image?: NsoAppArticleImage;
  title: string;
  /**
   * For some reason API sends back some pre-compiled css.
   */
  css: string;
  link: string;

  /**
   * Some hungarian properties...
   */
  csatorna_id: string;
  realcsatorna: string;
  kulcsszavak: string[];
};
