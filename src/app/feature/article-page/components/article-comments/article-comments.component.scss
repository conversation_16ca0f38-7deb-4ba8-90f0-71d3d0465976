@use 'shared' as *;
.content {
  display: flex;
  flex-direction: column;
  gap: 30px;
  max-width: 630px;
  margin: 0 auto;

  @include media-breakpoint-down(md) {
    margin: 0 10px;
  }
}

.sticky {
  position: sticky;
  align-self: flex-start;
  top: 130px;
  z-index: 1;
  width: 100%;
  background-color: var(--kui-white);
  padding: 18px 78px;
  margin: 0 -70px;

  @include media-breakpoint-down(md) {
    padding: 0;
    top: unset;
    margin: 0 0 10px;
  }

  h3 {
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: 120%; /* 19.2px */
    color: var(--kui-gray-500);

    i {
      width: 20px;
      height: 20px;
      transform: rotate(180deg);
      position: relative;
      top: 4px;
      margin-right: 10px;
    }
  }
}

h1 {
  font-size: 38px;
  font-style: normal;
  font-weight: 700;
  line-height: 110%; /* 41.8px */
  letter-spacing: -0.76px;
  text-transform: uppercase;
  color: var(--kui-gray-500);
  font-family: var(--kui-font-condensed);
}

.answer {
  nso-counting-text-area {
    width: 100%;
  }

  .right {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 30px;

    & > * {
      margin: 0;
    }
  }
}

.order {
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 160%; /* 25.6px */
  display: flex;
  justify-content: space-between;
  align-items: flex-end;

  select {
    border: none;
    font-weight: 700;
    margin-left: 10px;
  }

  nso-simple-button {
    margin: 0;
  }
}

.separator {
  width: 100%;
  height: 1px;
  background-color: var(--kui-gray-200);
  margin: -15px 0;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 0;
  overflow: hidden;
  transition: all 300ms ease-in-out;
}

.load-more-btn {
  height: 0;
  overflow: hidden;
}

.visible {
  height: 100px;
}

.comments {
  overflow-x: hidden;
}
