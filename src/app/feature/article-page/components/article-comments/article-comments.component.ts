import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import { ActivatedRoute, RouterLink } from '@angular/router';
import { BehaviorSubject, Observable, throwError } from 'rxjs';
import { catchError, map, switchMap, tap } from 'rxjs/operators';
import { SeoService } from '@trendency/kesma-core';
import { ArticleCommentsResolverData } from '../../api/article-comments.definitions';
import {
  AuthService,
  defaultMetaInfo,
  NsoCommentAnswerFormComponent,
  NsoPopupComponent,
  NsoSimpleButtonComponent,
  NsoSpinnerComponent,
} from '../../../../shared';
import { ArticleCommentsService } from '../../api/article-comment.service';
import { ApiListResult, buildArticleUrl, Comment, LoadMorePaginator, Ordering } from '@trendency/kesma-ui';
import { HttpErrorResponse } from '@angular/common/http';
import { AsyncPipe, NgForOf, NgIf } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { CommentWrapperComponent } from '../comment-wrapper/comment-wrapper.component';

@Component({
  selector: 'app-article-comments',
  templateUrl: './article-comments.component.html',
  styleUrls: ['./article-comments.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgIf,
    AsyncPipe,
    RouterLink,
    NsoCommentAnswerFormComponent,
    FormsModule,
    NsoSimpleButtonComponent,
    CommentWrapperComponent,
    NgForOf,
    NsoSpinnerComponent,
    NsoPopupComponent,
  ],
})
export class ArticleCommentsComponent implements OnInit {
  readonly routeData$ = (this.route.data as Observable<{ data: ArticleCommentsResolverData }>).pipe(
    map(({ data }) => data),
    tap((data) => this.setMeta(data))
  );
  readonly buildArticleUrl = buildArticleUrl;
  order: Ordering = 'latest';
  answerValue = '';
  readonly popupData$ = new BehaviorSubject<[string, string] | false>(false);
  readonly paginator = new LoadMorePaginator<Comment>(
    (page) => this.routeData$.pipe(switchMap(({ article }) => this.commentService.getCommentsFor(article.id, 'article', page, 20, this.order))),
    {} as ApiListResult<Comment>
  );
  readonly isLoggedIn$ = this.auth.currentUserSubject.pipe(map((user) => !!user));

  constructor(
    private readonly commentService: ArticleCommentsService,
    private readonly auth: AuthService,
    private readonly route: ActivatedRoute,
    private readonly seo: SeoService
  ) {}

  ngOnInit(): void {
    // Auth service sometimes takes a while to load.
    // Calling reset() too early will cause the paginator to load the public endpoint instead of the secure one.
    setTimeout(() => this.paginator.reset(), 200);
  }

  submit(text: string, articleId: string): void {
    if (!this.auth.currentUser) {
      this.popupData$.next(['Nincs bejelentkezve!', 'Válasza elküldéséhez kérem jelentkezzen be']);
      return;
    }

    this.commentService
      .submitComment(articleId, text)
      .pipe(
        catchError((err: HttpErrorResponse) => {
          this.popupData$.next(
            err.status === 401
              ? ['Nincs bejelentkezve!', 'Válasza elküldéséhez kérem jelentkezzen be']
              : ['Hiba történt', 'Kérjük próbálja újra pár perc múlva']
          );
          return throwError(() => err);
        })
      )
      .subscribe(() => {
        this.answerValue = '';
        this.popupData$.next(['Köszönjük!', 'Válasza hamarosan megjelenik']);
      });
  }

  onLoadMore(): void {
    this.paginator.next();
  }

  onRefresh(): void {
    this.paginator.reset();
  }

  onSortChange(sort: Ordering): void {
    this.order = sort;
    this.paginator.reset();
  }

  onPopupResult(_: boolean): void {
    this.popupData$.next(false);
    this.paginator.reset();
  }

  private setMeta(data: ArticleCommentsResolverData): void {
    this.seo.updateCanonicalUrl(data.article.seo?.seoCanonicalUrl || data.article?.canonicalUrl || `${this.seo.hostUrl}/${data.meta?.url}`, {
      skipSeoMetaCheck: true,
    });

    const { thumbnail, publicAuthor, publishDate, alternativeTitle, metaThumbnail } = data.article || {};

    if (!data.article) {
      return;
    }

    const title = data.article.seo?.seoTitle || data.article.title;

    const comboTitle = `${title} - ${defaultMetaInfo?.ogSiteName}`;
    const finalTitle = alternativeTitle && alternativeTitle.length > 0 ? alternativeTitle : comboTitle;
    const metaData = {
      ...defaultMetaInfo,
      title: finalTitle,
      description: data.article.seo?.seoDescription || data.article.excerpt || data.article.lead || defaultMetaInfo.description,
      robots: this.getRobotsMeta(data),
      ogTitle: finalTitle,
      ogImage: metaThumbnail || thumbnail,
      ogType: 'article', // TODO: check this
      articleAuthor: publicAuthor,
      articlePublishedTime: publishDate?.toISOString(),
    };

    this.seo.setMetaData(metaData);
  }

  private getRobotsMeta(data: ArticleCommentsResolverData): string {
    if (data.article.seo?.seoRobotsMeta) {
      return data.article.seo?.seoRobotsMeta;
    }
    if (data.article.robotsTag && data.article.robotsTag !== 'index, follow') {
      // By default, BE sends 'index, follow' for the columns seo Robots value, but we want to also have max-image-preview:large
      return data.article.robotsTag;
    }
    return 'index, follow, max-image-preview:large';
  }
}
