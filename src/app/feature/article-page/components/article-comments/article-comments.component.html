<ng-container *ngIf="(routeData$ | async)?.article as article">
  <section class="sticky">
    <a [routerLink]="buildArticleUrl(article, article.columnSlug, article.isOpinion)"
      ><h3><i class="icon icon-arrow-right-red"></i> {{ article.title }}</h3></a
    >
  </section>

  <div class="content">
    <h1>{{ article.title }}</h1>

    <nso-comment-answer-form
      [hasLegal]="true"
      [minLength]="3"
      [maxLength]="1000"
      [(value)]="answerValue"
      [isLoggedIn]="(isLoggedIn$ | async) || false"
      (onSubmit)="submit($event, article.id)"
    ></nso-comment-answer-form>

    <section class="order">
      <div class="left">
        <span>Rendezés:</span>
        <select [ngModel]="order" (ngModelChange)="onSortChange($event)">
          <option value="latest">Legújabb</option>
          <option value="oldest">Legrégebbi</option>
          <option value="most-popular">Legn<PERSON><PERSON><PERSON>űbb</option>
        </select>
      </div>
      <div class="right">
        <nso-simple-button (click)="onRefresh()">Frissítés</nso-simple-button>
      </div>
    </section>

    <div class="separator"></div>

    <section class="comments">
      <app-comment-wrapper *ngFor="let comment of (paginator.data$ | async) ?? []" [order]="order" [comment]="comment"></app-comment-wrapper>
    </section>

    <nso-simple-button
      *ngIf="paginator.hasMore$ | async"
      class="load-more-btn"
      [class.visible]="(paginator.isLoading$ | async) === false"
      (click)="onLoadMore()"
      >További kommentek
    </nso-simple-button>

    <section class="loading" [class.visible]="paginator.isLoading$ | async">
      <nso-spinner></nso-spinner>
    </section>
  </div>
</ng-container>

<nso-popup *ngIf="popupData$ | async as popupData" [title]="popupData?.[0]" acceptButtonLabel="Rendben" (resultEvent)="onPopupResult($event)">
  {{ popupData?.[1] }}
</nso-popup>
