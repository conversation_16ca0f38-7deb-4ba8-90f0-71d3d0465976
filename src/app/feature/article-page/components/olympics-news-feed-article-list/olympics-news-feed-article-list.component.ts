import { ChangeDetectionStrategy, ChangeDetectorRef, Component, computed, DestroyRef, inject, input, signal } from '@angular/core';
import {
  Article,
  ArticleBodyDetails,
  ArticleBodyType,
  ArticleCard,
  ArticleFileLinkDirective,
  ArticleVideoComponent,
  buildArticleUrl,
  buildTagUrl,
  GalleryData,
  GalleryElementData,
  PublicMultiAuthor,
  VoteData,
  VoteDataWithAnswer,
  VoteService,
} from '@trendency/kesma-ui';
import { forkJoin } from 'rxjs';
import { ArticleService } from '../../api/article-page.service';
import { GalleryApiService } from '../../../gallery-layer/api/gallery-api.service';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { NgForOf, NgIf, NgSwitch, NgSwitchCase, NgTemplateOutlet } from '@angular/common';
import { RouterLink } from '@angular/router';
import { PublishDatePipe } from '@trendency/kesma-core';
import {
  ArticleCardType,
  ArticleDossierComponent,
  NsoArticleCardComponent,
  NsoArticlesRelatedContentComponent,
  NsoGalleryCardComponent,
  NsoQuizComponent,
  NsoSimpleButtonComponent,
  NsoVotingComponent,
  NsoWysiwygBoxComponent,
} from '../../../../shared';

@Component({
  selector: 'app-olympics-news-feed-article-list',
  styleUrls: ['../../../news-feed/components/news-feed.component.scss', './olympics-news-feed-article-list.component.scss'],
  templateUrl: './olympics-news-feed-article-list.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgIf,
    NgForOf,
    RouterLink,
    NgTemplateOutlet,
    PublishDatePipe,
    NsoSimpleButtonComponent,
    NgSwitch,
    NgSwitchCase,
    NsoWysiwygBoxComponent,
    ArticleFileLinkDirective,
    ArticleVideoComponent,
    NsoVotingComponent,
    NsoQuizComponent,
    NsoArticleCardComponent,
    NsoGalleryCardComponent,
    ArticleDossierComponent,
    NsoArticlesRelatedContentComponent,
  ],
})
export class OlympicsNewsFeedArticleListComponent {
  articles = input<Article[]>();
  galleries = signal<Record<string, GalleryData>>({});
  displayArticles = computed(() => {
    const articles = this.articles();
    return articles?.map(this.mapArticleBody.bind(this));
  });
  protected readonly ArticleBodyType = ArticleBodyType;
  protected readonly ArticleCardType = ArticleCardType;
  private readonly articleService = inject(ArticleService);
  private readonly galleryService = inject(GalleryApiService);
  private readonly voteService = inject(VoteService);
  private readonly destroyRef = inject(DestroyRef);
  private readonly cdr = inject(ChangeDetectorRef);

  public getMultiAuthorLink(author: PublicMultiAuthor): string[] | undefined {
    return author?.slug ? ['/', 'szerzo', author?.slug] : undefined;
  }

  public getArticleLink(article: Article): string[] {
    return buildArticleUrl(article);
  }

  public getTagLink(article: Article, selectedTag: number): string[] {
    return buildTagUrl(article, selectedTag);
  }

  public getVoteData(value: VoteData): VoteDataWithAnswer {
    return this.voteService.getVoteData(value);
  }

  doubleArticleRecommendations(arr: ArticleBodyDetails[]): ArticleBodyDetails[] {
    return arr.filter((elem: ArticleBodyDetails) => elem?.value?.id);
  }

  doubleArticleRecommendation(articleBodyDetails: ArticleBodyDetails[]): ArticleCard[] {
    return this.doubleArticleRecommendations(articleBodyDetails)?.map((details: ArticleBodyDetails) => {
      const article = details.value;
      if (article.thumbnailUrl) {
        article.thumbnail = { url: article?.thumbnailUrl };
      }
      return {
        ...article,
        lead: article?.excerpt || article?.lead,
        category: {
          name: article?.primaryColumn?.title,
          slug: article?.primaryColumn?.slug,
        },
        thumbnailFocusedImages: article?.thumbnailUrlFocusedImages,
      };
    }) as ArticleCard[];
  }

  public onVotingSubmit($event: string, voteData: VoteDataWithAnswer): void {
    const votingSubmit$ = this.voteService.onVotingSubmit($event, voteData).subscribe({
      complete: () => {
        this.cdr.detectChanges();
        votingSubmit$.unsubscribe();
      },
    });
  }

  private mapArticleBody(article: Article): Article {
    this.loadEmbeddedGalleries(article);
    return {
      ...article,
      body: this.articleService.prepareArticleBody(article?.body),
      excerpt: article?.lead || article?.excerpt,
      columnSlug: article?.primaryColumn?.slug,
    };
  }

  private loadEmbeddedGalleries(article: Article): void {
    const bodyElements = article?.body ?? [];
    const gallerySubs = ((bodyElements ?? []) as GalleryElementData[])
      .filter(({ type }) => type === ArticleBodyType.Gallery)
      .map((bodyElem: GalleryElementData) => this.galleryService.getGalleryDetails(bodyElem.details[0].value?.slug));

    forkJoin(gallerySubs)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((galleries) => {
        galleries.forEach((gallery) => {
          this.galleries.set({
            ...this.galleries(),
            [gallery.id]: {
              ...gallery,
              highlightedImageUrl: gallery.highlightedImage.url,
            } as GalleryData,
          });
        });
      });
  }
}
