<div class="dossier-body">
  @if (displayArticles(); as articles) {
    @for (article of articles; track article.id; let index = $index) {
      <img *ngIf="article?.thumbnail" class="article-img" [src]="article?.thumbnail" loading="lazy" />
      <div class="article-author-list">
        <ng-container *ngFor="let author of article.publicAuthorM2M">
          <div class="article-author">
            <img class="article-avatar" [src]="author?.avatar || '/assets/images/nemzetisport.png'" [alt]="author?.description" />
            <ng-container *ngIf="getMultiAuthorLink(author) as authorLink; else authorNameTemplate">
              <a [routerLink]="authorLink">
                <ng-container *ngTemplateOutlet="authorNameTemplate"></ng-container>
              </a>
            </ng-container>
            <ng-template #authorNameTemplate>
              <strong class="article-author-name"
                >{{ author?.fullName || 'Nemzeti Sport Online' }}
                <ng-container *ngIf="$any(article).publicAuthorLocalization"> &bull; {{ $any(article).publicAuthorLocalization }} </ng-container>
              </strong>
            </ng-template>
          </div>
        </ng-container>
      </div>
      <div class="article-meta">
        <div class="article-publish-date">{{ article.publishDate | publishDate: 'yyyy.MM.dd. HH:mm' }}</div>
      </div>
      <a [routerLink]="getArticleLink(article)">
        <h2 class="article-title">{{ article?.title }}</h2>
      </a>
      <div class="article-lead" *ngIf="article?.lead">{{ article?.lead }}</div>
      <div class="article-tags" *ngIf="article?.tags?.length">
        <ng-container *ngFor="let tag of article?.tags; index as index; last as last">
          <a class="article-tag" [routerLink]="getTagLink(article, index)">{{ tag?.title }}</a>
          <div *ngIf="!last" class="dossier-divider vertical"></div>
        </ng-container>
      </div>
      <ng-container [ngTemplateOutlet]="bodyContent" [ngTemplateOutletContext]="{ article: article }"></ng-container>
      <div class="dossier-divider"></div>
    }
    <nso-simple-button [routerLink]="['/', 'hirfolyam', 'olimpia-hirfolyam']" class="load-more-btn" color="primary" round="round"
      >Teljes hírfolyam megtekintése</nso-simple-button
    >
  }
</div>
<ng-template #bodyContent let-article="article">
  <ng-container *ngFor="let element of article?.body">
    <ng-container [ngSwitch]="element.type">
      <ng-container *ngSwitchCase="ArticleBodyType.Wysywyg">
        <ng-container *ngFor="let wysiwygDetail of element?.details">
          <nso-wysiwyg-box [html]="wysiwygDetail?.value || ''" trArticleFileLink></nso-wysiwyg-box>
        </ng-container>
      </ng-container>

      <div class="block-video" *ngSwitchCase="ArticleBodyType.MediaVideo">
        <kesma-article-video [data]="element?.details[0]?.value"></kesma-article-video>
      </div>

      <div class="voting-block" *ngSwitchCase="ArticleBodyType.Voting">
        <nso-voting
          *ngIf="getVoteData(element?.details[0]?.value) as voteData"
          [data]="voteData.data"
          [voteId]="$any(voteData.votedId)"
          [showResults]="voteData.showResults"
          (vote)="onVotingSubmit($event, voteData)"
        >
        </nso-voting>
      </div>

      <ng-container *ngSwitchCase="ArticleBodyType.Quiz">
        <nso-quiz [data]="element?.details[0]?.value"></nso-quiz>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleBodyType.Article">
        <nso-article-card [data]="element?.details[0]?.value" [styleID]="ArticleCardType.FeaturedSidedImgColumnTitleLead"></nso-article-card>
      </ng-container>

      <div class="block-gallery" *ngSwitchCase="ArticleBodyType.Gallery">
        <ng-container *ngIf="galleries[element?.details[0]?.value?.id] as gallery">
          <nso-gallery-card [isInsideAdultArticleBody]="article?.isAdultsOnly" [data]="gallery" [routerLink]="['/galeria', gallery?.slug]"></nso-gallery-card>
        </ng-container>
      </div>

      <ng-container *ngSwitchCase="ArticleBodyType.SubsequentDossierNewsFeed">
        <app-article-dossier [dossier]="element?.details[0]?.value" [excludedArticleSlug]="article?.slug"></app-article-dossier>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleBodyType.DoubleArticleRecommendation">
        <nso-articles-related-content [data]="doubleArticleRecommendation(element.details)"> </nso-articles-related-content>
      </ng-container>
    </ng-container>
  </ng-container>
</ng-template>
