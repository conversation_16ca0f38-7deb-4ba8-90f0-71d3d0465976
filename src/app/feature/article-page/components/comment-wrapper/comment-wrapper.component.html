<div class="vertical-line top" *ngIf="hasTopLine"></div>
<div class="vertical-line side" *ngIf="!isLast || (isIdle$ | async) === false"></div>
<nso-comment-card
  [data]="comment"
  [childrenCount]="(childrenCount$ | async) ?? this.comment.answerCount"
  [responseFormShown]="(answerOpen$ | async) ?? false"
  [canAnswer]="(isLoggedIn$ | async) || false"
  [isLast]="isLast && !!(isIdle$ | async)"
  (responseFormShownChange)="onResponseFormShownChange($event)"
  (reaction)="onReaction($event)"
  (childrenOpenChange)="onChildrenOpenChange($event)"
  (report)="onReport(comment)"
  [canUpdate]="comment?.author?.uid === auth.currentUser?.uid"
  [isUpdating]="isUpdating"
  (update)="onUpdate($event)"
></nso-comment-card>
<div class="reply" *ngIf="answerOpen$ | async">
  <nso-comment-answer-form
    [hasLegal]="false"
    [canCancel]="true"
    [minLength]="3"
    [maxLength]="1000"
    (canceled)="onAnswerCancel()"
    (onSubmit)="onSubmit($event)"
  ></nso-comment-answer-form>
</div>
<div *ngIf="childrenOpen$ | async">
  <app-comment-wrapper
    *ngFor="let child of paginator.data$ | async; let isLast = last"
    [isLast]="isLast"
    [hasTopLine]="true"
    [comment]="child"
    [level]="level + 1"
  ></app-comment-wrapper>
  <nso-simple-button *ngIf="paginator.hasMore$ | async" class="load-more-btn" [class.visible]="(paginator.isLoading$ | async) === false" (click)="onLoadMore()">
    További kommentek
  </nso-simple-button>

  <section class="loading" [class.visible]="paginator.isLoading$ | async">
    <nso-spinner></nso-spinner>
  </section>
</div>

<nso-popup
  (resultEvent)="onConfirmReport($event)"
  *ngIf="reportPopupText$ | async as reportPopup"
  acceptButtonLabel="JELENTEM"
  cancelButtonLabel="MÉGSEM"
  title="Jelenti ezt a hozzászólást?"
>
  "{{ reportPopup }}"
</nso-popup>

<nso-popup *ngIf="popupData$ | async as popupData" [title]="popupData?.[0]" acceptButtonLabel="Rendben" (resultEvent)="onConfirmAnswer($event)">
  {{ popupData?.[1] }}
</nso-popup>
