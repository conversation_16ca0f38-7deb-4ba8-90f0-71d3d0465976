import { ChangeDetectionStrategy, Component, HostBinding, Input } from '@angular/core';
import { ApiListR<PERSON>ult, Comment, LoadMorePaginator, Ordering, ReactionEvent } from '@trendency/kesma-ui';
import { BehaviorSubject, combineLatest, take, throwError } from 'rxjs';
import { catchError, first, map, tap } from 'rxjs/operators';
import { ArticleCommentsService } from '../../api/article-comment.service';
import { HttpErrorResponse } from '@angular/common/http';
import {
  AuthService,
  NsoCommentAnswerFormComponent,
  NsoCommentCardComponent,
  NsoPopupComponent,
  NsoSimpleButtonComponent,
  NsoSpinnerComponent,
} from '../../../../shared';
import { AsyncPipe, NgForOf, NgIf } from '@angular/common';

@Component({
  selector: 'app-comment-wrapper',
  templateUrl: './comment-wrapper.component.html',
  styleUrls: ['./comment-wrapper.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, AsyncPipe, NsoCommentCardComponent, NsoCommentAnswerFormComponent, NgForOf, NsoSimpleButtonComponent, NsoSpinnerComponent, NsoPopupComponent],
})
export class CommentWrapperComponent {
  @Input() comment!: Comment;
  @Input() level = 0;
  @Input() order: Ordering = 'latest';
  @Input() isLast = false;
  @Input() hasTopLine = false;
  readonly answerOpen$ = new BehaviorSubject(false);
  readonly childrenOpen$ = new BehaviorSubject(false);
  readonly paginator = new LoadMorePaginator(
    (page) => this.commentService.getCommentsFor(this.comment?.id, 'comment', page, 10, this.order),
    {} as ApiListResult<Comment>
  );
  readonly isIdle$ = combineLatest([this.childrenOpen$, this.answerOpen$]).pipe(map(([children, answer]) => !children && !answer));
  readonly reportPopupText$ = new BehaviorSubject('');
  readonly popupData$ = new BehaviorSubject<[string, string] | false>(false);
  readonly childrenCount$ = this.paginator.meta$.pipe(map((res) => res?.limitable?.rowAllCount));
  readonly isLoggedIn$ = this.auth.currentUserSubject.pipe(map((user) => !!user));

  isUpdating = false;

  constructor(
    private readonly commentService: ArticleCommentsService,
    public readonly auth: AuthService
  ) {}

  @HostBinding('class.ident') get hasIdent(): boolean {
    return this.level > 0 && this.level < 3; // After 3 level the offset makes the action buttons break in multiple lines
  }

  onAnswerCancel(): void {
    this.answerOpen$.next(false);
  }

  onSubmit(text: string): void {
    if (!this.auth.currentUser) {
      this.popupData$.next(['Nincs bejelentkezve!', 'Válasza elküldéséhez kérem jelentkezzen be']);
      return;
    }

    this.commentService
      .submitReply(this.comment.id, text)
      .pipe(
        first(),
        catchError((err: HttpErrorResponse) => {
          this.popupData$.next(
            err.status === 401
              ? ['Nincs bejelentkezve!', 'Válasza elküldéséhez kérem jelentkezzen be']
              : ['Hiba történt', 'Kérjük próbálja újra pár perc múlva']
          );
          return throwError(() => err);
        })
      )
      .subscribe(() => {
        this.popupData$.next(['Köszönjük!', 'Válasza hamarosan megjelenik']);
        this.answerOpen$.next(false);
      });
  }

  onResponseFormShownChange(isOpen: boolean): void {
    this.answerOpen$.next(isOpen);
  }

  onUpdate(comment: string): void {
    this.isUpdating = true;
    this.commentService
      .update(this.comment.id, comment)
      .pipe(
        take(1),
        tap(() => (this.isUpdating = false)),
        catchError((error: HttpErrorResponse) => {
          this.popupData$.next(['Sikertelen művelet!', 'Hozzászólás módosítása meghiúsult, kérem később próbálja újra!']);
          return throwError(() => error);
        })
      )
      .subscribe(() => {
        this.popupData$.next(['Sikeres művelet!', 'Hozzászólása módosult!']);
        this.comment = {
          ...this.comment,
          text: comment,
        };
      });
  }

  onReaction(reaction: ReactionEvent): void {
    if (this.comment.myReaction === 'like') {
      return;
    }

    this.commentService.reaction(this.comment.id, reaction).subscribe();
    this.comment = {
      ...this.comment,
      likeCount: this.comment.likeCount + 1,
      myReaction: 'like',
    };
  }

  onChildrenOpenChange(isOpen: boolean): void {
    this.childrenOpen$.next(isOpen);
    if (isOpen) {
      this.paginator.reset();
    }
  }

  onReport(comment: Comment): void {
    this.reportPopupText$.next(comment.text);
  }

  onConfirmReport(confirm: boolean): void {
    if (confirm) {
      this.commentService.report(this.comment.id).subscribe();
    }

    this.reportPopupText$.next('');
  }

  onConfirmAnswer(_: boolean): void {
    this.popupData$.next(false);
    this.paginator.reset();
  }

  onLoadMore(): void {
    this.paginator.next();
  }
}
