@use 'shared' as *;

:host {
  position: relative;
  display: block;
  width: calc(100% - 56px);

  @include media-breakpoint-down(md) {
    width: calc(100% - 20px);
  }
}

.ident {
  left: 58px;

  @include media-breakpoint-down(md) {
    left: 20px;
  }
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 0;
  overflow: hidden;
  transition: all 300ms ease-in-out;
}

.load-more-btn {
  height: 0;
  overflow: hidden;
}

.visible {
  height: 100px;
}

.vertical-line {
  position: absolute;
  width: 1px;
  background-color: var(--kui-gray-150);
  left: 25px;

  @include media-breakpoint-down(md) {
    display: none;
  }

  &.side {
    top: 35px;
    height: calc(100% - 55px);
  }

  &.top {
    top: -10px;
    height: 20px;
  }
}

.reply {
  position: relative;
  background-color: var(--kui-white);
  margin: 10px 0 10px 30px;

  @include media-breakpoint-down(md) {
    margin: 0;
  }
}
