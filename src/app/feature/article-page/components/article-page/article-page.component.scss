@use 'shared' as *;

:host {
  aside {
    @include media-breakpoint-down(sm) {
      display: none;
    }
  }

  app-sidebar {
    ::ng-deep {
      app-layout {
        width: 300px;

        @include media-breakpoint-down(md) {
          width: 100%;
        }
      }
    }
  }

  .olympics-article {
    ::ng-deep {
      .article-page-divider {
        display: none;
      }
      .article-header-title,
      .block-content a {
        color: #009ce0;
      }
      .custom-text-style.highlight {
        font-family: var(--kui-font-condensed);
        padding: 0px 24px;
        background-color: #009ce0;
        font-size: 24px;
        min-height: 60px;
        font-style: normal;
        font-weight: 700;
        line-height: 54px; /* 225% */
        letter-spacing: 0.48px;
        text-transform: uppercase;
        display: flex;
        align-items: center;
        p {
          font-family: var(--kui-font-condensed);
          font-size: inherit;
        }
      }
      .tag-container-tag {
        background-color: #022366;
      }
    }
  }

  .article-page {
    &.mobile-app-article {
      margin: 0;
      .wrapper {
        width: 100%;
        max-width: none;
      }
    }
    margin: 40px 0;

    // This is need for fix the sidebar overhangs.
    .wrapper-row {
      margin-right: -35px;

      @include media-breakpoint-down(md) {
        margin-right: 0;
      }
    }

    // Custom body layout.
    .wrapper-body {
      display: flex;
      gap: 24px;

      @include media-breakpoint-down(lg) {
        flex-direction: column;
      }

      @include media-breakpoint-down(md) {
        .left-column {
          display: none;
        }
      }

      .right-column {
        width: 100%;
        overflow: hidden;

        .lead {
          font-family: var(--kui-font-primary);
          font-weight: 700;
          font-size: 22px;
          line-height: 33px;
          padding-bottom: 15px;
          @include media-breakpoint-down(md) {
            font-size: 16px;
            line-height: 24px;
            padding-bottom: 5px;
          }
        }

        .embed-pr-advert {
          width: 100%;
          display: flex;
          justify-content: center;
          margin: 10px auto;
        }

        .btn-comment {
          display: flex;
          justify-content: center;
          align-items: center;
          gap: 16px;
          text-transform: uppercase;

          .icon-comment {
            @include icon('icons/icon-comment.svg');
            width: 18px;
            height: 16px;
            margin: 0;
          }
        }
      }

      .block-title {
        font-size: 16px;
        font-family: var(--kui-font-primary);
        font-style: normal;
        font-weight: 400;
        line-height: 160%;
        margin-bottom: 16px;
        color: var(--kui-gray-300);
      }

      .share {
        margin-bottom: 40px;

        nso-social-buttons {
          flex-wrap: wrap;
          @include media-breakpoint-up(md) {
            max-width: 150px;
          }
        }
      }
      .alt-color {
        span,
        kesma-icon {
          color: #646464;
        }

        nso-simple-button {
          &::ng-deep {
            .btn-primary {
              background-color: #ffe035;
              border: 1px solid #ffe035;
            }
          }
        }
      }
    }

    .article-source {
      height: 56px;

      @include media-breakpoint-down(sm) {
        display: flex;
        justify-content: center;
      }

      &-text {
        font-size: 18px;
        line-height: 24px;
        text-transform: uppercase;
        color: var(--kui-red-400);

        @include media-breakpoint-down(sm) {
          font-size: 14px;
        }

        &-img {
          margin-bottom: 3px;
        }
      }
    }

    .minute-by-minute-block {
      border: 1px solid var(--kui-gray-200);
      padding: 20px 32px;
      margin-bottom: 20px;

      &-header {
        margin-bottom: 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 20px;
      }

      &-time {
        font-size: 16px;
        font-weight: 700;
        line-height: 26px;
        color: var(--kui-gray-550);
      }

      &-buzzword {
        word-break: break-all;
      }

      &-title {
        font-size: 22px;
        line-height: 29px;
        text-transform: uppercase;
        font-family: var(--kui-font-condensed);
        margin-bottom: 12px;
      }
    }

    .article-page-title {
      color: var(--kui-red-400);
      font-size: 24px;
      font-family: var(--kui-font-primary);
      font-weight: 700;
      line-height: 130%;
      margin: 25px 0;
    }

    .recommendation-block {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr;
      grid-gap: 34px 24px;
      margin-bottom: 30px;

      @include media-breakpoint-down(md) {
        display: flex;
        flex-direction: column;
        grid-gap: 0;
      }
    }

    .sport-radio {
      font-family: var(--kui-font-primary);
      font-weight: 700;
      font-size: 24px;
      margin-bottom: 5px;
      line-height: 31px;
      color: var(--kui-gray-600);
    }

    &-thumbnail {
      width: 100%;
      object-fit: cover;
      margin-bottom: 20px;

      &-infos {
        display: flex;
        justify-content: center;
        gap: 4px;
        color: var(--kui-gray-600);
        font-size: 14px;
        margin-bottom: 20px;

        span {
          &::after {
            content: ',';
          }

          &:last-child {
            &::after {
              content: '';
            }
          }
        }
      }
    }

    &-divider {
      width: 100%;
      background-color: var(--kui-gray-200);
      margin: 40px 0;
      height: 1px;
    }

    nso-quiz,
    nso-voting,
    nso-multi-voting,
    nso-simple-button {
      display: block;
      margin-bottom: 20px;
    }
  }

  .highlight-video {
    margin-bottom: 36px;
    display: block;
  }

  .tags {
    &.mobile {
      display: none;
      @include media-breakpoint-down(md) {
        display: block;
        padding: 20px 0px;
        ::ng-deep {
          .tag-container {
            gap: 10px;
            max-width: 100%;
          }
        }
      }
    }
  }

  .wrapper {
    &.protected {
      ::ng-deep .raw-html-embed {
        min-height: 500px;
      }

      .share {
        @include media-breakpoint-down(md) {
          display: none;
        }

        nso-social-buttons {
          margin: 10px 0 30px;
        }
      }

      .article-source {
        height: 56px;

        @include media-breakpoint-down(sm) {
          display: flex;
          justify-content: center;
        }

        &-text {
          font-size: 18px;
          line-height: 24px;
          text-transform: uppercase;
          color: var(--kui-red-400);

          @include media-breakpoint-down(sm) {
            font-size: 14px;
          }

          &-img {
            margin-bottom: 3px;
          }
        }
      }
    }
  }
}

nso-wysiwyg-box::ng-deep .table-wrapper {
  @include media-breakpoint-down(sm) {
    overflow-x: auto;
    table {
      display: block;
    }
  }
  &:not(.table-style-align-left, .table-style-align-right) {
    overflow-x: auto;
  }
}

.external {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20px;

  @include media-breakpoint-up(md) {
    margin-bottom: 40px;
  }

  &-link {
    color: var(--kui-red-400);
    font-size: 18px;
    font-weight: 700;
    text-decoration: underline;
  }
}
