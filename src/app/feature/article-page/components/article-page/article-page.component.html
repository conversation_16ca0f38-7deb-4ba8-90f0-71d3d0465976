<ng-container *ngIf="!isUserAdultChoice && article?.isAdultsOnly; else articleContent">
  <section class="article-page" [class.mobile-app-article]="(showCompleteArticlePage$ | async) === false" [class.olympics-article]="isOlympicsArticle()">
    <nso-adult (isUserAdult)="onIsUserAdultChoose($event)"></nso-adult>
  </section>
</ng-container>

<ng-template #articleContent>
  <ng-container *ngIf="article?.isProtectedContent; else normalArticleContent" [ngTemplateOutlet]="protectedArticleContent"></ng-container>
</ng-template>

<ng-template #protectedArticleContent>
  <section class="article-page" [class.mobile-app-article]="(showCompleteArticlePage$ | async) === false" [class.olympics-article]="isOlympicsArticle()">
    <div class="wrapper protected">
      <nso-article-header [data]="article"></nso-article-header>

      <nso-protected-content-user-info [isLoading]="isProtectedContentLoading"></nso-protected-content-user-info>
      <ng-container *ngIf="article?.body?.[0]?.details?.[0]?.value">
        <nso-wysiwyg-box [html]="article?.body?.[0]?.details?.[0]?.value" [useTableWrapper]="true"></nso-wysiwyg-box>
      </ng-container>
      <!-- Display only first wysiwyg element, because of iframe height bug (skip eadvert) -->
      <!--<ng-container [ngTemplateOutletContext]="{ body: article.body }" [ngTemplateOutlet]="bodyContent"></ng-container>-->

      <nso-protected-content-navigator [article]="article" [categoryArticles]="categoryArticlesFullList"></nso-protected-content-navigator>

      <div class="share">
        <div class="block-title">Megosztás</div>
        <nso-social-buttons [isReactionDisabled]="true" [link]="articleLink"></nso-social-buttons>
      </div>
      <div class="tags">
        <div class="block-title">Címkék</div>
        <nso-tags [data]="article.tags"></nso-tags>
      </div>

      <div *ngIf="article.isOpinion && article?.articleSource" class="article-source">
        <a [href]="article.articleSource" class="article-source-text" target="_blank">
          az eredeti, teljes írást itt olvashatja el
          <img alt="Navigálás" class="article-source-text-img" src="/assets/images/icons/arrow-right-black.svg" />
        </a>
      </div>

      <div class="article-page-title">Ezek is érdekelhetik</div>
      <div #externalRecommendationsBlock class="recommendation-block">
        <nso-article-card
          *ngFor="let article of externalRecommendation"
          [data]="article"
          [styleID]="ArticleCardType.ExternalRecommendation"
          nso-article-card
        ></nso-article-card>
      </div>
    </div>
  </section>
</ng-template>

<ng-template #normalArticleContent>
  <section
    *ngIf="article"
    class="article-page"
    [class.mobile-app-article]="(showCompleteArticlePage$ | async) === false"
    [class.olympics-article]="isOlympicsArticle()"
  >
    <div class="wrapper">
      <div class="wrapper-row">
        <div class="article-page-divider"></div>
        <!--
        <nso-sport-radio
          (playingChange)="radioService.radioClick()"
          (volumeChange)="radioService.volumeChange($event)"
          [playing]="(radioService.isPlaying$ | async) || false"
        ></nso-sport-radio>
        -->
      </div>
    </div>
    <div class="wrapper" [class.with-aside]="showCompleteArticlePage$ | async">
      <div class="left-column">
        <ng-container *ngTemplateOutlet="videoTemplate; context: { position: 'above_title' }"></ng-container>
        <nso-article-header [data]="article">
          <ng-container social *ngTemplateOutlet="socialTemplate"></ng-container>
        </nso-article-header>

        <figure *ngIf="article.thumbnail && !article?.videoLead?.video && !article?.hideThumbnailFromBody">
          <img
            withFocusPoint
            [data]="article?.thumbnailFocusedImages"
            [alt]="article.thumbnailInfo?.altText"
            [displayedUrl]="article.thumbnail"
            [displayedAspectRatio]="{ desktop: '16:9' }"
            class="article-page-thumbnail"
            loading="eager"
          />
          <div class="article-page-thumbnail-infos">
            <span *ngIf="article.thumbnailInfo?.photographer as photographer">{{ photographer }}</span>
            <span *ngIf="article.thumbnailInfo?.caption as caption">{{ caption }}</span>
            <span *ngIf="article.thumbnailInfo?.source as source">{{ source }}</span>
          </div>
        </figure>
        <ng-container *ngTemplateOutlet="videoTemplate; context: { position: 'below_title' }"></ng-container>
        <div class="wrapper-body">
          <div class="left-column">
            <div class="share">
              <div class="block-title">Megosztás</div>
              <ng-container *ngTemplateOutlet="socialTemplate"></ng-container>
            </div>
            <div class="tags">
              <div class="block-title">Címkék</div>
              <nso-tags [data]="article.tags"></nso-tags>
            </div>
          </div>
          <div class="right-column">
            <div class="lead">{{ article.lead }}</div>

            <div *ngIf="embedPrAdvert" [innerHTML]="embedPrAdvert" class="embed-pr-advert"></div>

            <ng-container [ngTemplateOutletContext]="{ body: article.body }" [ngTemplateOutlet]="bodyContent"></ng-container>

            <ng-container [ngTemplateOutlet]="minuteByMinuteTemplate"></ng-container>
            <div class="tags mobile">
              <nso-tags [data]="article.tags"></nso-tags>
            </div>

            <div *ngIf="article.columnSlug === 'utanpotlassport'" class="external">
              <a class="external-link" href="https://www.utanpotlassport.hu/" target="_blank">TOVÁBBI KOROSZTÁLYOS HÍREK</a>
            </div>

            <div #dataTrigger *ngIf="article"></div>
            <div [class.alt-color]="isAltColor()">
              <nso-simple-button *ngIf="comments$ | async as comments" [routerLink]="['kommentek']" [wide]="true" round="round">
                <div class="btn-comment">
                  <kesma-icon name="comment-colored" size="18" />
                  <span>{{ article?.commentCount }} Komment</span>
                </div>
              </nso-simple-button>
            </div>

            <div *ngIf="article.isOpinion && article?.articleSource" class="article-source">
              <a [href]="article.articleSource" class="article-source-text" target="_blank">
                az eredeti, teljes írást itt olvashatja el
                <img alt="Navigálás" class="article-source-text-img" src="/assets/images/icons/arrow-right-black.svg" />
              </a>
            </div>
          </div>
        </div>

        <!-- <app-strossle-advert *ngIf="!isOlympicsNewsFeed()" [element]="{ id: 'strossle_placeholder' }"></app-strossle-advert> -->
        <div class="rtl-container">
          <div *ngIf="!isOlympicsNewsFeed()" class="rltdwidget" data-widget-id="6710261"></div>
        </div>

        <ng-container *ngIf="!isOlympicsArticle()">
          <div class="article-page-title">Legfrissebb hírek</div>
          <nso-article-card
            *ngFor="let article of relatedArticles"
            [data]="article"
            [styleID]="ArticleCardType.FeaturedSidedBottomColumnTitleDate"
            nso-article-card
          ></nso-article-card>
          <div class="article-page-divider"></div>
          <kesma-nativterelo [tereloUrl]="tereloUrl"></kesma-nativterelo>
          <div class="article-page-title">Ezek is érdekelhetik</div>
          <div #externalRecommendationsBlock class="recommendation-block">
            <nso-article-card
              *ngFor="let article of externalRecommendation"
              [data]="article"
              [styleID]="ArticleCardType.ExternalRecommendation"
              nso-article-card
            ></nso-article-card>
          </div>
        </ng-container>
        <ng-container
          *ngIf="isOlympicsNewsFeed() && olympicsNewsFeedArticles()"
          [ngTemplateOutlet]="olympicsNewsFeed"
          [ngTemplateOutletContext]="{ $implicit: olympicsNewsFeedArticles() }"
        >
        </ng-container>
      </div>
      <aside *ngIf="(showCompleteArticlePage$ | async) && !isOlympicsArticle()">
        <app-sidebar *ngIf="articleSlug && categorySlug" [articleId]="article.id" [articleSlug]="articleSlug" [categorySlug]="categorySlug" [hasAds]="true">
        </app-sidebar>
      </aside>
    </div>
  </section>
</ng-template>

<ng-template #bodyContent let-body="body">
  <ng-container *ngFor="let element of body">
    <ng-container [ngSwitch]="element.type">
      <ng-container *ngSwitchCase="ArticleBodyType.Wysywyg">
        <ng-container *ngFor="let wysiwygDetail of element?.details">
          <nso-wysiwyg-box [html]="wysiwygDetail?.value" [useTableWrapper]="true" trArticleFileLink></nso-wysiwyg-box>
        </ng-container>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleBodyType.Voting">
        @if (voteCache[element?.details?.[0]?.value?.id ?? ''] | async; as voteData) {
          <nso-voting (vote)="onVotingSubmit($event, voteData)" [data]="voteData.data" [voteId]="voteData.votedId" />
        }
      </ng-container>

      <ng-container *ngSwitchCase="ArticleBodyType.MultiVoting">
        <nso-multi-voting
          *ngIf="multiVoteCache?.[element?.details?.[0]?.value?.id] | async as voteData"
          (vote)="onMultiVotingSubmit($event, voteData, element?.id)"
          [data]="voteData?.data"
          [showResults]="multiVoteService.hasVoted(voteData)"
          [userVotes]="voteData?.votedIds"
        >
        </nso-multi-voting>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleBodyType.Quiz">
        <nso-quiz [data]="element?.details[0]?.value"></nso-quiz>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleBodyType.MediaVideo">
        <kesma-article-video [data]="element?.details[0]?.value"></kesma-article-video>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleBodyType.Gallery">
        <ng-container *ngIf="galleries[element?.details?.[0]?.value?.id] as gallery">
          <nso-gallery-card [isInsideAdultArticleBody]="article?.isAdultsOnly" [data]="gallery"></nso-gallery-card>
        </ng-container>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleBodyType.SportDailySchedules">
        <app-eb-daily-program [fillByDate]="[element?.details?.[0]?.value?.slug, element?.details?.[1]?.value]"></app-eb-daily-program>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleBodyType.Article">
        <nso-article-card [data]="element?.details[0]?.value" [styleID]="ArticleCardType.FeaturedSidedImgColumnTitleLead" nso-article-card></nso-article-card>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleBodyType.DoubleArticleRecommendation">
        <nso-articles-related-content [data]="doubleArticleRecommendation(element.details)"></nso-articles-related-content>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleBodyType.SubsequentDossierNewsFeed">
        <app-article-dossier [dossier]="element?.details[0]?.value" [excludedArticleSlug]="articleSlug"></app-article-dossier>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleBodyType.SportSingleElimination">
        <app-eb-single-elimination [explicitCompetitionSlug]="element?.details[0]?.value?.slug" *ngIf="isEbEnabled"></app-eb-single-elimination>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleBodyType.SportCompetitionPhaseStat">
        <app-eb-teams
          *ngIf="element?.details[0]?.value?.slug && element?.details[1]?.value?.id"
          [desktopWidth]="6"
          [explicitCompetitionSlug]="element?.details[0]?.value?.slug"
          [phaseId]="element?.details[1]?.value?.id"
        ></app-eb-teams>
      </ng-container>
    </ng-container>
  </ng-container>
</ng-template>

<ng-template #minuteByMinuteTemplate>
  <ng-container *ngIf="article?.minuteToMinute !== MinuteToMinuteState.NOT">
    <div *ngFor="let minuteToMinute of (minuteToMinutes$ | async) ?? []" class="minute-by-minute-block">
      <div class="minute-by-minute-block-header">
        <div *ngIf="minuteToMinute?.date && !article?.minuteToMinuteTimeHidden" class="minute-by-minute-block-time">
          {{ minuteToMinute.date | formatDate: 'h-m' }}
        </div>
        <div *ngIf="minuteToMinute?.buzzword as buzzword" class="minute-by-minute-block-buzzword">{{ buzzword }}</div>
      </div>
      <h5 class="minute-by-minute-block-title">{{ minuteToMinute?.title }}</h5>
      <ng-container [ngTemplateOutletContext]="{ body: minuteToMinute.body }" [ngTemplateOutlet]="bodyContent"></ng-container>
    </div>
  </ng-container>
</ng-template>

<ng-template #videoTemplate let-position="position">
  <ng-container *ngIf="article?.videoLead?.video as video">
    <kesma-article-video
      *ngIf="article?.videoLead?.position === position"
      [data]="video"
      [useCoverThumbnail]="true"
      class="highlight-video"
    ></kesma-article-video>
  </ng-container>
</ng-template>

<ng-template #socialTemplate>
  <nso-social-buttons
    [link]="articleLink"
    [likeCount]="article?.likeCount ?? 0"
    [isReactionDisabled]="isReactionDisabled"
    [isAlreadyReacted]="isAlreadyReacted"
    (reactionClicked)="handleReactionClicked()"
  >
  </nso-social-buttons>
</ng-template>

<nso-popup
  *ngIf="showNotLoggedInPopup"
  (resultEvent)="handleNotLoggedInUserReaction($event)"
  acceptButtonLabel="Belépek"
  cancelButtonLabel="Mégsem"
  title="Nincs bejelentkezve..."
>
  Like-hoz kérem jelentkezzen be!
</nso-popup>

<nso-popup
  *ngIf="showAlreadyReactedPopup"
  (resultEvent)="showAlreadyReactedPopup = $event"
  [showAcceptButton]="false"
  cancelButtonLabel="Bezár"
  title="Sajnáljuk, de ezt a cikket már lájkolta!"
>
</nso-popup>

<ng-template #olympicsNewsFeed let-articles>
  <app-olympics-news-feed-article-list [articles]="articles"></app-olympics-news-feed-article-list>
</ng-template>
