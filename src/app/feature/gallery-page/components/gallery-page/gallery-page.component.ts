import { ChangeDetectionStrategy, Component, OnDestroy, OnInit } from '@angular/core';
import { createCanonicalUrlForPageablePage, GalleryData, LimitableMeta } from '@trendency/kesma-ui';
import { ActivatedRoute } from '@angular/router';
import { map, takeUntil } from 'rxjs/operators';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import { createNSOTitle, defaultMetaInfo, NsoGalleryCardComponent, NsoPagerComponent } from '../../../../shared';
import { Observable, Subject } from 'rxjs';
import { AsyncPipe, NgForOf, NgIf } from '@angular/common';

@Component({
  selector: 'app-galleries-page',
  templateUrl: './gallery-page.component.html',
  styleUrls: ['./gallery-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [AsyncPipe, NgForOf, NsoGalleryCardComponent, NsoPagerComponent, NgIf],
})
export class GalleryPageComponent implements OnInit, OnDestroy {
  showCompleteArticlePage$: Observable<boolean> = this.route.data.pipe(
    map((data) => data['isMobileApp']),
    map((onlyBody) => !onlyBody)
  );

  galleries$: Observable<GalleryData[]> = this.route.data.pipe(map((res) => res?.['data']?.data));

  limitables$: Observable<LimitableMeta> = this.route.data.pipe(map((res) => res?.['data']?.meta?.limitable));

  private readonly destroy$: Subject<boolean> = new Subject();

  constructor(
    private readonly route: ActivatedRoute,
    private readonly seo: SeoService
  ) {}

  ngOnInit(): void {
    this.route.data.pipe(takeUntil(this.destroy$)).subscribe();
    this.setMetaData();
  }

  public ngOnDestroy(): void {
    this.destroy$.next(true);
    this.destroy$.complete();
  }

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage('galeriak');
    if (canonical) this.seo.updateCanonicalUrl(canonical);
    const title = createNSOTitle('Galériák');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    };
    this.seo.setMetaData(metaData);
  }
}
