@use 'shared' as *;

:host {
  display: block;

  .galleries-page {
    margin: 40px 0;

    &.mobile-app-gallery {
      margin: 0;
      .wrapper {
        width: 100%;
        max-width: none;
      }
    }
  }

  .title {
    color: var(--kui-orange-600);
    margin: 20px 0;
  }

  nso-pager {
    display: flex;
    justify-content: flex-end;
    margin-top: 50px;
  }

  .flex-container {
    display: flex;
    flex-wrap: wrap;
    margin-left: -15px;
    margin-right: -15px;

    nso-gallery-card {
      width: 25%;
      padding: 12px;

      @include media-breakpoint-down(md) {
        width: 33.33%;
      }

      @include media-breakpoint-down(md) {
        width: 50%;
      }

      @include media-breakpoint-down(sm) {
        width: 100%;
      }
    }
  }
}
