<section class="galleries-page" [class.mobile-app-gallery]="(showCompleteArticlePage$ | async) === false">
  <div class="wrapper">
    <div class="flex-container">
      <ng-container *ngFor="let gallery of galleries$ | async">
        <nso-gallery-card [data]="gallery"></nso-gallery-card>
      </ng-container>
    </div>

    <nso-pager
      *ngIf="limitables$ | async as limitable"
      [rowAllCount]="limitable.rowAllCount!"
      [rowOnPageCount]="limitable.rowOnPageCount!"
      [hasFirstLastButton]="true"
      [showTotalPagesPositionAtRight]="true"
      [isCountPager]="true"
    >
    </nso-pager>
  </div>
</section>
