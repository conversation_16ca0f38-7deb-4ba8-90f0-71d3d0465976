import { Routes } from '@angular/router';
import { GalleryPageResolver } from './api/gallery-page.resolver';
import { GalleryPageComponent } from './components/gallery-page/gallery-page.component';
import { PageValidatorGuard } from '@trendency/kesma-ui';

export const galleryPageRouting: Routes = [
  {
    path: '',
    component: GalleryPageComponent,
    canActivate: [PageValidatorGuard],
    runGuardsAndResolvers: 'paramsOrQueryParamsChange', // To react pager changes!
    resolve: {
      data: GalleryPageResolver,
    },
    providers: [GalleryPageResolver],
  },
];
