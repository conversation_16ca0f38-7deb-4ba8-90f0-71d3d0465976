import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { ApiResponseMetaList, ApiResult, GalleryData, RedirectService, toBool } from '@trendency/kesma-ui';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { ApiService } from '../../../shared';

@Injectable()
export class GalleryPageResolver {
  constructor(
    private readonly apiService: ApiService,
    private readonly router: Router,
    private readonly redirectService: RedirectService
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<ApiResult<GalleryData[], ApiResponseMetaList>> {
    const pageIndex = route.queryParams['page'] ? route.queryParams['page'] - 1 : 0;
    return this.apiService.getGalleries(pageIndex).pipe(
      map((result) => {
        if (this.redirectService.shouldBeRedirect(pageIndex, result?.data)) {
          this.redirectService.redirectOldUrl(`galeriak`, false, 302);
        }
        return {
          ...result,
          data: result.data.map((gallery) => ({
            ...gallery,
            publishDate: new Date(String(gallery.publicDate)),
            isAdult: toBool(gallery?.isAdult),
          })),
        };
      }),
      catchError((error) => {
        this.router.navigate(['/', '404'], {
          state: { errorResponse: JSON.stringify(error) },
          skipLocationChange: true,
        });
        return throwError(error);
      })
    );
  }
}
