import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { backendDateToDate, Video, VideoCard } from '@trendency/kesma-ui';
import { VideoService } from './video.service';

@Injectable()
export class VideoResolverService {
  constructor(
    private readonly videoService: VideoService,
    private readonly router: Router
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<Video> {
    const slug = route.params['slug'];

    return this.videoService.getVideo(slug).pipe(
      map(({ data }) => ({
        ...data[0],
        similarVideos: (data[0]?.similarVideos ?? []).map((video: VideoCard) => ({
          ...video,
          publishDate: backendDateToDate(video.publishDate as string) as Date,
          category: {
            slug: video.columnSlug,
            name: video.columnTitle,
          },
          thumbnail: {
            url: video.coverImage as string,
            alt: 'Videó minta kép: ' + video.title,
          },
        })),
      })),
      catchError((error) => {
        console.error(' >> navigation error: ', error);
        this.router.navigate(['/', '404'], {
          state: { errorResponse: JSON.stringify(error) },
          skipLocationChange: true,
        });
        return throwError(error);
      })
    );
  }
}
