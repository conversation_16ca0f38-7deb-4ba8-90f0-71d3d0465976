import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { Article, ArticleVideoComponent, buildArticleUrl, Video, VideoCard, VideoComponentObject, videoToArticle, videoToVideoCard } from '@trendency/kesma-ui';
import { ActivatedRoute, Data } from '@angular/router';
import { ArticleService } from '../article-page/api/article-page.service';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import {
  ArticleCardType,
  defaultMetaInfo,
  NsoArticleHeaderComponent,
  NsoBlockTitleRowComponent,
  NsoSocialButtonsComponent,
  NsoVideoCardComponent,
  RadioService,
  VideoCardType,
} from '../../shared';
import { NgForOf, NgIf } from '@angular/common';
import { SidebarComponent } from '../layout/components/sidebar/sidebar.component';

@Component({
  selector: 'app-video',
  templateUrl: './video.component.html',
  styleUrls: ['./video.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    ArticleVideoComponent,
    NsoArticleHeaderComponent,
    NsoSocialButtonsComponent,
    NsoBlockTitleRowComponent,
    NsoVideoCardComponent,
    NgForOf,
    SidebarComponent,
    NgIf,
  ],
})
export class VideoComponent implements OnInit {
  video: Video;
  similarVideos: VideoCard[] = [];
  VideoCardType = VideoCardType;
  columnBgColor?: string;
  ArticleCardType = ArticleCardType;
  buildArticleUrl = buildArticleUrl;

  constructor(
    private readonly activatedRoute: ActivatedRoute,
    private readonly articleService: ArticleService,
    public readonly radioService: RadioService,
    private readonly seo: SeoService,
    private readonly cdr: ChangeDetectorRef
  ) {}

  get videoAsArticle(): Article {
    return videoToArticle(this.video, this.columnBgColor);
  }

  get videoAsVideoComponentObject(): VideoComponentObject {
    return this.video as unknown as VideoComponentObject;
  }

  get videoAsVideoCard(): VideoCard {
    return videoToVideoCard(this.video);
  }

  ngOnInit(): void {
    this.activatedRoute.data.subscribe(({ data }: { data: Video } | Data) => {
      this.video = data;
      this.similarVideos = [];
      if (this.video.columnId) {
        this.fetchColumnById(this.video.columnId);
        this.similarVideos = data?.similarVideos;
      }
      this.setMetaData();
      this.cdr.markForCheck();
    });
  }

  fetchColumnById(columnId: string): void {
    this.articleService.getAllColumns(1, [columnId], ['video']).subscribe(({ data }) => {
      if (data[0]?.titleColor) {
        this.columnBgColor = data[0].titleColor;
      }
    });
  }

  private setMetaData(): void {
    if (!this.video) {
      return;
    }

    const title = `${this.video?.title} - Nemzeti Sport Online`;

    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
      ogImage: this.video?.thumbnail as string,
      ogDescription: this.video?.description || defaultMetaInfo.ogDescription,
    };
    this.seo.setMetaData(metaData);
  }
}
