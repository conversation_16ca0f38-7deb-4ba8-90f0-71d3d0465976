<section>
  <div class="wrapper">
    <kesma-article-video [data]="videoAsVideoComponentObject"></kesma-article-video>
    <!--
    <nso-sport-radio
      (playingChange)="radioService.radioClick()"
      (volumeChange)="radioService.volumeChange($event)"
      [playing]="(radioService.isPlaying$ | async) || false"
    ></nso-sport-radio>
    -->
  </div>
  <div class="wrapper with-aside">
    <div class="left-column">
      <nso-article-header [data]="videoAsArticle" [showMeta]="true" [showLengthMeta]="true">
        <nso-social-buttons social [link]="buildArticleUrl(videoAsArticle)"></nso-social-buttons>
      </nso-article-header>
      <nso-block-title-row [data]="{ text: 'Kapcsolódó tartalmak' }"></nso-block-title-row>
      <nso-video-card *ngFor="let video of similarVideos" [data]="video" [styleID]="VideoCardType.FeaturedSidedImgColumnTitleLead"> </nso-video-card>
    </div>
    <aside>
      <app-sidebar
        *ngIf="videoAsArticle.slug && videoAsArticle.columnSlug"
        [articleId]="videoAsArticle.id"
        [articleSlug]="videoAsArticle.slug"
        [categorySlug]="videoAsArticle.columnSlug"
      >
      </app-sidebar>
    </aside>
  </div>
</section>
