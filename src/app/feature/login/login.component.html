<section class="login">
  <div class="wrapper">
    <!-- Login form -->
    <ng-container *ngIf="allowedLoginMethods$ | async as allowedLoginMethods">
      <form (ngSubmit)="login()" *ngIf="formGroup" [formGroup]="formGroup" class="nso-form">
        <h1>Bejelentkezés</h1>

        <div class="login-social-buttons">
          <nso-social-login-buttons
            (facebookClickEvent)="loginWithSocialProvider(SocialProvider.FACEBOOK)"
            (googleClickEvent)="loginWithSocialProvider(SocialProvider.GOOGLE)"
            [isFacebookAllowed]="allowedLoginMethods?.[SocialProvider.FACEBOOK]"
            [isGoogleAllowed]="allowedLoginMethods?.[SocialProvider.GOOGLE]"
          ></nso-social-login-buttons>
        </div>

        <div
          *ngIf="(allowedLoginMethods?.[SocialProvider.FACEBOOK] || allowedLoginMethods?.[SocialProvider.GOOGLE]) && allowedLoginMethods.email"
          class="login-social-text"
        >
          Vag<PERSON> be<PERSON> a következővel
        </div>

        <ng-container *ngIf="allowedLoginMethods.email">
          <div class="nso-form-row">
            <kesma-form-control>
              <input class="nso-form-input" formControlName="email" id="email" placeholder="E-mail cím" type="text" />
            </kesma-form-control>
          </div>
          <div class="nso-form-row">
            <kesma-form-control>
              <div class="nso-form-input-password">
                <input [type]="showPassword ? 'text' : 'password'" class="nso-form-input" formControlName="password" id="password" placeholder="Jelszó" />
                <img
                  (click)="showPassword = !showPassword"
                  [src]="showPassword ? '/assets/images/icons/icon-no-eye.svg' : '/assets/images/icons/icon-eye.svg'"
                  alt="Jelszó megtekintése"
                  class="nso-form-input-password-img"
                />
              </div>
            </kesma-form-control>
          </div>
          <div class="login-forgot-password">
            <a class="login-forgot-password-link" routerLink="/elfelejtett-jelszo">Elfelejtettem a jelszavam</a>
          </div>
          <div *ngIf="error" class="nso-form-general-error">
            {{ error }}
          </div>
          <div class="login-action">
            <nso-simple-button [disabled]="isLoading" [isSubmit]="true" class="w-100" round="round">
              {{ isLoading ? 'Kérem várjon...' : 'BELÉPÉS' }}
            </nso-simple-button>
          </div>
        </ng-container>
        <div class="login-no-profile">Még nincs fiókja? <a class="login-no-profile-link" routerLink="/regisztracio">Regisztráció</a></div>
      </form>
    </ng-container>
  </div>
</section>
