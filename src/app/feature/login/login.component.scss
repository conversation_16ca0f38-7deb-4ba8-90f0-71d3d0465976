@use 'shared' as *;

.login {
  padding: 100px 0;

  @include media-breakpoint-down(sm) {
    padding: 50px 15px;
  }

  .wrapper {
    width: 100%;
    max-width: 630px;
    border-radius: 10px;
    background-color: var(--kui-white);
    padding: 70px 80px 60px;

    @include media-breakpoint-down(sm) {
      padding: 45px 20px 30px;
    }
  }

  h1 {
    font-weight: bold;
    font-size: 38px;
    line-height: 42px;
    font-family: var(--kui-font-condensed);
    text-transform: uppercase;
    margin-bottom: 60px;
    text-align: center;
  }

  &-social-text {
    margin: 40px 0 30px;
    text-align: center;
    font-size: 16px;
    line-height: 20px;
  }

  &-forgot-password {
    font-size: 16px;
    margin: 30px 0 50px;
    text-align: center;

    &-link {
      color: var(--kui-gray-550);
      text-decoration: underline;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  &-no-profile {
    font-size: 16px;
    margin-top: 30px;
    text-align: center;

    &-link {
      color: var(--kui-gray-550);
      text-decoration: underline;

      &:hover {
        text-decoration: underline;
      }
    }
  }
}
