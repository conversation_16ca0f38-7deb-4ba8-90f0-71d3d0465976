import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, OnInit } from '@angular/core';
import { AbstractControl, ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { environment } from '../../../environments/environment';
import { ReCaptchaV3Service } from 'ngx-captcha';
import {
  ApiService,
  AuthService,
  BackendAllowedLoginMethodsResponse,
  createNSOTitle,
  defaultMetaInfo,
  NsoSimpleButtonComponent,
  NsoSocialLoginButtonsComponent,
  SocialProvider,
} from '../../shared';
import { IMetaData, SeoService, StorageService, UtilService } from '@trendency/kesma-core';
import { AsyncPipe, DOCUMENT, NgIf } from '@angular/common';
import { Observable } from 'rxjs';
import { createCanonicalUrlForPageablePage, KesmaFormControlComponent, passwordValidator } from '@trendency/kesma-ui';

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, AsyncPipe, ReactiveFormsModule, NsoSocialLoginButtonsComponent, KesmaFormControlComponent, NsoSimpleButtonComponent, RouterLink],
})
export class LoginComponent implements OnInit {
  formGroup: UntypedFormGroup;
  showPassword = false;
  isLoading = false;
  error: string | null = null;
  SocialProvider = SocialProvider;
  allowedLoginMethods$: Observable<BackendAllowedLoginMethodsResponse> = this.apiService.getAllowedLoginMethods();

  constructor(
    private readonly authService: AuthService,
    private readonly formBuilder: UntypedFormBuilder,
    private readonly router: Router,
    private readonly cdr: ChangeDetectorRef,
    private readonly reCaptchaV3Service: ReCaptchaV3Service,
    private readonly seo: SeoService,
    @Inject(DOCUMENT) private readonly document: Document,
    private readonly utilsService: UtilService,
    private readonly apiService: ApiService,
    private readonly route: ActivatedRoute,
    private readonly storage: StorageService
  ) {}

  ngOnInit(): void {
    this.initForm();
    this.setMetaData();
  }

  initForm(): void {
    this.formGroup = this.formBuilder.group({
      email: [null, [Validators.required]],
      password: [null, [Validators.required, passwordValidator]],
    });
  }

  login(): void {
    Object.values(this.formGroup.controls).forEach((control: AbstractControl) => {
      control.markAsTouched();
      control.updateValueAndValidity();
    });

    if (!this.formGroup.valid) {
      return;
    }

    this.error = null;
    this.isLoading = true;

    this.reCaptchaV3Service.execute(
      environment.googleSiteKey ?? '',
      'app_publicapi_portal_user_login',
      (recaptchaToken: string) => {
        this.authService.authenticate(this.formGroup.value, recaptchaToken).subscribe({
          next: () => {
            this.isLoading = false;
            this.cdr.detectChanges();

            const redirectUrlFromStorage = this.storage.getLocalStorageData('redirectUrl');
            const redirectUrl = this.route.snapshot.queryParams['redirect'] ?? redirectUrlFromStorage ?? '/';
            this.storage.removeLocalStorageData('redirectUrl');
            if (redirectUrl) {
              this.router.navigate([redirectUrl]);
            } else {
              this.router.navigate(['/']);
            }
          },
          error: (err) => {
            if (err?.error?.data?.message === 'Email not verified') {
              this.error = 'Kérjük, erősítse meg a regisztrációját az e-mail címben kapott link segítségével!';
            } else {
              this.error = 'Helytelen e-mail cím vagy jelszó.';
            }
            this.isLoading = false;
            this.cdr.detectChanges();
          },
        });
      },
      {
        useGlobalDomain: false,
      },
      () => {
        this.error = 'Captcha: Robot ellenőrzés hiba!';
        this.isLoading = false;
        this.cdr.detectChanges();
      }
    );
  }

  loginWithSocialProvider(provider: SocialProvider): void {
    if (this.utilsService.isBrowser()) {
      this.document.location.href = this.authService.getSocialProviderAuthUrl(provider);
    }
  }

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage('bejelentkezes');
    if (canonical) this.seo.updateCanonicalUrl(canonical);
    const title = createNSOTitle('Bejelentkezés');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
      robots: 'noindex, nofollow',
    };
    this.seo.setMetaData(metaData);
  }
}
