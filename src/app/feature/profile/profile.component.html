<section class="profile">
  <div class="breadcrumb-wrapper">
    <nso-breadcrumb [isShowLogout]="true" [items]="[{ label: 'Profilom', url: '/my-nso' }, { label: 'Profil beállítások módosítása' }]"></nso-breadcrumb>
  </div>

  <div class="wrapper">
    <!-- Profile form -->
    <form (ngSubmit)="editCurrentUser()" *ngIf="formGroup" [formGroup]="formGroup" class="nso-form">
      <h1>Profil</h1>
      <div class="nso-form-row">
        <kesma-form-control>
          <label class="nso-form-label" for="email">E-mail cím</label>
          <input class="nso-form-input" formControlName="email" id="email" placeholder="E-mail cím" type="text" />
        </kesma-form-control>
      </div>
      <div class="nso-form-row">
        <kesma-form-control>
          <label class="nso-form-label" for="username">Felhasználónév</label>
          <input class="nso-form-input" formControlName="username" id="username" placeholder="Felhasználónév " type="text" />
        </kesma-form-control>
        <small class="nso-form-small"
          >A felhasználónévnek legalább 6 karakterből kell állnia, és a következőket tartalmazhatja: kis- és nagybetű, szám, kötőjel, alsóvonás.</small
        >
      </div>
      <div class="nso-form-row">
        <kesma-form-control>
          <div class="nso-form-input-password">
            <label class="nso-form-label" for="newPassword">Jelszó módosítása</label>
            <input
              [type]="showNewPassword ? 'text' : 'password'"
              class="nso-form-input"
              formControlName="newPassword"
              id="newPassword"
              placeholder="Új jelszó"
            />
            <img
              (click)="showNewPassword = !showNewPassword"
              [src]="showNewPassword ? '/assets/images/icons/icon-no-eye.svg' : '/assets/images/icons/icon-eye.svg'"
              alt="Jelszó megtekintése"
              class="nso-form-input-password-img"
            />
          </div>
        </kesma-form-control>
        <nso-password-strength-meter
          [errorLabels]="errorLabels"
          [errorsToWatch]="errorsToWatch"
          [passwordInput]="passwordControl"
        ></nso-password-strength-meter>
      </div>
      <div class="nso-form-row">
        <kesma-form-control>
          <div class="nso-form-input-password">
            <input
              [type]="showOldPassword ? 'text' : 'password'"
              class="nso-form-input"
              formControlName="oldPassword"
              id="oldPassword"
              placeholder="Jelenlegi jelszó"
            />
            <img
              (click)="showOldPassword = !showOldPassword"
              [src]="showOldPassword ? '/assets/images/icons/icon-no-eye.svg' : '/assets/images/icons/icon-eye.svg'"
              alt="Jelszó megtekintése"
              class="nso-form-input-password-img"
            />
          </div>
        </kesma-form-control>
        <small class="nso-form-small">Az adatok változtatásához biztonsági okokból meg kell adnia a jelenleg használt jelszavát.</small>
      </div>
      <div class="profile-checkboxes">
        <kesma-form-control class="checkbox">
          <label class="nso-form-checkbox" for="newsletter">
            <input formControlName="newsletter" id="newsletter" type="checkbox" />
            <span>Feliratkozom az NSO hírlevélre</span>
          </label>
        </kesma-form-control>
      </div>
      <div class="profile-delete-container">
        <span class="profile-delete-container-title">Profil törlése</span>
        <p class="profile-delete-container-description">A profil törlésével véglegesen törli a kedvenceket és a profil adatokat.</p>
        <a class="profile-delete-container-link" routerLink="/profil/torles">Profil végleges törlése</a>
      </div>
      <div *ngIf="error" class="nso-form-general-error">
        {{ error }}
      </div>
      <div class="profile-action">
        <nso-simple-button [disabled]="isLoading" [isSubmit]="true" class="w-100" round="round">
          {{ isLoading ? 'Kérem várjon...' : 'VÁLTOZTATÁSOK MENTÉSE' }}
        </nso-simple-button>
      </div>
      <div class="profile-cancel">
        <button class="profile-cancel-link" (click)="resetUserForm()">Mégsem</button>
      </div>
    </form>
  </div>
</section>

<nso-popup
  (resultEvent)="handleSuccessPopupResult()"
  *ngIf="showSuccessPopup"
  [acceptButtonLabel]="'TOVÁBB A FŐOLDALRA'"
  [showCancelButton]="false"
  [title]="'Sikeres adatmódosítás'"
>
  A módosítás sikeres. További kellemes olvasást kívánunk!
</nso-popup>
