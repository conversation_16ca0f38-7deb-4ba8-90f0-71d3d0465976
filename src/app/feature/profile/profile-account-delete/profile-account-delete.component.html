<section class="profile">
  <div class="breadcrumb-wrapper">
    <nso-breadcrumb [isShowLogout]="true" [items]="[{ label: 'Profilom', url: '/my-nso' }, { label: 'Profil törlése' }]"></nso-breadcrumb>
  </div>

  <div class="wrapper">
    <!-- Profile account delete form -->
    <form (ngSubmit)="submit()" *ngIf="formGroup" [formGroup]="formGroup" class="profile-settings-form">
      <img alt="Profil törlése" class="delete-icon" src="/assets/images/icons/icon-bin.svg" />
      <h1>Profil törlése</h1>
      <div class="profile-gray-text">A profil törlésével véglegesen törli a kedvenceket és a profil adatokat.</div>
      <div class="nso-form-row">
        <kesma-form-control>
          <div class="nso-form-input-password">
            <input
              [type]="showOldPassword ? 'text' : 'password'"
              class="nso-form-input"
              formControlName="oldPassword"
              id="oldPassword"
              placeholder="Jelenlegi jelszó"
            />
            <img
              (click)="showOldPassword = !showOldPassword"
              [src]="showOldPassword ? '/assets/images/icons/icon-no-eye.svg' : '/assets/images/icons/icon-eye.svg'"
              alt="Jelszó megtekintése"
              class="nso-form-input-password-img"
            />
          </div>
        </kesma-form-control>
        <small class="nso-form-small">Az adatok változtatásához biztonsági okokból meg kell adnia a jelenleg használt jelszavát.</small>
      </div>
      <div *ngIf="error" class="nso-form-general-error">
        {{ error }}
      </div>
      <div class="profile-action">
        <nso-simple-button [disabled]="isLoading" [isSubmit]="true" class="w-100" round="round">
          {{ isLoading ? 'Kérem várjon...' : 'PROFIL VÉGLEGES TÖRLÉSE' }}
        </nso-simple-button>
      </div>
      <div class="profile-delete">
        <a class="profile-delete-link" routerLink="/profil">Mégsem</a>
      </div>
    </form>
  </div>
</section>

<nso-popup
  (resultEvent)="onConfirmUserDelete($event)"
  *ngIf="showPopup"
  [acceptButtonLabel]="acceptButtonLabel"
  [cancelButtonLabel]="cancelButtonLabel"
  [showAcceptButton]="showAcceptButton"
  [showCancelButton]="showCancelButton"
  [title]="popupTitle"
>
  {{ popupText }}
</nso-popup>
