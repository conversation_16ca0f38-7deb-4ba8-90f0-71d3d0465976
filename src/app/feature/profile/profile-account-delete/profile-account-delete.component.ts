import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import {
  AuthService,
  createNSOTitle,
  defaultMetaInfo,
  NsoBreadcrumbComponent,
  NsoPopupComponent,
  NsoSimpleButtonComponent,
  SecureApiService,
} from '../../../shared';
import { ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { HttpErrorResponse } from '@angular/common/http';
import { Router, RouterLink } from '@angular/router';
import { catchError, switchMap } from 'rxjs/operators';
import { throwError } from 'rxjs';
import { BackendFormErrors, createCanonicalUrlForPageablePage, KesmaFormControlComponent, markControlsTouched, passwordValidator } from '@trendency/kesma-ui';
import { NgIf } from '@angular/common';

@Component({
  selector: 'app-profile-account-delete',
  templateUrl: './profile-account-delete.component.html',
  styleUrls: ['./profile-account-delete.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NsoBreadcrumbComponent, ReactiveFormsModule, NgIf, KesmaFormControlComponent, NsoSimpleButtonComponent, RouterLink, NsoPopupComponent],
})
export class ProfileAccountDeleteComponent implements OnInit {
  formGroup: UntypedFormGroup;
  showPopup = false;
  acceptButtonLabel = 'PROFIL TÖRLÉSE';
  cancelButtonLabel = 'MÉGSEM';
  popupTitle = 'Profil törlése';
  popupText = 'Biztos abban, hogy törli NSO profilját és az összes személyes adatát?';
  showAcceptButton = true;
  showCancelButton = true;
  showOldPassword = false;
  isLoading = false;
  error: string | null = null;

  constructor(
    private readonly seo: SeoService,
    private readonly formBuilder: UntypedFormBuilder,
    private readonly secureApiService: SecureApiService,
    private readonly cdr: ChangeDetectorRef,
    private readonly router: Router,
    private readonly authService: AuthService
  ) {}

  ngOnInit(): void {
    this.initForm();
    this.setMetaData();
  }

  initForm(): void {
    this.formGroup = this.formBuilder.group({
      oldPassword: [null, [Validators.required, passwordValidator]],
    });
  }

  submit(): void {
    if (this.formGroup) {
      markControlsTouched(this.formGroup);
    }

    if (!this.formGroup.valid) {
      return;
    }
    this.showPopup = true;
  }

  onConfirmUserDelete(popupResult: boolean): void {
    this.acceptButtonLabel = 'PROFIL TÖRLÉSE';
    this.cancelButtonLabel = 'MÉGSEM';
    this.popupTitle = 'Profil törlése';
    this.popupText = 'Biztos abban, hogy törli NSO profilját és az összes személyes adatát?';
    this.showAcceptButton = true;
    this.showCancelButton = true;

    this.showPopup = false;

    if (this.formGroup) {
      markControlsTouched(this.formGroup);
    }

    if (!this.formGroup.valid || !popupResult) {
      return;
    }

    this.error = null;
    this.isLoading = true;

    this.secureApiService
      .deleteAccount(this.formGroup.value.oldPassword)
      .pipe(
        catchError((response: HttpErrorResponse) => {
          const backendErrors = response.error as BackendFormErrors;
          let isErrorHandled = false;
          if (backendErrors?.form?.errors?.children) {
            for (const [errorKey, value] of Object.entries(backendErrors.form.errors.children)) {
              // User old password is not correct
              if (errorKey === 'password' && !!value.errors) {
                this.formGroup.get('oldPassword')?.setErrors({ invalidOldPassword: true });
                isErrorHandled = true;
              }
            }
          }
          if (!isErrorHandled) {
            this.error = 'Ismeretlen hiba!';
          }
          this.isLoading = false;
          this.cdr.detectChanges();
          return throwError(() => 'Error');
        }),
        switchMap(() => this.authService.invalidate())
      )
      .subscribe(() => {
        this.showSuccesDeletePopup();
      });
  }

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage('profil/torles');
    if (canonical) this.seo.updateCanonicalUrl(canonical);
    const title = createNSOTitle('Profil törlése');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
      robots: 'noindex, nofollow',
    };
    this.seo.setMetaData(metaData);
  }

  private showSuccesDeletePopup(): void {
    this.popupTitle = 'SIKERESEN TÖRÖLTE A PROFILJÁT!';
    this.popupText = '';
    this.showAcceptButton = false;
    this.showCancelButton = false;
    this.showPopup = true;
    this.cdr.markForCheck();
    setTimeout(() => {
      this.showPopup = false;
      this.router.navigate(['/']);
    }, 3000);
  }
}
