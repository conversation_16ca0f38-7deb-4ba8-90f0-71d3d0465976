@use 'shared' as *;

.profile {
  padding: 0 0 100px 0;

  @include media-breakpoint-down(sm) {
    padding: 15px 15px 50px;
  }

  .breadcrumb-wrapper {
    padding: 30px 0;
    background-color: var(--kui-white);
    margin-bottom: 100px;

    @include media-breakpoint-down(sm) {
      margin-bottom: 15px;
      border-radius: 10px;
    }

    nso-breadcrumb {
      margin: 0 auto;
      width: $header-max-width;
      max-width: calc(100% - 30px);
    }
  }

  .wrapper {
    width: 100%;
    max-width: 630px;
    border-radius: 10px;
    background-color: var(--kui-white);
    padding: 70px 80px 60px;

    @include media-breakpoint-down(sm) {
      padding: 50px 25px 30px;
    }
  }

  h1 {
    font-weight: bold;
    font-size: 38px;
    line-height: 42px;
    font-family: var(--kui-font-condensed);
    text-transform: uppercase;
    margin-bottom: 60px;
    text-align: center;
  }

  &-checkboxes {
    margin: 40px 0;
  }

  &-cancel {
    font-size: 16px;
    margin-top: 20px;
    text-align: center;

    &-link {
      font-family: var(--kui-font-primary);
      color: var(--kui-gray-550);
      text-decoration: underline;
      font-size: 16px;
      font-weight: 400;
      line-height: 160%;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  .profile-delete-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
    font-family: var(--kui-font-primary);
    margin-bottom: 40px;

    &-title {
      @extend %delete-container-appearance;
    }

    &-description {
      color: var(--kui-gray-275);
      @extend %delete-container-appearance;
    }

    &-link {
      color: var(--kui-red-400);
      @extend %delete-container-appearance;
    }
  }

  &-gray-text {
    font-size: 16px;
    line-height: 26px;
    color: var(--kui-gray-350);
    margin-bottom: 35px;
    text-align: center;
  }

  %delete-container-appearance {
    font-size: 16px;
    font-weight: 400;
    line-height: 160%;
  }
}
