import { HttpErrorResponse } from '@angular/common/http';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import {
  AuthService,
  BackendUserResponse,
  createNSOTitle,
  defaultMetaInfo,
  NsoBreadcrumbComponent,
  NsoPasswordStrengthMeterComponent,
  NsoPopupComponent,
  NsoSimpleButtonComponent,
  SecureApiService,
} from '../../shared';
import { AbstractControl, FormControl, ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { Router, RouterLink } from '@angular/router';
import {
  BackendFormErrors,
  createCanonicalUrlForPageablePage,
  KesmaFormControlComponent,
  passwordContainsCharsAndNumbers,
  passwordContainsSpecial,
  passwordValidator,
  User,
  usernameValidator,
} from '@trendency/kesma-ui';
import { throwError } from 'rxjs';
import { catchError, switchMap } from 'rxjs/operators';
import { errorLabels, errorsToWatch } from 'src/app/shared/constants/formErrorLabels.consts';
import { NgIf } from '@angular/common';

@Component({
  selector: 'app-profile',
  templateUrl: './profile.component.html',
  styleUrls: ['./profile.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NsoBreadcrumbComponent,
    ReactiveFormsModule,
    NgIf,
    KesmaFormControlComponent,
    NsoPasswordStrengthMeterComponent,
    RouterLink,
    NsoSimpleButtonComponent,
    NsoPopupComponent,
  ],
})
export class ProfileComponent implements OnInit {
  formGroup: UntypedFormGroup;
  showNewPassword = false;
  showOldPassword = false;
  showSuccessPopup = false;
  isLoading = false;
  error: string | null = null;
  errorsToWatch = errorsToWatch;
  errorLabels = errorLabels;
  userDataCopy: User | null;

  constructor(
    private readonly seo: SeoService,
    private readonly formBuilder: UntypedFormBuilder,
    private readonly secureApiService: SecureApiService,
    private readonly cdr: ChangeDetectorRef,
    private readonly router: Router,
    private readonly authService: AuthService
  ) {}

  get user(): User | undefined {
    return this.authService.currentUser;
  }

  ngOnInit(): void {
    this.initForm();
    this.setMetaData();
  }

  initForm(): void {
    this.userDataCopy = this.user ?? null;
    this.formGroup = this.formBuilder.group({
      email: [{ value: this.user?.email, disabled: true }],
      username: [this.user?.username ?? null, [Validators.required, usernameValidator]],
      newPassword: [null, [Validators.minLength(6), passwordContainsSpecial, passwordContainsCharsAndNumbers]],
      oldPassword: [null, [Validators.required, passwordValidator]],
      newsletter: [!!this.user?.newsletter],
    });
  }

  editCurrentUser(): void {
    Object.values(this.formGroup.controls).forEach((control: AbstractControl) => {
      control.markAsTouched();
      control.updateValueAndValidity();
    });

    if (!this.formGroup.valid) {
      return;
    }

    this.error = null;
    this.isLoading = true;

    this.secureApiService
      .editCurrentUser(this.formGroup.value)
      .pipe(
        catchError((response: HttpErrorResponse) => {
          const backendErrors = response.error as BackendFormErrors;
          let isErrorHandled = false;
          if (backendErrors?.form?.errors?.children) {
            for (const [errorKey, value] of Object.entries(backendErrors.form.errors.children)) {
              // User with the same username is already registered
              if (errorKey === 'userName' && !!value.errors) {
                this.formGroup.get('username')?.setErrors({ usernameInUse: true });
                isErrorHandled = true;
              }
              // User old password is not correct
              if (errorKey === 'oldPassword' && !!value.errors) {
                this.formGroup.get('oldPassword')?.setErrors({ invalidOldPassword: true });
                isErrorHandled = true;
              }
            }
          }
          if (!isErrorHandled) {
            this.error = 'Ismeretlen hiba!';
          }
          this.isLoading = false;
          this.cdr.detectChanges();
          return throwError(() => 'Error');
        }),
        switchMap(() => this.secureApiService.getCurrentUser())
      )
      .subscribe((backendUserResponse: BackendUserResponse) => {
        this.authService.currentUserSubject.next(this.authService.mapBackendUserResponseToUser(backendUserResponse));
        this.showSuccessPopup = true;
        this.cdr.detectChanges();
      });
  }

  get passwordControl(): FormControl {
    return this.formGroup.get('newPassword') as FormControl;
  }

  handleSuccessPopupResult(): void {
    this.showSuccessPopup = false;
    this.router.navigate(['/']);
  }

  resetUserForm(): void {
    this.formGroup.patchValue({
      ...this.userDataCopy,
      newPassword: null,
      oldPassword: null,
    });
    this.router.navigate(['/', 'my-nso']);
  }

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage('profil');
    if (canonical) this.seo.updateCanonicalUrl(canonical);
    const title = createNSOTitle('Profil');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
      robots: 'noindex, nofollow',
    };
    this.seo.setMetaData(metaData);
  }
}
