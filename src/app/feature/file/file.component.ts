import { HttpClient, HttpEvent, HttpEventType, HttpHeaders } from '@angular/common/http';
import { ChangeDetectionStrategy, Component, inject, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { catchError, Observable, of } from 'rxjs';
import { environment } from 'src/environments/environment';
import { IMetaData, SeoService, UtilService } from '@trendency/kesma-core';
import { defaultMetaInfo } from '../../shared';
import { DOCUMENT } from '@angular/common';

@Component({
  selector: 'app-file',
  templateUrl: './file.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FileComponent implements OnInit {
  fileId = '';
  fileName = '';

  private readonly document: Document = inject(DOCUMENT);

  constructor(
    private readonly route: ActivatedRoute,
    private readonly router: Router,
    private readonly http: HttpClient,
    private readonly utils: UtilService,
    private readonly seo: SeoService
  ) {}

  ngOnInit(): void {
    const fileId: string = this.route.snapshot.paramMap.get('fileId') || '';
    const fileName: string = this.route.snapshot.paramMap.get('fileName') || '';

    if (Boolean(fileId) && Boolean(fileName)) {
      this.downloadFile(this.getUri(fileId), fileName);
      this.fileId = fileId;
      this.fileName = fileName;
    }
    this.setMetaData();
  }

  handleClick(e: MouseEvent): void {
    e.preventDefault();
    this.downloadFile(this.getUri(this.fileId), this.fileName);
  }

  private getUri(fileId: string): string {
    const apiUri = typeof environment.apiUrl === 'string' ? environment.apiUrl : environment.apiUrl?.clientApiUrl;
    return `${apiUri}/media/file/stream/${fileId}`;
  }

  private getFile(uri: string): Observable<HttpEvent<Blob> | undefined> {
    return this.http
      .get(uri, {
        headers: new HttpHeaders().set('portal', 'nso'),
        observe: 'events',
        reportProgress: true,
        responseType: 'blob',
      })
      .pipe(
        catchError((err) => {
          console.error('Failed to download file: ', err);
          this.router.navigate(['/404']);
          return of(undefined);
        })
      );
  }

  private downloadFile(uri: string, fileName: string): void {
    if (!this.utils.isBrowser()) {
      return;
    }
    this.getFile(uri).subscribe((x) => {
      if (!x) {
        return;
      }
      // It is necessary to create a new blob object with mime-type explicitly set
      // otherwise only Chrome works like it should
      if (x.type === HttpEventType.Response) {
        const contentType = x.headers.get('content-type') || 'application/pdf';
        const newBlob = new Blob([x.body as BlobPart], { type: contentType });

        // Create a link pointing to the ObjectURL containing the blob.
        const data = window.URL.createObjectURL(newBlob);

        const link = this.document.createElement('a');
        link.href = data;
        link.download = fileName;
        // this is necessary as link.click() does not work on the latest firefox
        link.dispatchEvent(new MouseEvent('click', { bubbles: true, cancelable: true, view: window }));

        setTimeout(function () {
          // For Firefox it is necessary to delay revoking the ObjectURL
          window.URL.revokeObjectURL(data);
          link.remove();
        }, 100);
      }
    });
  }

  private setMetaData(): void {
    const title = 'Fájl letöltés - ' + defaultMetaInfo.ogSiteName;

    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    };
    this.seo.setMetaData(metaData);
  }
}
