import { Routes } from '@angular/router';
import { AuthorPageResolver } from './api/author-page.resolver';
import { AuthorPageComponent } from './components/author-page/author-page.component';
import { PageValidatorGuard } from '@trendency/kesma-ui';

export const authorRoutes: Routes = [
  {
    path: ':authorSlug',
    runGuardsAndResolvers: 'paramsOrQueryParamsChange',
    component: AuthorPageComponent,
    canActivate: [PageValidatorGuard],
    resolve: {
      data: AuthorPageResolver,
    },
    providers: [AuthorPageResolver],
  },
];
