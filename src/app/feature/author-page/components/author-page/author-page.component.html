<section>
  <div class="wrapper">
    <nso-author-info *ngIf="author" [data]="author"></nso-author-info>

    <span class="divider"></span>

    <ng-container *ngIf="articles">
      <ng-container *ngFor="let article of articles">
        <nso-article-card [styleID]="articleCardType" [data]="article"> </nso-article-card>
      </ng-container>
    </ng-container>

    <nso-pager
      *ngIf="limitable?.pageMax! > 0"
      [rowAllCount]="limitable?.rowAllCount!"
      [rowOnPageCount]="limitable?.rowOnPageCount!"
      [hasFirstLastButton]="true"
      [showTotalPagesPositionAtRight]="true"
      [isCountPager]="true"
    >
    </nso-pager>
  </div>
</section>
