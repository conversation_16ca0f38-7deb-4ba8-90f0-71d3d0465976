import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { IMetaData, SchemaOrgService, SeoService } from '@trendency/kesma-core';
import { takeUntil } from 'rxjs/operators';
import { Observable, Subject } from 'rxjs';
import {
  ArticleAuthor,
  ArticleCard,
  createCanonicalUrlForPageablePage,
  getStructuredDataForProfilePage,
  LimitableMeta,
  mapBackendArticleDataToArticleCard,
} from '@trendency/kesma-ui';
import { AuthorData, AuthorPage } from '../../api/author-page.definitions';
import { ArticleCardType, createNSOTitle, defaultMetaInfo, NsoArticleCardComponent, NsoAuthorInfoComponent, NsoPagerComponent } from '../../../../shared';
import { NgForOf, NgIf } from '@angular/common';
import { environment } from '../../../../../environments/environment';

@Component({
  selector: 'app-author-page-page',
  templateUrl: './author-page.component.html',
  styleUrls: ['./author-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NsoAuthorInfoComponent, NgIf, NgForOf, NsoPagerComponent, NsoArticleCardComponent],
})
export class AuthorPageComponent implements OnInit, OnDestroy {
  articles?: ArticleCard[];
  author?: ArticleAuthor | null;
  limitable?: LimitableMeta;
  rowAllCount = 0;

  private readonly destroy$: Subject<boolean> = new Subject();

  public readonly articleCardType = ArticleCardType.FeaturedSidedImgColumnTitleLead;

  constructor(
    private readonly activatedRoute: ActivatedRoute,
    private readonly seoService: SeoService,
    private readonly cdr: ChangeDetectorRef,
    private readonly schemaService: SchemaOrgService
  ) {}

  ngOnInit(): void {
    (this.activatedRoute.data as Observable<{ data: AuthorPage }>).pipe(takeUntil(this.destroy$)).subscribe(({ data: { articles, author } }) => {
      this.articles = articles?.data?.map(mapBackendArticleDataToArticleCard);
      this.limitable = articles?.meta?.limitable;
      this.rowAllCount = this.limitable?.rowAllCount as number;
      this.author = this.mapAuthorDataToArticleAuthor(author?.data);

      this.schemaService.removeStructuredData();
      this.schemaService.insertSchema(getStructuredDataForProfilePage(author?.data as any, environment?.siteUrl ?? ''));

      this.setMetaData(this.author?.name as string);
      this.cdr.markForCheck();
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next(true);
    this.destroy$.complete();
  }

  private setMetaData(authorName: string): void {
    if (!authorName) {
      return;
    }

    const canonical = createCanonicalUrlForPageablePage('szerzo', this.activatedRoute.snapshot);
    canonical && this.seoService.updateCanonicalUrl(canonical);
    const title = createNSOTitle(`${authorName} cikkei`);
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    };
    this.seoService.setMetaData(metaData);
  }

  private mapAuthorDataToArticleAuthor(data: AuthorData): ArticleAuthor | null {
    if (!data?.publicAuthorName) {
      return null;
    }

    return {
      name: data.publicAuthorName,
      description: data.publicAuthorDescription,
      facebook: data.facebook,
      instagram: data.instagram,
      tiktok: data.tiktok,
      avatarUrl: data.avatar.fullSizeUrl || '/assets/images/nemzetisport.png',
      avatar: data.avatar.fullSizeUrl,
      rank: data.rank,
      count: this.limitable?.rowAllCount,
    };
  }
}
