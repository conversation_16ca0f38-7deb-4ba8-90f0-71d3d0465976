import { ApiResponseMetaList, ApiResult } from '@trendency/kesma-ui';
import { BackendArticleSearchResult } from '@trendency/kesma-ui/lib/definitions';

export type AuthorData = Readonly<{
  readonly facebook: string;
  readonly instagram: string;
  readonly publicAuthorDescription: string;
  readonly publicAuthorName: string;
  readonly tiktok: string;
  readonly avatar: AuthorDataAvatar;
  readonly rank?: string;
  readonly count?: number;
  readonly id?: string;
}>;

export type AuthorDataAvatar = Readonly<{
  readonly fullSizeUrl: string;
  readonly thumbnailUrl: string;
  readonly variantId: number;
  readonly altText?: string;
}>;

export type AuthorPage = Readonly<{
  articles: ApiResult<BackendArticleSearchResult[], ApiResponseMetaList>;
  author: ApiResult<AuthorData>;
}>;
