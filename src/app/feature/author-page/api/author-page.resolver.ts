import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { Observable, throwError } from 'rxjs';
import { Injectable } from '@angular/core';
import { RedirectService, SearchQuery } from '@trendency/kesma-ui';
import { catchError, map, switchMap, tap } from 'rxjs/operators';
import { ApiService } from '../../../shared';

@Injectable()
export class AuthorPageResolver {
  constructor(
    private readonly apiService: ApiService,
    private readonly router: Router,
    private readonly redirectService: RedirectService
  ) {}

  public resolve(route: ActivatedRouteSnapshot): Observable<any> {
    const slug = route.params['authorSlug'];
    const pageIndex = route.queryParams['page'] ? route.queryParams['page'] - 1 : 0;

    return this.apiService.getAuthorFromPublicAuthor(slug).pipe(
      switchMap((author) => {
        const searchQuery = { 'author[]': author?.data?.id } as SearchQuery;
        const name = author?.data?.publicAuthorName;
        !name && this.router.navigate(['/', '404']).then();
        return this.apiService.getSearch(searchQuery, pageIndex).pipe(
          map((articles) => ({
            author,
            articles,
          })),
          tap(({ articles }) => {
            if (this.redirectService.shouldBeRedirect(pageIndex, articles?.data)) {
              this.redirectService.redirectOldUrl(`szerzo/${slug}`, false, 302);
            }
          }),
          catchError((error) => {
            this.router
              .navigate(['/', '404'], {
                state: { errorResponse: JSON.stringify(error) },
                skipLocationChange: true,
              })
              .then();
            return throwError(() => error);
          })
        );
      })
    );
  }
}
