import { Routes } from '@angular/router';
import { ArticleCommentsComponent } from '../article-page/components/article-comments/article-comments.component';
import { ArticleCommentsByIdResolver } from '../article-page/api/article-comments-by-id.resolver';

export const articlePageNsoAppRoutes: Routes = [
  {
    path: '',
    component: ArticleCommentsComponent,
    resolve: {
      data: ArticleCommentsByIdResolver,
    },
    providers: [ArticleCommentsByIdResolver],
  },
];
