import { Routes } from '@angular/router';
import { LayoutEditorComponent } from './layout-editor.component';
import { HomeResolver } from '../home/<USER>';

export const layoutEditorRoutes: Routes = [
  {
    path: '',
    pathMatch: 'full',
    component: LayoutEditorComponent,
    resolve: {
      layoutData: HomeResolver,
    },
    providers: [HomeResolver],
    data: { skipSsrConditionalElements: true },
  },
];
