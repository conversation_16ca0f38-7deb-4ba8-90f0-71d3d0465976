import { ChangeDetectionStrategy, Component, inject, OnInit } from '@angular/core';
import { AdvertisementPlaceholderComponent, LayoutEditorComponent as KesmaLayoutEditorComponent, LayoutElementContentType } from '@trendency/kesma-ui';
import { SeoService, UtilService } from '@trendency/kesma-core';
import { LayoutComponent } from '../layout/components/layout/layout.component';
import { NgForOf, NgSwitch, NgSwitchCase, NgSwitchDefault, NgTemplateOutlet } from '@angular/common';

@Component({
  selector: 'app-layout-editor',
  templateUrl: './layout-editor.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [KesmaLayoutEditorComponent, LayoutComponent, NgSwitch, NgSwitchCase, AdvertisementPlaceholderComponent, NgSwitchDefault, NgTemplateOutlet, NgForOf],
})
export class LayoutEditorComponent implements OnInit {
  readonly utilService = inject(UtilService);
  readonly seoService = inject(SeoService);

  readonly isBrowser = this.utilService.isBrowser();
  readonly LayoutElementContentType = LayoutElementContentType;

  getIteratorArray(length: number): number[] {
    const arr = [];
    for (let i = 0; i < length; i++) {
      arr.push(i);
    }
    return arr;
  }

  ngOnInit(): void {
    this.seoService.setMetaData({
      robots: 'noindex, nofollow',
    });
  }
}
