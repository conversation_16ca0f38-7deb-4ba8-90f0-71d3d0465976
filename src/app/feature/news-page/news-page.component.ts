import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import { ArticleCard, createCanonicalUrlForPageablePage, LimitableMeta } from '@trendency/kesma-ui';
import { Observable, Subject } from 'rxjs';
import { map, share, takeUntil, tap } from 'rxjs/operators';
import { ArticleCardType, createNSOTitle, defaultMetaInfo, NsoArticleCardComponent, NsoPagerComponent, RadioService } from '../../shared';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { AsyncPipe, NgForOf, NgIf } from '@angular/common';
import { NewsMenuComponent } from './components/news-menu/news-menu.component';
import { SidebarComponent } from '../layout/components/sidebar/sidebar.component';

@Component({
  selector: 'app-nso-news-page',
  templateUrl: './news-page.component.html',
  styleUrls: ['./news-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, AsyncPipe, NewsMenuComponent, NgForOf, NsoArticleCardComponent, NsoPagerComponent, SidebarComponent],
})
export class NewsPageComponent implements OnInit, OnDestroy {
  readonly ArticleCardType = ArticleCardType;

  private readonly unsubscribe$: Subject<boolean> = new Subject();

  articles$: Observable<ArticleCard[]> = this.route.data.pipe(map((res) => res?.['data']?.articles));

  limitables$: Observable<LimitableMeta> = this.route.data.pipe(map((res) => res['data']?.limitable));

  sidebarExcludedIds: Array<string> = [];

  showCompletePage$: Observable<boolean> = this.route.data.pipe(
    map((data) => data['isMobileApp']),
    map((onlyBody) => !onlyBody),
    share()
  );
  get showCompletePage(): boolean {
    return !this.route.snapshot.data?.['isMobileApp'];
  }
  page = 0;

  isMenuReady = false;

  MOBILE_BREAKPOINT = '(max-width: 768px)';

  isMobile = false;
  isMobile$ = this.breakpointObserver.observe([this.MOBILE_BREAKPOINT]).pipe(
    tap((state: BreakpointState) => {
      this.isMobile = state.matches;
    })
  );

  constructor(
    private readonly cdr: ChangeDetectorRef,
    private readonly route: ActivatedRoute,
    private readonly router: Router,
    private readonly seo: SeoService,
    public readonly radioService: RadioService,
    private readonly breakpointObserver: BreakpointObserver
  ) {}

  ngOnInit(): void {
    this.initAds();
    this.subscribeToResolverDataChange();
  }

  subscribeToResolverDataChange(): void {
    this.route.data.pipe(takeUntil(this.unsubscribe$)).subscribe((res) => {
      this.setPageMeta();
      this.populateSidebarExcludedIds(res?.['data']['articles']);
      this.isMenuReady = true;
      this.cdr.detectChanges();
    });
  }

  // eslint-disable-next-line @typescript-eslint/no-empty-function
  initAds(): void {}

  setPageMeta(): void {
    const title = createNSOTitle('Hírek');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
      robots: 'noindex, nofollow',
    };
    this.seo.setMetaData(metaData);
    const canonical = createCanonicalUrlForPageablePage('hirek');
    if (canonical) {
      this.seo.updateCanonicalUrl(canonical);
    }
  }

  populateSidebarExcludedIds(articles: ArticleCard[]): void {
    this.sidebarExcludedIds = articles?.map((item) => item.id!) ?? [];
  }

  onDateChange(date: Date): void {
    this.isMenuReady = false;
    this.setUrl(date);
  }

  private setUrl(date: Date): void {
    const pageIndex = { page: this.page >= 0 ? this.page + 1 : null };
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const year = date.getFullYear();
    const dateStr = `${year}-${month}-${day}`;
    this.router
      .navigate([], {
        relativeTo: this.route,
        queryParams: { date: dateStr, ...pageIndex },
      })
      .then(() => {
        this.isMenuReady = true;
      });
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next(true);
    this.unsubscribe$.complete();
  }
}
