@use 'shared' as *;

.news-page {
  &-head {
    display: flex;
    justify-content: flex-start;
    flex-direction: column;
    gap: 40px;
    padding-top: 40px;
  }
}

section {
  &.mobile-app-news-page {
    margin: 15px;

    .wrapper {
      width: 100%;
      max-width: none;
    }
  }
}

.left-column {
  width: calc(100% - 324px); // 300px sidebar + gap
}

.article-list {
  margin-top: 9px;

  .article-card {
    &.style-FeaturedTitleArrowDate {
      @include media-breakpoint-down(md) {
        margin-bottom: 0;
      }
    }
  }
}

.article-divider {
  height: 1px;
  margin: 40px 0;
  background-color: var(--kui-gray-275);

  @include media-breakpoint-down(md) {
    margin: 24px 0;
  }
}
aside {
  @include media-breakpoint-down(sm) {
    margin-top: 20px;
  }
}
