<ng-container *ngIf="isMobile$ | async"></ng-container>
<section class="news-page" [class.mobile-app-news-page]="!showCompletePage">
  <div class="news-page-head wrapper">
    <!--
    <nso-sport-radio
      [playing]="(radioService.isPlaying$ | async) || false"
      (playingChange)="radioService.radioClick()"
      (volumeChange)="radioService.volumeChange($event)">
    </nso-sport-radio>
    -->
    <app-nso-news-menu [canChangeDate]="isMenuReady" (dateChange)="onDateChange($event)"></app-nso-news-menu>
  </div>
  <div class="wrapper" [class.with-aside]="showCompletePage">
    <div [class.left-column]="showCompletePage" *ngIf="articles$ | async as articles">
      <div class="article-list">
        <ng-container *ngFor="let article of articles; let i = index; let last = last">
          <nso-article-card
            nso-article-card
            class="article-card"
            [data]="article"
            [styleID]="isMobile ? ArticleCardType.FeaturedTitleArrowDate : ArticleCardType.FeaturedSidedImgColumnTitleLead"
          ></nso-article-card>
          <div *ngIf="!last" class="article-divider"></div>
        </ng-container>
      </div>
      <ng-container *ngIf="limitables$ | async as limitable">
        <nso-pager
          *ngIf="limitable?.pageMax! > 0"
          [rowAllCount]="limitable.rowAllCount!"
          [rowOnPageCount]="limitable.rowOnPageCount!"
          [hasFirstLastButton]="true"
          [showTotalPagesPositionAtRight]="true"
          [isCountPager]="true"
        >
        </nso-pager>
      </ng-container>
    </div>
    <aside *ngIf="!isMobile && showCompletePage">
      <app-sidebar [excludedIds]="sidebarExcludedIds"> </app-sidebar>
    </aside>
  </div>
</section>
