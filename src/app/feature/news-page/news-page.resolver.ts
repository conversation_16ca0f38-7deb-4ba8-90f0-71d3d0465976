import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot } from '@angular/router';
import { map, Observable, of } from 'rxjs';
import { ApiService, backendArticlesSearchResultsToArticleSearchResArticles, searchResultToArticleCard } from '../../shared';
import { NewsPageData } from './news-page.definitions';
import { addDays, isToday } from 'date-fns';
import { switchMap, tap } from 'rxjs/operators';
import { RedirectService } from '@trendency/kesma-ui';

const MAX_RESULTS_PER_PAGE = 50;

@Injectable()
export class NewsPageResolver {
  constructor(
    private readonly apiService: ApiService,
    private readonly redirectService: RedirectService
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<NewsPageData> {
    const currentPage = route.queryParams['page'] ? parseInt(route.queryParams['page'], 10) - 1 : 0;
    const today = new Date().toISOString().split('T')[0];
    const queryDate = route.queryParams['date'] ?? today;
    const params = {
      isHiddenOnLayout: '1',
    };
    return this.apiService.searchByDate(queryDate, queryDate, currentPage, MAX_RESULTS_PER_PAGE, params).pipe(
      switchMap((res) => {
        const numberOfArticlesToday = res.data?.length || 0;
        if (numberOfArticlesToday < MAX_RESULTS_PER_PAGE && isToday(new Date(queryDate))) {
          const yesterday = addDays(new Date(), -1).toISOString().split('T')[0];
          return this.apiService.searchByDate(yesterday, yesterday, 0, MAX_RESULTS_PER_PAGE - numberOfArticlesToday).pipe(
            map((yesterdayRes) => {
              return {
                meta: res?.meta,
                data: [...(res?.data as []), ...(yesterdayRes?.data as [])],
              };
            })
          );
        }
        return of(res);
      }),
      map(({ data, meta }) => ({
        articles: data.map(backendArticlesSearchResultsToArticleSearchResArticles).map(searchResultToArticleCard),
        limitable: meta.limitable,
      })),
      tap(({ articles }) => {
        if (this.redirectService.shouldBeRedirect(currentPage, articles)) {
          this.redirectService.redirectOldUrl(`nso-hirek${queryDate ? `?date=${queryDate}` : ''}`, false, 302);
        }
      })
    );
  }
}
