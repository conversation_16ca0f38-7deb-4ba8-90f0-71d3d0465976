import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';
import { NgForOf } from '@angular/common';

const dayLabels = ['Vas<PERSON>rnap', '<PERSON><PERSON>tfő', 'Kedd', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>'];
const todayLabel = 'Ma';
const MENU_ITEM_COUNT = 6;
type MenuItem = { date: Date; label: string };

@Component({
  selector: 'app-nso-news-menu',
  templateUrl: './news-menu.component.html',
  styleUrls: ['./news-menu.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgForOf],
})
export class NewsMenuComponent {
  @Input() canChangeDate = false;
  today: Date = new Date(new Date().setHours(0, 0, 0, 0));
  @Output() dateChange: EventEmitter<Date> = new EventEmitter<Date>();
  menuItems: MenuItem[];
  selectedMenuItem: MenuItem;

  constructor() {
    this.initMenuItems();
    this.selectedMenuItem = this.menuItems[0];
  }

  initMenuItems(): void {
    const dateCursor: Date = new Date(this.today);
    this.menuItems = [{ date: new Date(dateCursor), label: todayLabel }];
    for (let i = 0; i < MENU_ITEM_COUNT; i++) {
      dateCursor.setDate(dateCursor.getDate() - 1);
      this.menuItems.push({
        date: new Date(dateCursor),
        label: dayLabels[dateCursor.getDay()],
      });
    }
  }

  isItemActive(item: MenuItem): boolean {
    return item.label === this.selectedMenuItem.label;
  }

  selectDay(event: MouseEvent, item: MenuItem): void {
    if (this.canChangeDate) {
      event.preventDefault();
      this.selectedMenuItem = item;
      this.dateChange.emit(item.date);
    }
  }
}
