@use 'shared' as *;

:host {
  border-bottom: 1px solid var(--kui-gray-200);
  overflow-x: auto;
  overflow-y: hidden;
  /* Hide scrollbar*/
  scrollbar-width: none; /* Firefox 64 */
  -ms-overflow-style: none; /* IE 11 */
}

/* Hide scrollbar for Chrome, Safari and Opera */
:host::-webkit-scrollbar {
  display: none;
}

ul {
  display: flex;
  flex-direction: row;
  gap: 48px;
  li {
    display: contents;
    a.active {
      padding-bottom: 16px;
      border-bottom: 4px solid var(--kui-red-400);
      font-weight: 700;
      line-height: 120%;
      letter-spacing: 0.16px;
    }
  }
}

a {
  color: var(--kui-gray-550);
  font-family: var(--kui-font-primary);
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 160%;
  cursor: pointer;
}
