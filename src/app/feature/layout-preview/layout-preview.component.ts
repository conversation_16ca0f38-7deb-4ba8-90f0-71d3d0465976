import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, ActivatedRouteSnapshot } from '@angular/router';
import { LayoutElementContentConfiguration, LayoutElementRow, LayoutPageType } from '@trendency/kesma-ui';
import { LayoutComponent } from '../layout/components/layout/layout.component';
import { NgIf } from '@angular/common';

@Component({
  selector: 'app-layout-preview',
  templateUrl: './layout-preview.component.html',
  styleUrls: ['./layout-preview.component.scss'],
  imports: [LayoutComponent, NgIf],
})
export class LayoutPreviewComponent implements OnInit {
  layoutApiData: {
    struct: LayoutElementRow[];
    content: LayoutElementContentConfiguration[];
  };
  layoutType: LayoutPageType;
  constructor(private readonly route: ActivatedRoute) {}
  ngOnInit(): void {
    const routeSnapshot: ActivatedRouteSnapshot = this.route.snapshot;
    this.layoutApiData = routeSnapshot.data['layoutData'];
  }
}
