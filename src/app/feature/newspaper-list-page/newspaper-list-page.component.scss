@use 'shared' as *;

.newspaper-page {
  &-wrapper {
    padding: 30px 0 100px;
  }

  &-title {
    font-family: var(--kui-font-condensed);
    color: var(--kui-gray-500);
    text-transform: uppercase;
    font-weight: 700;
    font-size: 38px;
    line-height: 42px;
    margin: 20px 0;
  }

  &-buttons {
    margin-top: 30px;
    display: flex;
    justify-content: flex-start;
    gap: 20px;

    @include media-breakpoint-down(md) {
      flex-direction: column;
    }

    nso-simple-button {
      margin: 0;
    }
  }

  &-link {
    margin-top: 30px;

    a {
      color: var(--kui-gray-550);
      text-decoration: underline;
      font-weight: normal;
      cursor: pointer;
      transition: color 300ms ease-in-out;

      &:hover {
        color: var(--kui-red-200);
      }
    }
  }

  &-latest {
    p {
      @include media-breakpoint-down(md) {
        margin-top: 20px;
      }
    }
  }

  &-list {
    border-top: 1px solid var(--kui-gray-200);
    margin-top: 50px;
    padding-top: 50px;

    h2 {
      font-family: var(--kui-font-primary);
      color: var(--kui-gray-500);
      font-weight: 700;
      font-size: 30px;
      line-height: 36px;
      margin: 0 0 20px;
    }

    &-wrapper {
      > div {
        margin-bottom: calc(var(--bs-gutter-x));
      }
    }

    nso-simple-button {
      margin: 0 auto;
    }
  }

  &-contact {
    border-top: 1px solid var(--kui-gray-200);
    margin-top: 50px;
    padding-top: 50px;

    h3 {
      font-family: var(--kui-font-primary);
      color: var(--kui-gray-500);
      font-weight: 700;
      font-size: 30px;
      line-height: 36px;
      margin: 0 0 20px;
    }

    &-details {
      margin-top: 20px;
    }

    a {
      color: var(--kui-gray-550);
      text-decoration: underline;
      font-weight: normal;
      cursor: pointer;
      transition: color 300ms ease-in-out;

      &:hover {
        color: var(--kui-red-200);
      }
    }
  }

  &-newspaper {
    display: block;
    border: 1px solid var(--kui-gray-200);
    color: var(--kui-gray-550);
    text-align: center;

    img {
      width: 100%;
      min-height: 170px;
    }

    p {
      background-color: var(--kui-gray-100);
      font-weight: bold;
      font-size: 16px;
      line-height: 20px;
      margin: 0;
      font-family: var(--kui-font-primary);
      padding: 10px;
    }
  }
}
