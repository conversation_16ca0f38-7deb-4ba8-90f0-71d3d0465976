import { ChangeDetectionStrategy, Component, Inject, OnInit } from '@angular/core';
import { ActivatedRoute, RouterLink } from '@angular/router';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { createNSOTitle, defaultMetaInfo, NsoBreadcrumbComponent, NsoSimpleButtonComponent } from '../../shared';
import { ArticleCard, buildArticleUrl, createCanonicalUrlForPageablePage, FocusPointDirective } from '@trendency/kesma-ui';
import { AsyncPipe, DOCUMENT, NgForOf, NgIf, NgTemplateOutlet, SlicePipe } from '@angular/common';
import { FormatPipeModule } from 'ngx-date-fns';

@Component({
  selector: 'app-newspapers-list-page',
  templateUrl: './newspaper-list-page.component.html',
  styleUrls: ['./newspaper-list-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgIf,
    AsyncPipe,
    NsoBreadcrumbComponent,
    NgTemplateOutlet,
    NsoSimpleButtonComponent,
    RouterLink,
    SlicePipe,
    NgForOf,
    FocusPointDirective,
    FormatPipeModule,
  ],
})
export class NewspaperListPageComponent implements OnInit {
  articles$: Observable<ArticleCard[]> = this.route.data.pipe(map((res) => res?.['data']));

  buildArticleUrl = buildArticleUrl;

  constructor(
    private readonly seo: SeoService,
    private readonly route: ActivatedRoute,
    @Inject(DOCUMENT) private readonly document: Document
  ) {}

  ngOnInit(): void {
    this.setPageMeta();
  }

  navigateToDigitalStand(): void {
    this.document.defaultView?.open('https://napilap.nemzetisport.hu/elofizetes/', '_blank');
  }

  setPageMeta(): void {
    const canonical = createCanonicalUrlForPageablePage('napilap', this.route.snapshot);
    canonical && this.seo.updateCanonicalUrl(canonical);
    const title = createNSOTitle(`Napilap`);
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    };
    this.seo.setMetaData(metaData);
  }
}
