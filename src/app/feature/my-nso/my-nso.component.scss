@use 'shared' as *;

:host {
  .wrapper:not(.with-aside) {
    padding: 30px 0 0;
  }

  .wrapper.with-aside {
    padding: 0 0 50px;

    @include media-breakpoint-down(md) {
      padding: 0;
    }

    > .left-column {
      width: calc(100% - 336px - 40px);

      @include media-breakpoint-down(md) {
        width: 100%;
      }
    }

    > aside {
      width: 336px;

      @include media-breakpoint-down(md) {
        width: 100%;
      }
    }
  }

  .title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 40px;

    &-mynso {
      color: var(--kui-red-400);
      font-size: 24px;
      font-weight: 700;
    }

    &-right {
      display: flex;
      align-items: center;
      flex-direction: row;
      column-gap: 15px;

      @include media-breakpoint-down(sm) {
        flex-direction: column;
      }
    }

    &-settings {
      color: var(--kui-red-400);
      text-decoration: underline;
      font-weight: 700;
    }
  }

  .profil {
    margin: 30px 0;
    border: 3px solid var(--kui-red-400);

    &-container {
      display: flex;
      flex-direction: column;
      row-gap: 20px;
      margin: 20px;

      &-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        column-gap: 20px;

        @include media-breakpoint-down(sm) {
          flex-direction: column;
          row-gap: 10px;
        }

        &-left {
          font-size: 20px;
          font-weight: 700;
        }

        &-right {
          color: var(--kui-red-400);
          font-weight: 700;
          text-decoration: underline;
        }
      }

      &-datas {
        display: flex;
        flex-direction: row;

        @include media-breakpoint-down(sm) {
          flex-direction: column;
          row-gap: 10px;
        }

        &-left {
          width: 200px;
          font-weight: 600;
        }

        &-username {
          color: var(--kui-red-400);
          font-weight: 700;
        }

        &-email {
          color: var(--kui-red-400);
          text-decoration: underline;
        }
      }
    }
  }
}

.left-column-title {
  font-size: 26px;
  font-family: var(--kui-font-primary);
  font-weight: 700;
  line-height: 45px;
}

.article-number {
  font-weight: 600;
  margin-top: 10px;
}

.article-divider {
  width: 100%;
  background-color: var(--kui-gray-200);
  height: 1px;
  margin: 10px 0;
}
