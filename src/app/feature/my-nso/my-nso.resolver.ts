import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { catchError, map, Observable, switchMap, throwError } from 'rxjs';
import { RedirectService } from '@trendency/kesma-ui';
import { ApiService, SecureApiService } from '../../shared';

@Injectable()
export class MyNsoResolver {
  constructor(
    private readonly apiService: ApiService,
    private readonly router: Router,
    private readonly secureApiService: SecureApiService,
    private readonly redirectService: RedirectService
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<any> {
    const pageIndex = route.queryParams['page'] ? route.queryParams['page'] - 1 : 0;

    return this.secureApiService.getFollowedColumns().pipe(
      switchMap((followedColumns) => {
        return this.apiService
          .getSearch(
            {},
            pageIndex,
            20,
            followedColumns.data.filter((d) => d.isFollowed).map((d) => d.slug)
          )
          .pipe(
            map((res) => {
              if (this.redirectService.shouldBeRedirect(pageIndex, res?.data)) {
                this.redirectService.redirectOldUrl(`my-nso`, false, 302);
              }
              return res;
            }),
            catchError((error) => {
              this.router
                .navigate(['/', '404'], {
                  state: {
                    errorResponse: JSON.stringify(error),
                  },
                  skipLocationChange: true,
                })
                .then();
              return throwError(() => error);
            })
          );
      })
    );
  }
}
