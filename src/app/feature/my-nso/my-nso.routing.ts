import { Routes } from '@angular/router';
import { MyNsoComponent } from './my-nso.component';
import { MyNsoResolver } from './my-nso.resolver';

export const myNsoRoutes: Routes = [
  {
    path: '',
    component: MyNsoComponent,
    runGuardsAndResolvers: 'paramsOrQueryParamsChange',
    resolve: {
      data: MyNsoResolver,
    },
    providers: [MyNsoResolver],
  },
  /* {
    path: 'testreszabas',
    component: MyNsoSettingsComponent,
    data: {
      isFullWidth: true,
    },
  }, */
];
