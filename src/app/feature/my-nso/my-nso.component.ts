import { ChangeDetectionStrategy, Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute, RouterLink } from '@angular/router';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import { ArticleCard, createCanonicalUrlForPageablePage, LimitableMeta, mapBackendArticleDataToArticleCard, User } from '@trendency/kesma-ui';
import { map, Observable, Subject, takeUntil, tap } from 'rxjs';
import { defaultMetaInfo } from 'src/app/shared/constants/meta.consts';
import { ArticleCardType, AuthService, createNSOTitle, NsoArticleCardComponent, NsoBreadcrumbComponent, NsoPagerComponent } from '../../shared';
import { AsyncPipe, DatePipe, NgForOf, NgIf } from '@angular/common';
import { SidebarComponent } from '../layout/components/sidebar/sidebar.component';

@Component({
  selector: 'app-my-nso',
  templateUrl: './my-nso.component.html',
  styleUrls: ['./my-nso.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NsoBreadcrumbComponent, DatePipe, AsyncPipe, NgForOf, NsoArticleCardComponent, NsoPagerComponent, NgIf, SidebarComponent, RouterLink],
})
export class MyNsoComponent implements OnInit, OnDestroy {
  rowAllCount = 0;
  articles$: Observable<ArticleCard[]> = this.activatedRoute.data.pipe(
    tap((res) => (this.rowAllCount = res['data'].meta.limitable.rowAllCount as number)),
    map((res) => res['data'].data.map(mapBackendArticleDataToArticleCard))
  );
  limitables$: Observable<LimitableMeta> = this.activatedRoute.data.pipe(map((res) => res?.['data']?.meta?.limitable));
  readonly ArticleCardType = ArticleCardType.FeaturedColumnTitleLeadTags;
  private readonly destroy$: Subject<boolean> = new Subject();

  constructor(
    private readonly activatedRoute: ActivatedRoute,
    private readonly authService: AuthService,
    private readonly seo: SeoService
  ) {}

  get user(): User | undefined {
    return this.authService.currentUser;
  }

  ngOnInit(): void {
    this.setMetaData();
    this.activatedRoute.data.pipe(takeUntil(this.destroy$)).subscribe();
  }

  ngOnDestroy(): void {
    this.destroy$.next(true);
  }

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage('my-nso');
    if (canonical) this.seo.updateCanonicalUrl(canonical);
    const title = createNSOTitle('Profilom');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
      robots: 'noindex, nofollow',
    };
    this.seo.setMetaData(metaData);
  }
}
