import { Injectable } from '@angular/core';
import { map } from 'rxjs/operators';
import { Column, ColumnTreeElement, FollowedColumn, FollowedColumnTreeElement } from '@trendency/kesma-ui';
import { forkJoin, Observable } from 'rxjs';
import { ApiService, SecureApiService } from '../../../shared';

@Injectable({
  providedIn: 'root',
})
export class MyNsoSettingsService {
  constructor(
    private readonly apiService: ApiService,
    private readonly secureApiService: SecureApiService
  ) {}

  getSettings(): Observable<FollowedColumnTreeElement[]> {
    return forkJoin([
      this.apiService.getColumnsAsTree().pipe(map((response) => response.data)),
      this.secureApiService.getFollowedColumns().pipe(map((response) => response.data)),
    ]).pipe(
      map(([columnTree, followedColumns]) => {
        return columnTree.map(
          (column: ColumnTreeElement) =>
            ({
              ...column,
              isFollowed: !!followedColumns.find((followedColumn: FollowedColumn) => followedColumn.slug === column.slug),
              children: column.children.map((subColumn: Column) => ({
                ...subColumn,
                isFollowed: !!followedColumns.find((followedColumn: FollowedColumn) => followedColumn.slug === subColumn.slug),
              })),
            }) as FollowedColumnTreeElement
        );
      })
    );
  }

  saveSetting(column: FollowedColumn, newValue: boolean): Observable<void> {
    if (newValue) {
      return this.secureApiService.setFollowedColumn(column.id);
    } else {
      return this.secureApiService.deleteFollowedColumn(column.id);
    }
  }
}
