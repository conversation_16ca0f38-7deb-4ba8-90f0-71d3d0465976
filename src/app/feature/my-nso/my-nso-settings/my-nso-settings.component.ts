import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import { ButtonStyleInfo, createCanonicalUrlForPageablePage, FollowedColumn, FollowedColumnTreeElement } from '@trendency/kesma-ui';
import { BehaviorSubject } from 'rxjs';
import { defaultMetaInfo } from 'src/app/shared/constants/meta.consts';
import { createNSOTitle, NsoBreadcrumbComponent, NsoSimpleButtonComponent, NsoSpinnerComponent, NsoToggleSwitchComponent } from '../../../shared';
import { MyNsoSettingsService } from './my-nso-settings.service';
import { AsyncPipe, NgForOf, NgIf, SlicePipe } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'app-my-nso-settings',
  templateUrl: './my-nso-settings.component.html',
  styleUrls: ['./my-nso-settings.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NsoBreadcrumbComponent,
    NgIf,
    AsyncPipe,
    NgForOf,
    NsoToggleSwitchComponent,
    FormsModule,
    SlicePipe,
    NsoSimpleButtonComponent,
    RouterLink,
    NsoSpinnerComponent,
  ],
})
export class MyNsoSettingsComponent implements OnInit {
  tree$: BehaviorSubject<FollowedColumnTreeElement[] | undefined> = new BehaviorSubject<FollowedColumnTreeElement[] | undefined>(undefined);
  styleInfo: ButtonStyleInfo = { icon: { transform: 'rotate(180deg)' } };

  constructor(
    private readonly seo: SeoService,
    private readonly myNsoSettingsService: MyNsoSettingsService
  ) {}

  ngOnInit(): void {
    this.myNsoSettingsService.getSettings().subscribe((settings: FollowedColumnTreeElement[]) => this.tree$.next(settings));
    this.setMetaData();
  }

  onChange(column: FollowedColumn): void {
    this.myNsoSettingsService.saveSetting(column, !column.isFollowed).subscribe(() => {
      column.isFollowed = !column.isFollowed;
    });
  }

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage('my-nso/testreszabas');
    if (canonical) this.seo.updateCanonicalUrl(canonical);
    const title = createNSOTitle('MY NSO - Testreszabás');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    };
    this.seo.setMetaData(metaData);
  }
}
