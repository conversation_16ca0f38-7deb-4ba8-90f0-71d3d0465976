<section class="my-nso-settings">
  <div class="breadcrumb-wrapper">
    <nso-breadcrumb [isShowLogout]="true" [items]="[{ label: 'Profilom', url: '/my-nso' }, { label: 'My NSO testreszabása' }]"></nso-breadcrumb>
  </div>

  <div class="wrapper">
    <h1>MY NSO TESTRESZABÁSA</h1>
    <ng-container *ngIf="tree$ | async as tree; else loading">
      <section *ngFor="let item of tree" class="column">
        <div class="column-header border-bottom">
          <h2>{{ item.title }}</h2>
          <nso-toggle-switch (toggleChanged)="onChange(item)" [ngModel]="item.isFollowed"></nso-toggle-switch>
        </div>
        <div *ngFor="let child of item.children | slice: 0 : (item.isExpanded ? item.children.length : 3)" class="sub-column border-bottom">
          <h3>{{ child.title }}</h3>
          <nso-toggle-switch (toggleChanged)="onChange(child)" [ngModel]="child.isFollowed"></nso-toggle-switch>
        </div>
        <nso-simple-button (click)="item.isExpanded = !item.isExpanded" *ngIf="item.children.length > 3" color="link">
          Mutass {{ item.isExpanded ? 'kevesebbet' : 'többet' }}</nso-simple-button
        >
      </section>
      <section class="save">
        <nso-simple-button [routerLink]="['..']" color="primary" round="round">VISSZA</nso-simple-button>
      </section>
    </ng-container>
  </div>
</section>

<ng-template #loading>
  <nso-spinner></nso-spinner>
</ng-template>
