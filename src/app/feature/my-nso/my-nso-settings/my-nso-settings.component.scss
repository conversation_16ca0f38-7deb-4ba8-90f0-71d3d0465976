@use 'shared' as *;

.my-nso-settings {
  padding: 0 0 100px 0;

  @include media-breakpoint-down(sm) {
    padding: 15px 15px 50px;
  }

  .breadcrumb-wrapper {
    padding: 30px 0;
    background-color: var(--kui-white);
    margin-bottom: 100px;

    @include media-breakpoint-down(sm) {
      margin-bottom: 15px;
      border-radius: 10px;
    }

    nso-breadcrumb {
      margin: 0 auto;
      width: $header-max-width;
      max-width: calc(100% - 30px);
    }
  }

  .wrapper {
    width: 100%;
    max-width: 630px;
    border-radius: 10px;
    background-color: var(--kui-white);
    padding: 70px 80px 60px;

    @include media-breakpoint-down(sm) {
      padding: 50px 25px 30px;
    }
  }

  h1 {
    font-family: var(--kui-font-condensed);
    color: var(--kui-gray-500);
    font-size: 38px;
    font-style: normal;
    font-weight: 700;
    line-height: 110%; /* 41.8px */
    letter-spacing: -0.76px;
    text-transform: uppercase;
    margin-bottom: 70px;
    text-align: center;
  }

  .column {
    display: flex;
    flex-direction: column;
    gap: 25px;
    width: 100%;
    margin-bottom: 40px;

    &-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-right: 25px;
    }

    .border-bottom {
      padding-bottom: 25px;
      border-bottom: 1px solid var(--kui-gray-200);
    }
  }

  h2 {
    color: var(--kui-gray-550);
    font-family: var(--kui-font-condensed);
    font-size: 24px;
    font-style: normal;
    font-weight: 700;
    line-height: 120%; /* 28.8px */
    text-transform: uppercase;
  }

  h3 {
    color: var(--kui-gray-350);
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 160%;
  }

  .sub-column {
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: center;
    padding-left: 25px;
    padding-right: 25px;
  }
}
