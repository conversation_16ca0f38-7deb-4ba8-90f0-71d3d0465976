<section>
  <div class="wrapper">
    <nso-breadcrumb [isShowLogout]="true" [items]="[{ label: 'Profilom' }]"></nso-breadcrumb>
    <div class="title">
      <span class="title-mynso">Profilom</span>
      <!-- <div class="title-right">
        <a class="title-settings" routerLink="testreszabas">MY NSO testreszabása</a>
      </div> -->
    </div>
    <div class="profil">
      <div class="profil-container">
        <div class="profil-container-header">
          <span class="profil-container-header-left">Személyes profil</span>
          <a class="profil-container-header-right" routerLink="/profil">Profil beállítások módosítása</a>
        </div>
        <div class="profil-container-datas">
          <span class="profil-container-datas-left">Felhasználónév</span>
          <span class="profil-container-datas-username">{{ user?.username }}</span>
        </div>
        <div class="profil-container-datas">
          <span class="profil-container-datas-left">E-mail-cím</span>
          <span class="profil-container-datas-email">{{ user?.email }}</span>
        </div>
        <div *ngIf="user?.passwordLastSave" class="profil-container-datas">
          <span class="profil-container-datas-left">Jelszó</span>
          <span>Utolsó módosítás: {{ (user?.passwordLastSave | date: 'yyyy.MM.dd HH:mm') ?? '-' }}</span>
        </div>
      </div>
    </div>
  </div>
  <div class="wrapper with-aside">
    <div class="left-column">
      <div class="left-column-title">MY NSO hírek</div>
      <div class="article-number">{{ rowAllCount }} találat</div>
      <div class="article-divider"></div>
      <nso-article-card nso-article-card *ngFor="let article of articles$ | async" [styleID]="ArticleCardType" [data]="article"> </nso-article-card>

      <nso-pager
        *ngIf="limitables$ | async as limitable"
        [hasFirstLastButton]="true"
        [isCountPager]="true"
        [rowAllCount]="limitable.rowAllCount!"
        [rowOnPageCount]="limitable.rowOnPageCount!"
        [showTotalPagesPositionAtRight]="true"
      >
      </nso-pager>
    </div>
    <aside>
      <app-sidebar></app-sidebar>
    </aside>
  </div>
</section>
