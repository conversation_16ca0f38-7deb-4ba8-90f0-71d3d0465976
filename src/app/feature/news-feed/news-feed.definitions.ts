import { ApiResult, Article, ArticleCard, RecommendationsData } from '@trendency/kesma-ui';
import { ApiResponseMetaList } from '@trendency/kesma-ui/lib/definitions/api-result';

export type NewsFeedDossier = Readonly<{
  slug: string;
  title: string;
  description: string;
  newsFeedDescription: string;
  newsFeedTitle: string;
  coverImage: NewsFeedDosserImage;
  createdAt: string;
}>;

export type NewsFeedDosserImage = Readonly<{
  caption?: string;
  photographer?: string;
  source?: string;
  thumbnail?: string;
}>;

export type NewsFeedApiResponseMeta = ApiResponseMetaList &
  Readonly<{
    newsFeed: NewsFeedDossier;
  }>;

export type NewsFeedApiResponseList = {
  data: NewsFeedApiResponse;
};

export type NewsFeedApiResponse = {
  result: ApiResult<Article[], NewsFeedApiResponseMeta>;
  recommendations?: ApiResult<RecommendationsData>;
  relatedArticles?: Array<ArticleCard[]>;
};
