import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot } from '@angular/router';
import { forkJoin, Observable, share } from 'rxjs';
import { NewsFeedService } from './news-feed.service';
import { ArticleRouteParams } from '@trendency/kesma-ui';
import { NewsFeedApiResponse } from '../news-feed.definitions';

@Injectable()
export class NewsFeedResolver {
  constructor(private readonly newsFeedService: NewsFeedService) {}

  resolve(route: ActivatedRouteSnapshot): Observable<NewsFeedApiResponse> {
    const params: ArticleRouteParams = route.params as ArticleRouteParams;
    const slug = params.articleSlug;
    const newsFeed$ = this.newsFeedService.getNewsFeed(slug).pipe(share());

    return forkJoin({
      result: newsFeed$,
      // relatedArticles: this.newsFeedService.getRelatedArticles(newsFeed$),
    });
  }
}
