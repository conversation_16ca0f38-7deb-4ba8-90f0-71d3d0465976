import { Injectable } from '@angular/core';
import { HttpErrorResponse } from '@angular/common/http';
import { Router } from '@angular/router';
import { IHttpOptions, ReqService } from '@trendency/kesma-core';
import { Observable, throwError } from 'rxjs';
import { ApiResult, Article } from '@trendency/kesma-ui';
import { NewsFeedApiResponseMeta } from '../news-feed.definitions';
import { catchError, map, switchMap } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class NewsFeedService {
  private meta = {};

  constructor(
    private readonly router: Router,
    private readonly reqService: ReqService
  ) {}

  getNewsFeed(slug: string, pageLimit = 0, rowCountLimit = 15): Observable<ApiResult<Article[], NewsFeedApiResponseMeta>> {
    const params: IHttpOptions = {
      params: {
        page_limit: pageLimit.toString(),
        rowCount_limit: rowCountLimit.toString(),
      },
    };
    return this.reqService.get<ApiResult<Article[]>>(`/content-group/news-feed/${slug}`, { ...params }).pipe(
      map((result) => {
        if (!result.data.length) {
          this.navigateTo404();
        }
        return result;
      }),
      catchError((error: HttpErrorResponse | Error) => {
        this.navigateTo404(error);
        return throwError(() => error);
      }),
      switchMap((result: ApiResult<Article[]>) => {
        this.meta = result?.meta;
        return [result?.data];
      }),
      map((articles: Article[]) => {
        return {
          data: articles,
          meta: this.meta,
        } as ApiResult<Article[], NewsFeedApiResponseMeta>;
      })
    );
  }

  private navigateTo404(error?: HttpErrorResponse | Error): void {
    this.router
      .navigate(['/', '404'], {
        state: { errorResponse: JSON.stringify(error || {}) },
        skipLocationChange: true,
      })
      .then();
  }
}
