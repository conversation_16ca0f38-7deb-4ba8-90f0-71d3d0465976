import { ChangeDetectionStrategy, ChangeDetectorRef, Component, HostBinding, OnInit } from '@angular/core';
import { ActivatedRoute, RouterLink } from '@angular/router';
import {
  Article,
  ArticleBodyDetails,
  ArticleBodyType,
  ArticleCard,
  ArticleFileLinkDirective,
  ArticleRouteParams,
  ArticleVideoComponent,
  buildArticleUrl,
  buildColumnUrl,
  buildTagUrl,
  GalleryData,
  GalleryElementData,
  PublicMultiAuthor,
  VoteDataWithAnswer,
  VoteService,
} from '@trendency/kesma-ui';
import { forkJoin, Observable, Subject } from 'rxjs';
import { map, takeUntil } from 'rxjs/operators';
import { ArticleService } from '../../article-page/api/article-page.service';
import { GalleryApiService } from '../../gallery-layer/api/gallery-api.service';
import { NewsFeedService } from '../api/news-feed.service';
import { NewsFeedApiResponseList, NewsFeedDossier } from '../news-feed.definitions';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import {
  ArticleCardType,
  ArticleDossierComponent,
  NsoArticleCardComponent,
  NsoArticlesRelatedContentComponent,
  NsoGalleryCardComponent,
  NsoQuizComponent,
  NsoSimpleButtonComponent,
  NsoSpinnerComponent,
  NsoVotingComponent,
  NsoWysiwygBoxComponent,
} from 'src/app/shared';
import { AsyncPipe, NgForOf, NgIf, NgSwitch, NgSwitchCase, NgTemplateOutlet } from '@angular/common';
import { PublishDatePipe } from '@trendency/kesma-core';
import { SidebarComponent } from '../../layout/components/sidebar/sidebar.component';

const EB_NEWS_FEED_SLUG = 'eb-hirfolyam';
const OLYMPICS_NEWS_FEED_SLUG = 'olimpia-hirfolyam';

@Component({
  selector: 'app-news-feed',
  templateUrl: './news-feed.component.html',
  styleUrls: ['./news-feed.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgForOf,
    NgIf,
    RouterLink,
    NgTemplateOutlet,
    PublishDatePipe,
    NsoSpinnerComponent,
    NsoSimpleButtonComponent,
    AsyncPipe,
    SidebarComponent,
    NgSwitch,
    NgSwitchCase,
    NsoWysiwygBoxComponent,
    ArticleFileLinkDirective,
    ArticleVideoComponent,
    NsoVotingComponent,
    NsoQuizComponent,
    NsoArticleCardComponent,
    NsoGalleryCardComponent,
    ArticleDossierComponent,
    NsoArticlesRelatedContentComponent,
  ],
})
export class NewsFeedComponent implements OnInit {
  articles: Article[] = [];
  galleries: Record<string, GalleryData> = {};
  isAdultsOnly = false;
  dossier?: NewsFeedDossier;
  rowAllCount = 0;
  page = 0;
  isLoading = false;
  articleSlug?: string;
  isEbNewsfeed = false;

  @HostBinding('class.is-olympics') isOlympicsNewsfeed = false;

  MOBILE_BREAKPOINT = '(max-width: 768px)';

  isMobile$ = this.breakpointObserver.observe([this.MOBILE_BREAKPOINT]).pipe(map((state: BreakpointState) => state.matches));

  readonly ArticleBodyType = ArticleBodyType;
  readonly ArticleCardType = ArticleCardType;

  readonly voteCache = this.voteService.voteCache;

  private readonly unsubscribe$: Subject<boolean> = new Subject();
  private readonly ARTICLES_PER_PAGE = 15;

  constructor(
    private readonly route: ActivatedRoute,
    private readonly articleService: ArticleService,
    private readonly cdr: ChangeDetectorRef,
    private readonly galleryService: GalleryApiService,
    private readonly newsFeedService: NewsFeedService,
    private readonly voteService: VoteService,
    private readonly breakpointObserver: BreakpointObserver
  ) {}

  ngOnInit(): void {
    this.route.params.subscribe((params) => {
      this.articleSlug = (params as ArticleRouteParams).articleSlug;
      if (this.articleSlug === EB_NEWS_FEED_SLUG) {
        this.isEbNewsfeed = true;
      }
      if (this.articleSlug === OLYMPICS_NEWS_FEED_SLUG) {
        this.isOlympicsNewsfeed = true;
      }
    });

    (this.route.data as Observable<NewsFeedApiResponseList>).subscribe(({ data: { result } }) => {
      this.articles = result?.data?.map(this.mapArticleBody.bind(this));
      this.isAdultsOnly = this.articles?.some((article) => article?.isAdultsOnly);
      this.dossier = result?.meta?.newsFeed;
      this.rowAllCount = result?.meta?.limitable?.rowAllCount as number;

      for (const article of this.articles) {
        this.voteService.initArticleVotes(article);
      }

      this.cdr.markForCheck();
    });
  }

  get canLoadMore(): boolean {
    return this.rowAllCount !== 0 && this.ARTICLES_PER_PAGE * (this.page + 1) < this.rowAllCount;
  }

  public onVotingSubmit($event: string, voteData: VoteDataWithAnswer): void {
    const votingSubmit$ = this.voteService.onVotingSubmit($event, voteData).subscribe({
      complete: () => {
        this.cdr.detectChanges();
        votingSubmit$.unsubscribe();
      },
    });
  }

  public getArticleLink(article: Article): string[] {
    return buildArticleUrl(article);
  }

  public getColumnLink(article: Article): string[] {
    return buildColumnUrl(article);
  }

  public getTagLink(article: Article, selectedTag: number): string[] {
    return buildTagUrl(article, selectedTag);
  }

  public getMultiAuthorLink(author: PublicMultiAuthor): string[] | undefined {
    return author?.slug ? ['/', 'szerzo', author?.slug] : undefined;
  }
  public getArticlePublishdate(article: Article): Date | undefined {
    return article.publishDate;
  }

  doubleArticleRecommendations(arr: ArticleBodyDetails[]): ArticleBodyDetails[] {
    return arr.filter((elem: ArticleBodyDetails) => elem?.value?.id);
  }

  doubleArticleRecommendation(articleBodyDetails: ArticleBodyDetails[]): ArticleCard[] {
    return this.doubleArticleRecommendations(articleBodyDetails)?.map((details: ArticleBodyDetails) => {
      const article = details.value;
      if (article.thumbnailUrl) {
        article.thumbnail = { url: article?.thumbnailUrl };
      }
      return {
        ...article,
        lead: article?.excerpt || article?.lead,
        category: {
          name: article?.primaryColumn?.title,
          slug: article?.primaryColumn?.slug,
        },
        thumbnailFocusedImages: article?.thumbnailUrlFocusedImages,
      };
    }) as ArticleCard[];
  }

  public loadMoreResults(): void {
    if (!this.canLoadMore || !this.articleSlug) {
      return;
    }

    ++this.page;
    this.isLoading = true;
    const newsFeed$ = this.newsFeedService.getNewsFeed(this.articleSlug, this.page);

    forkJoin({
      newsFeed: newsFeed$,
      // relatedArticles: this.newsFeedService.getRelatedArticles(newsFeed$)
    }).subscribe(({ newsFeed }) => {
      const articles = newsFeed?.data?.map(this.mapArticleBody.bind(this));
      this.articles = this.articles.concat(articles);
      this.isLoading = false;
      this.cdr.markForCheck();
    });
  }

  private loadEmbeddedGalleries(article: Article): void {
    const bodyElements = article?.body ?? [];
    const gallerySubs = ((bodyElements ?? []) as GalleryElementData[])
      .filter(({ type }) => type === ArticleBodyType.Gallery)
      .map((bodyElem: GalleryElementData) => this.galleryService.getGalleryDetails(bodyElem.details[0].value?.slug));

    forkJoin(gallerySubs)
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe((galleries) => {
        galleries.forEach((gallery) => {
          this.galleries[gallery.id] = {
            ...gallery,
            highlightedImageUrl: gallery.highlightedImage.url,
          } as GalleryData;
          this.cdr.markForCheck();
        });
      });
  }

  private mapArticleBody(article: Article): Article {
    this.loadEmbeddedGalleries(article);
    return {
      ...article,
      body: this.articleService.prepareArticleBody(article?.body),
      excerpt: article?.lead || article?.excerpt,
      columnSlug: article?.primaryColumn?.slug,
    };
  }
}
