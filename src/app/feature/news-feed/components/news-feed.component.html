<section>
  <div class="wrapper with-aside">
    <div class="left-column">
      <h1 class="dossier-title">{{ dossier?.title }}</h1>
      <div class="dossier-body">
        <ng-container *ngFor="let article of articles; let index = index">
          <img *ngIf="article?.thumbnail" class="article-img" [src]="article?.thumbnail" loading="lazy" />
          <div class="article-author-list">
            <ng-container *ngFor="let author of article.publicAuthorM2M">
              <div class="article-author">
                <img class="article-avatar" [src]="author?.avatar || '/assets/images/nemzetisport.png'" [alt]="author?.description" />
                <ng-container *ngIf="getMultiAuthorLink(author) as authorLink; else authorNameTemplate">
                  <a [routerLink]="authorLink">
                    <ng-container *ngTemplateOutlet="authorNameTemplate"></ng-container>
                  </a>
                </ng-container>
                <ng-template #authorNameTemplate>
                  <strong class="article-author-name"
                    >{{ author?.fullName || 'Nemzeti Sport Online' }}
                    <ng-container *ngIf="$any(article).publicAuthorLocalization"> &bull; {{ $any(article).publicAuthorLocalization }} </ng-container>
                  </strong>
                </ng-template>
              </div>
            </ng-container>
          </div>
          <div class="article-meta">
            <div class="article-publish-date">{{ article.publishDate | publishDate: 'yyyy.MM.dd. HH:mm' }}</div>
          </div>
          <a [routerLink]="getArticleLink(article)">
            <h2 class="article-title">{{ article?.title }}</h2>
          </a>
          <div class="article-lead" *ngIf="article?.lead">{{ article?.lead }}</div>
          <div class="article-tags" *ngIf="article?.tags?.length">
            <ng-container *ngFor="let tag of article?.tags; index as index; last as last">
              <a class="article-tag" [routerLink]="getTagLink(article, index)">{{ tag?.title }}</a>
              <div *ngIf="!last" class="dossier-divider vertical"></div>
            </ng-container>
          </div>
          <ng-container [ngTemplateOutlet]="bodyContent" [ngTemplateOutletContext]="{ article: article }"></ng-container>
          <div class="dossier-divider"></div>
        </ng-container>
      </div>

      <ng-container *ngIf="isLoading; else loadMoreTemplate">
        <nso-spinner></nso-spinner>
      </ng-container>

      <ng-template #loadMoreTemplate>
        <nso-simple-button *ngIf="canLoadMore" [class.olympics-news-feed]="isOlympicsNewsfeed" (click)="loadMoreResults()">
          <strong>Mutass többet</strong>
        </nso-simple-button>
      </ng-template>
    </div>
    <aside *ngIf="((isMobile$ | async) === false || !isEbNewsfeed) && !isOlympicsNewsfeed">
      <app-sidebar></app-sidebar>
    </aside>
  </div>
</section>

<ng-template #bodyContent let-article="article">
  <ng-container *ngFor="let element of article?.body">
    <ng-container [ngSwitch]="element.type">
      <ng-container *ngSwitchCase="ArticleBodyType.Wysywyg">
        <ng-container *ngFor="let wysiwygDetail of element?.details">
          <nso-wysiwyg-box [html]="wysiwygDetail?.value || ''" trArticleFileLink></nso-wysiwyg-box>
        </ng-container>
      </ng-container>

      <div class="block-video" *ngSwitchCase="ArticleBodyType.MediaVideo">
        <kesma-article-video [data]="element?.details[0]?.value"></kesma-article-video>
      </div>

      <div class="voting-block" *ngSwitchCase="ArticleBodyType.Voting">
        @if (voteCache[element?.details[0]?.value?.id ?? ''] | async; as voteData) {
          <nso-voting [data]="voteData.data" [voteId]="voteData.votedId" (vote)="onVotingSubmit($event, voteData)" />
        }
      </div>

      <ng-container *ngSwitchCase="ArticleBodyType.Quiz">
        <nso-quiz [data]="element?.details[0]?.value"></nso-quiz>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleBodyType.Article">
        <nso-article-card nso-article-card [data]="element?.details[0]?.value" [styleID]="ArticleCardType.FeaturedSidedImgColumnTitleLead"></nso-article-card>
      </ng-container>

      <div class="block-gallery" *ngSwitchCase="ArticleBodyType.Gallery">
        <ng-container *ngIf="galleries[element?.details[0]?.value?.id] as gallery">
          <nso-gallery-card [data]="gallery" [routerLink]="['/galeria', gallery?.slug]"></nso-gallery-card>
        </ng-container>
      </div>

      <ng-container *ngSwitchCase="ArticleBodyType.SubsequentDossierNewsFeed">
        <app-article-dossier [dossier]="element?.details[0]?.value" [excludedArticleSlug]="article?.slug"></app-article-dossier>
      </ng-container>

      <ng-container *ngSwitchCase="ArticleBodyType.DoubleArticleRecommendation">
        <nso-articles-related-content [data]="doubleArticleRecommendation(element.details)"> </nso-articles-related-content>
      </ng-container>
    </ng-container>
  </ng-container>
</ng-template>
