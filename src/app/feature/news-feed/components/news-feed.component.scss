@use 'shared' as *;

:host {
  display: block;
  margin: 40px 0;

  .dossier {
    &-title {
      font-size: 48px;
      line-height: 53px;
      font-family: var(--kui-font-condensed);
      color: var(--kui-gray-900);
      text-transform: uppercase;
    }

    &-body {
      margin: 20px 0;
      background-color: var(--kui-gray-100);
      padding: 40px;

      @include media-breakpoint-down(md) {
        padding: 20px;
      }
    }

    &-divider {
      background-color: var(--kui-gray-200);
      margin: 40px 0;
      width: 100%;
      height: 1px;

      &.vertical {
        margin: 0;
        width: 1px;
        height: 20px;
      }
    }
  }

  .article {
    &-title {
      font-size: 22px;
      line-height: 28.6px;
      font-family: var(--kui-font-condensed);
      color: var(--kui-gray-900);
      text-transform: uppercase;
      margin-bottom: 10px;

      &:hover {
        color: var(--kui-red-400);
      }
    }

    &-lead {
      margin-bottom: 10px;
    }

    &-meta,
    &-author-list {
      display: flex;
      gap: 10px;
      flex-wrap: wrap;
    }
    &-author {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
      gap: 10px;

      @include media-breakpoint-down(md) {
        margin-bottom: 6px;
        gap: 5px;
      }

      &-name {
        line-height: 21px;
        color: var(--kui-red-400);
      }
    }

    &-avatar {
      width: 48px;
      height: 48px;
      object-fit: cover;
      border-radius: 48px;
      border: 1px solid var(--kui-gray-200);
    }

    &-column {
      font-weight: 700;
      color: var(--kui-red-400);
      line-height: 19px;
    }

    &-publish-date {
      line-height: 25px;
      color: var(--kui-gray-550);
    }

    &-tags {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      margin-bottom: 24px;
      gap: 8px;
    }

    &-tag {
      color: var(--kui-red-400);
      font-weight: 700;
      line-height: 19px;
    }

    &-img {
      width: 100%;
      aspect-ratio: 16/9;
      object-fit: cover;
      margin-bottom: 4px;
    }
  }

  nso-voting,
  nso-quiz,
  nso-simple-button {
    display: block;
    margin-bottom: 20px;
  }

  nso-simple-button.olympics-news-feed {
    margin-bottom: 0;
    padding: 24px 0px;

    @include media-breakpoint-down(sm) {
      padding: 16px 0px;
    }
  }

  nso-spinner {
    display: block;
    margin: 40px auto;
  }

  // Olimpia
  &.is-olympics {
    .dossier-title {
      color: #009ce0;
    }

    .article-tag,
    .article-title {
      color: #022366;
    }
  }
}
