import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute, Params, RouterLink } from '@angular/router';
import { takeUntil } from 'rxjs/operators';
import { Subject } from 'rxjs';
import { ApiService, createNSOTitle, defaultMetaInfo, NsoSimpleButtonComponent } from '../../../shared';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import { NgIf } from '@angular/common';
import { createCanonicalUrlForPageablePage } from '@trendency/kesma-ui';

@Component({
  selector: 'app-confirm-registration',
  templateUrl: './confirm-registration.component.html',
  styleUrls: ['./confirm-registration.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, RouterLink, NsoSimpleButtonComponent],
})
export class ConfirmRegistrationComponent implements OnInit, OnDestroy {
  isLoading = true;
  error: string | null = null;

  unsubscribe$: Subject<void> = new Subject<void>();

  constructor(
    private readonly route: ActivatedRoute,
    private readonly apiService: ApiService,
    private readonly cdr: ChangeDetectorRef,
    private readonly seo: SeoService
  ) {}

  ngOnInit(): void {
    this.route.queryParams.pipe(takeUntil(this.unsubscribe$)).subscribe((params: Params) => {
      const id = params['id'];
      const hashedEmail = params['hashedEmail'];
      const expiration = params['expiration'];
      const signature = params['signature'];

      this.setMetaData();

      if (!(id && hashedEmail && expiration && signature)) {
        this.isLoading = false;
        this.error = 'Hiba, érvénytelen link, kérlek ellenőrizd a böngészőben megadott hivatkozást!';
        this.cdr.detectChanges();
        return;
      }

      this.verifyRegister(id, hashedEmail, expiration, signature);
    });
  }

  verifyRegister(id: string, hashedEmail: string, expiration: string, signature: string): void {
    this.apiService.verifyRegister(id, hashedEmail, expiration, signature).subscribe({
      next: () => {
        this.isLoading = false;
        this.error = null;
        this.cdr.detectChanges();
      },
      error: () => {
        this.isLoading = false;
        this.error = 'Hiba, a regisztráció megerősítésére kiküldött link érvénytelen vagy lejárt, kérlek próbáld újra!';
        this.cdr.detectChanges();
      },
    });
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage('regisztracio/megerosites');
    if (canonical) this.seo.updateCanonicalUrl(canonical);
    const title = createNSOTitle('Profil véglegesítése');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    };
    this.seo.setMetaData(metaData);
  }
}
