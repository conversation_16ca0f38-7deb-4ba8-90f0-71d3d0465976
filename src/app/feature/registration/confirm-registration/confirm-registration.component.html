<section class="registration">
  <div class="wrapper">
    <p class="registration-text" *ngIf="isLoading">Kérlek várj...</p>
    <div class="nso-form-general-error registration-error" *ngIf="!isLoading && error">
      {{ error }}
    </div>
    <p class="registration-error-navigation" *ngIf="!isLoading && error">
      Vissza a <a class="registration-text-link" [routerLink]="['/', 'bejelentkezes']">bejelentkezésre</a> vagy a
      <a class="registration-text-link" [routerLink]="['/', 'regisztracio']">regisztrációs</a> oldalra.
    </p>
    <div class="registration-success" *ngIf="!isLoading && !error">
      <img src="/assets/images/icons/icon-success.svg" alt="Sikeres regisztráció" />
      <h1>Sikeres regisztráció</h1>
      <div class="registration-gray-text">
        A fiókját sikeresen létrehoztuk. Mostantól a <a [routerLink]="'/my-nso'">Profilom</a> menüpont alatt testreszabhatja kedvenceit.
      </div>
      <nso-simple-button class="w-100" round="round" routerLink="/bejelentkezes">BEJELENTKEZÉS</nso-simple-button>
    </div>
  </div>
</section>
