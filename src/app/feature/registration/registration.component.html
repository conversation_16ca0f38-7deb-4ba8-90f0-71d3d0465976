<section class="registration">
  <div class="wrapper">
    <!-- Registration form -->
    <ng-container *ngIf="!isSubmitted">
      <ng-container *ngIf="allowedLoginMethods$ | async as allowedLoginMethods">
        <form (ngSubmit)="register()" *ngIf="formGroup" [formGroup]="formGroup" class="nso-form">
          <h1>Fiók létrehozása</h1>

          <div class="registration-social-buttons">
            <nso-social-login-buttons
              (facebookClickEvent)="registerWithSocialProvider(SocialProvider.FACEBOOK)"
              (googleClickEvent)="registerWithSocialProvider(SocialProvider.GOOGLE)"
              [isFacebookAllowed]="allowedLoginMethods?.[SocialProvider.FACEBOOK]"
              [isGoogleAllowed]="allowedLoginMethods?.[SocialProvider.GOOGLE]"
            ></nso-social-login-buttons>
          </div>

          <div
            *ngIf="(allowedLoginMethods?.[SocialProvider.FACEBOOK] || allowedLoginMethods?.[SocialProvider.GOOGLE]) && allowedLoginMethods.email"
            class="registration-social-text"
          >
            Vagy csatlakozás a következővel
          </div>

          <ng-container *ngIf="allowedLoginMethods.email">
            <div class="nso-form-row">
              <kesma-form-control>
                <input class="nso-form-input" formControlName="username" id="username" placeholder="Felhasználónév " type="text" />
              </kesma-form-control>
              <small class="nso-form-small"
                >A felhasználónévnek legalább 6 karakterből kell állnia, és a következőket tartalmazhatja: kis- és nagybetű, szám, kötőjel, alsóvonás.</small
              >
            </div>
            <div class="nso-form-row">
              <kesma-form-control>
                <input class="nso-form-input" formControlName="email" id="email" placeholder="E-mail cím" type="text" />
              </kesma-form-control>
            </div>
            <div class="nso-form-row">
              <kesma-form-control [hideErrors]="true">
                <div class="nso-form-input-password">
                  <input [type]="showPassword ? 'text' : 'password'" class="nso-form-input" formControlName="password" id="password" placeholder="Jelszó" />
                  <img
                    (click)="showPassword = !showPassword"
                    [src]="showPassword ? '/assets/images/icons/icon-no-eye.svg' : '/assets/images/icons/icon-eye.svg'"
                    alt="Jelszó megtekintése"
                    class="nso-form-input-password-img"
                  />
                </div>
              </kesma-form-control>
              <nso-password-strength-meter
                (remainingErrors)="checkRemainingErrors($event)"
                [errorLabels]="errorLabels"
                [errorsToWatch]="errorsToWatch"
                [passwordInput]="passwordControl"
              ></nso-password-strength-meter>
            </div>
            <div class="registration-checkboxes">
              <kesma-form-control class="checkbox">
                <label class="nso-form-checkbox" for="newsletter">
                  <input formControlName="newsletter" id="newsletter" type="checkbox" />
                  <span>Feliratkozom az NSO hírlevélre</span>
                </label>
              </kesma-form-control>
              <kesma-form-control class="checkbox">
                <label class="nso-form-checkbox" for="terms">
                  <input formControlName="terms" id="terms" type="checkbox" />
                  <span
                    >Elolvastam és elfogadom a
                    <a [routerLink]="['/felhasznalasi-feltetelek']" class="nso-form-checkbox-link" target="_blank">felhasználási feltételeket</a>
                    és az
                    <a [routerLink]="['/adatvedelmi-tajekoztato']" class="nso-form-checkbox-link" target="_blank">adatkezelési tájékoztatóban</a>
                    foglaltakat</span
                  >
                </label>
              </kesma-form-control>
            </div>
            <div *ngIf="error" class="nso-form-general-error">
              {{ error }}
            </div>
            <div class="registration-action">
              <nso-simple-button [disabled]="isLoading" [isSubmit]="true" class="w-100" round="round">
                {{ isLoading ? 'Kérem várjon...' : 'REGISZTRÁCIÓ' }}
              </nso-simple-button>
            </div>
          </ng-container>
          <div class="registration-has-profile">Már van fiókja? <a class="registration-has-profile-link" routerLink="/bejelentkezes">Bejelentkezés</a></div>
        </form>
      </ng-container>
    </ng-container>

    <!-- Submitted registration form -->
    <div *ngIf="isSubmitted" class="registration-submitted">
      <img alt="E-mail ellenőrzése" src="/assets/images/icons/icon-paper-plane.svg" />
      <h1>E-mail ellenőrzése</h1>
      <div class="registration-gray-text">Az aktiválásához szükséges adatokat e-mailben küldtük el. Kérjük ellenőrizze e-mail fiókját.</div>
      <nso-simple-button class="w-100" round="round" routerLink="/">TOVÁBB A FŐOLDALRA</nso-simple-button>
    </div>
  </div>
</section>
