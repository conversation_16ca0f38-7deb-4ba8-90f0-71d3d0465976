import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute, Params, Router, RouterLink } from '@angular/router';
import { catchError, switchMap, takeUntil } from 'rxjs/operators';
import { Subject, throwError } from 'rxjs';
import { AbstractControl, ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { HttpErrorResponse } from '@angular/common/http';
import { BackendFormErrors, createCanonicalUrlForPageablePage, KesmaFormControlComponent, usernameValidator } from '@trendency/kesma-ui';
import { AuthService, createNSOTitle, defaultMetaInfo, NsoSimpleButtonComponent, SecureApiService, SocialProvider } from '../../../shared';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import { NgIf } from '@angular/common';

@Component({
  selector: 'app-finalize-registration',
  templateUrl: './finalize-registration.component.html',
  styleUrls: ['./finalize-registration.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, RouterLink, ReactiveFormsModule, KesmaFormControlComponent, NsoSimpleButtonComponent],
})
export class FinalizeRegistrationComponent implements OnInit, OnDestroy {
  formGroup: UntypedFormGroup;
  isLoading = true;
  error: string | null = null;

  isFormLoading = false;
  formError: string | null = null;

  unsubscribe$: Subject<void> = new Subject<void>();

  constructor(
    private readonly route: ActivatedRoute,
    private readonly cdr: ChangeDetectorRef,
    private readonly formBuilder: UntypedFormBuilder,
    private readonly authService: AuthService,
    private readonly secureApiService: SecureApiService,
    private readonly router: Router,
    private readonly seo: SeoService
  ) {}

  ngOnInit(): void {
    this.route.queryParams.pipe(takeUntil(this.unsubscribe$)).subscribe((params: Params) => {
      if (params['state'] && params['code']) {
        this.verifyOAuthCode(params['state'] as SocialProvider, params['code']);
      } else {
        this.isLoading = false;
        this.error = 'A regisztráció / bejelentkezés folyamata megszakadt, kérlek próbáld újra!';
        this.setMetaData();
        this.cdr.detectChanges();
      }
    });
  }

  verifyOAuthCode(provider: SocialProvider, responseCode: string): void {
    this.authService.authenticateWithSocialProvider(provider, responseCode).subscribe({
      next: (isRegistrationFinished: boolean) => {
        if (isRegistrationFinished) {
          this.router.navigate(['/profil']);
        } else {
          this.isLoading = false;
          this.setMetaData();
          this.initForm();
          this.cdr.detectChanges();
        }
      },
      error: () => {
        this.isLoading = false;
        this.error = 'Hiba történt a külső forrásból kapott adatok vizsgálata során, kérlek próbáld újra!';
        this.cdr.detectChanges();
      },
    });
  }

  initForm(): void {
    this.formGroup = this.formBuilder.group({
      username: [null, [Validators.required, usernameValidator]],
      newsletter: [false],
      terms: [false, Validators.requiredTrue],
    });
  }

  finalizeRegistration(): void {
    Object.values(this.formGroup.controls).forEach((control: AbstractControl) => {
      control.markAsTouched();
      control.updateValueAndValidity();
    });

    if (!this.formGroup.valid) {
      return;
    }

    this.formError = null;
    this.isFormLoading = true;

    this.secureApiService
      .finishRegister(this.formGroup.value)
      .pipe(
        catchError((response: HttpErrorResponse) => {
          const backendErrors = response.error as BackendFormErrors;
          let isErrorHandled = false;
          if (backendErrors?.form?.errors?.children) {
            for (const [errorKey, value] of Object.entries(backendErrors.form.errors.children)) {
              // User with the same username is already registered
              if (errorKey === 'userName' && !!value.errors) {
                this.formGroup.get('username')?.setErrors({ usernameInUse: true });
                isErrorHandled = true;
              }
            }
          }
          if (!isErrorHandled) {
            this.formError = 'Ismeretlen hiba!';
          }
          this.isFormLoading = false;
          this.cdr.detectChanges();
          return throwError(() => 'Error');
        }),
        switchMap(() => this.authService.isAuthenticated())
      )
      .subscribe(() => {
        this.router.navigate(['/profil']);
      });
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage('regisztracio/veglegesites');
    if (canonical) this.seo.updateCanonicalUrl(canonical);
    const title = createNSOTitle('Profil véglegesítése');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    };
    this.seo.setMetaData(metaData);
  }
}
