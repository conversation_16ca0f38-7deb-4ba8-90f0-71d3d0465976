<section class="registration">
  <div class="wrapper">
    <h1 *ngIf="!isLoading">
      {{ isLoading || (!isLoading && error) ? 'Regisztráció / Bejelentkezés' : 'Regisztráció véglegesítése' }}
    </h1>
    <p *ngIf="isLoading" class="registration-text">Kérem várjon...</p>
    <div *ngIf="!isLoading && error" class="nso-form-general-error registration-error">
      {{ error }}
    </div>
    <p *ngIf="!isLoading && error" class="registration-error-navigation">
      Vissza a <a [routerLink]="['/', 'bejelentkezes']" class="registration-text-link">bejelentkezésre</a> vagy a
      <a [routerLink]="['/', 'regisztracio']" class="registration-text-link">regisztrációs</a> oldalra.
    </p>
    <ng-container *ngIf="!isLoading && !error">
      <p class="registration-info-text">
        NSO fiókját sikeresen összekapcsoltuk közösségi profiljával! K<PERSON>rjük, a regisztráció véglegesítéséhez ellenőrizze az alábbi adatokat!
      </p>
      <form (ngSubmit)="finalizeRegistration()" *ngIf="formGroup" [formGroup]="formGroup" class="register-form">
        <div class="nso-form-row">
          <kesma-form-control>
            <label class="nso-form-label" for="username">Kérjük adjon meg egy felhasználónevet</label>
            <input class="nso-form-input" formControlName="username" id="username" placeholder="Felhasználónév " type="text" />
          </kesma-form-control>
          <small class="nso-form-small"
            >A felhasználónévnek legalább 6 karakterből kell állnia, és a következőket tartalmazhatja: kis- és nagybetű, szám, kötőjel, alsóvonás.</small
          >
        </div>
        <div class="registration-checkboxes">
          <kesma-form-control class="checkbox">
            <label class="nso-form-checkbox" for="newsletter">
              <input formControlName="newsletter" id="newsletter" type="checkbox" />
              <span>Feliratkozom az NSO hírlevélre</span>
            </label>
          </kesma-form-control>
          <kesma-form-control class="checkbox">
            <label class="nso-form-checkbox" for="terms">
              <input formControlName="terms" id="terms" type="checkbox" />
              <span
                >Elolvastam és elfogadom a
                <a [routerLink]="['/felhasznalasi-feltetelek']" class="nso-form-checkbox-link" target="_blank">felhasználási feltételeket</a>
                és az
                <a [routerLink]="['/adatvedelmi-tajekoztato']" class="nso-form-checkbox-link" target="_blank">adatkezelési tájékoztatóban</a>
                foglaltakat</span
              >
            </label>
          </kesma-form-control>
        </div>
        <div *ngIf="error" class="nso-form-general-error">
          {{ error }}
        </div>
        <div class="registration-action">
          <nso-simple-button [disabled]="isFormLoading" [isSubmit]="true" class="w-100" round="round">
            {{ isFormLoading ? 'Kérem várjon...' : 'REGISZTRÁCIÓ VÉGLEGESÍTÉSE' }}
          </nso-simple-button>
        </div>
      </form>
    </ng-container>
  </div>
</section>
