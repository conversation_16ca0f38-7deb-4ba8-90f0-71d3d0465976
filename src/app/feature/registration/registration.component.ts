import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, OnInit } from '@angular/core';
import { AbstractControl, FormControl, ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import {
  BackendFormErrors,
  createCanonicalUrlForPageablePage,
  emailValidator,
  KesmaFormControlComponent,
  passwordContainsCharsAndNumbers,
  passwordContainsSpecial,
  usernameValidator,
} from '@trendency/kesma-ui';
import { HttpErrorResponse } from '@angular/common/http';
import { AsyncPipe, DOCUMENT, NgIf, ViewportScroller } from '@angular/common';
import { Router, RouterLink } from '@angular/router';
import { IMetaData, SeoService, UtilService } from '@trendency/kesma-core';
import { ReCaptchaV3Service } from 'ngx-captcha';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';
import {
  ApiService,
  AuthService,
  BackendAllowedLoginMethodsResponse,
  createNSOTitle,
  defaultMetaInfo,
  errorLabels,
  errorsToWatch,
  NsoPasswordStrengthMeterComponent,
  NsoSimpleButtonComponent,
  NsoSocialLoginButtonsComponent,
  SocialProvider,
} from '../../shared';

@Component({
  selector: 'app-registration',
  templateUrl: './registration.component.html',
  styleUrls: ['./registration.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgIf,
    AsyncPipe,
    ReactiveFormsModule,
    NsoSocialLoginButtonsComponent,
    KesmaFormControlComponent,
    NsoPasswordStrengthMeterComponent,
    RouterLink,
    NsoSimpleButtonComponent,
  ],
})
export class RegistrationComponent implements OnInit {
  formGroup: UntypedFormGroup;
  showPassword = false;
  isLoading = false;
  isSubmitted = false;
  error: string | null = null;
  errorsToWatch = errorsToWatch;
  errorLabels = errorLabels;
  remainingErrors: string[];
  SocialProvider = SocialProvider;
  allowedLoginMethods$: Observable<BackendAllowedLoginMethodsResponse> = this.apiService.getAllowedLoginMethods();

  constructor(
    private readonly apiService: ApiService,
    private readonly formBuilder: UntypedFormBuilder,
    private readonly cdr: ChangeDetectorRef,
    private readonly viewportScroller: ViewportScroller,
    private readonly reCaptchaV3Service: ReCaptchaV3Service,
    private readonly seo: SeoService,
    private readonly authService: AuthService,
    @Inject(DOCUMENT) private readonly document: Document,
    private readonly utilsService: UtilService,
    private readonly router: Router
  ) {}

  ngOnInit(): void {
    this.initForm();
    this.setMetaData();
  }

  initForm(): void {
    this.formGroup = this.formBuilder.group({
      email: [null, [Validators.required, emailValidator]],
      username: [null, [Validators.required, usernameValidator]],
      password: [null, [Validators.required, Validators.minLength(6), passwordContainsSpecial, passwordContainsCharsAndNumbers]],
      newsletter: [false],
      terms: [false, Validators.requiredTrue],
    });
  }

  get passwordControl(): FormControl {
    return this.formGroup.get('password') as FormControl;
  }

  checkRemainingErrors(errors: string[]): void {
    this.remainingErrors = errors;
  }

  register(): void {
    Object.values(this.formGroup.controls).forEach((control: AbstractControl) => {
      control.markAsTouched();
      control.updateValueAndValidity();
    });

    if (!this.formGroup.valid || this.remainingErrors?.length) {
      return;
    }

    this.error = null;
    this.isLoading = true;

    this.reCaptchaV3Service.execute(
      environment.googleSiteKey ?? '',
      'app_publicapi_portal_user_register',
      (recaptchaToken: string) => {
        this.apiService.register(this.formGroup.value, recaptchaToken).subscribe({
          next: () => {
            this.isSubmitted = true;
            this.isLoading = false;
            this.formGroup.reset();
            this.viewportScroller.scrollToPosition([0, 0]);
            this.cdr.detectChanges();
          },
          error: (response: HttpErrorResponse) => {
            const backendErrors = response.error as BackendFormErrors;
            let isErrorHandled = false;
            if (backendErrors?.form?.errors?.children) {
              for (const [errorKey, value] of Object.entries(backendErrors.form.errors.children)) {
                // User with the same email is already registered
                if (errorKey === 'email' && !!value.errors) {
                  this.formGroup.get('email')?.setErrors({ emailInUse: true });
                  isErrorHandled = true;
                }
                // User with the same username is already registered
                if (errorKey === 'userName' && !!value.errors) {
                  this.formGroup.get('username')?.setErrors({ usernameInUse: true });
                  isErrorHandled = true;
                }
              }
            }
            if (!isErrorHandled) {
              this.error = 'Ismeretlen hiba!';
            }
            this.isLoading = false;
            this.cdr.detectChanges();
          },
        });
      },
      {
        useGlobalDomain: false,
      },
      () => {
        this.error = 'Captcha: Robot ellenőrzés hiba!';
        this.isLoading = false;
        this.cdr.detectChanges();
      }
    );
  }

  registerWithSocialProvider(provider: SocialProvider): void {
    if (this.utilsService.isBrowser()) {
      this.document.location.href = this.authService.getSocialProviderAuthUrl(provider);
    }
  }

  confirmPopup(): void {
    this.router.navigate(['/']);
  }

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage('regisztracio');
    if (canonical) this.seo.updateCanonicalUrl(canonical);
    const title = createNSOTitle('Regisztráció');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    };
    this.seo.setMetaData(metaData);
  }
}
