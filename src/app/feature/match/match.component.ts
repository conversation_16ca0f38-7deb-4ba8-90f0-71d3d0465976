import { ChangeDetectionStrategy, Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute, RouterOutlet } from '@angular/router';
import { Subject, takeUntil, tap } from 'rxjs';
import { NsoScoretripHeaderLineComponent, ScoreTripHeaderLineDefinitions, SubPageTabComponent, TabType } from '../../shared';
import { RadioService } from 'src/app/shared/services/radio.service';
import { Schedule } from '../team-page/team-end-page.definitions';
import { createNSOTitle } from '../../shared';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import { defaultMetaInfo } from '../../shared';
import { LiveService } from './components/live/live.service';
import { NgIf } from '@angular/common';
import { createCanonicalUrlForPageablePage } from '@trendency/kesma-ui';

export const NON_LIVING_STATUSES = ['ended', 'ready', 'not_started', 'postponed'];
export const FUTURE_STATUSES = ['not_started', 'postponed'];
@Component({
  selector: 'app-match',
  templateUrl: './match.component.html',
  styleUrls: ['./match.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NsoScoretripHeaderLineComponent, SubPageTabComponent, RouterOutlet],
  providers: [LiveService],
})
export class MatchComponent implements OnInit, OnDestroy {
  destroy$ = new Subject();

  schedule: Schedule;
  header: ScoreTripHeaderLineDefinitions;
  pageType = 'match';
  stripTitle: string;
  showInWebview = false;

  championshipTabs: TabType[] = [
    { tabName: 'Elemzés', tabSlug: 'elemzes' },
    { tabName: 'Percről-percre', tabSlug: 'elo' },
    { tabName: 'Felállás', tabSlug: 'felallas' },
    { tabName: 'Statisztikák', tabSlug: 'stat' },
    { tabName: 'Közösség', tabSlug: 'kozosseg' },
  ];

  constructor(
    private readonly route: ActivatedRoute,
    public readonly radioService: RadioService,
    private readonly seo: SeoService,
    private readonly liveService: LiveService
  ) {}

  ngOnInit(): void {
    this.liveService.data$.subscribe((liveData) => {
      const homeScore = liveData?.miscInfo?.scores?.homeScore;
      const awayScore = liveData?.miscInfo?.scores?.awayScore;
      if (homeScore && awayScore) {
        this.header = {
          ...this.header,
          score1: +homeScore || this.header.score1,
          score2: +awayScore || this.header.score2,
          isLive: !NON_LIVING_STATUSES.includes(liveData?.miscInfo?.scheduleStatus as string),
          isFuture: FUTURE_STATUSES.includes(liveData?.miscInfo?.scheduleStatus as string),
        };
      }
    });

    this.setMetaData();
    this.route.data
      .pipe(
        tap((result) => {
          this.showInWebview = result['isMobileApp'];
          this.schedule = result['schedule'];
          this.header = this.mapScheduleToScoreHeader(this.schedule);
        }),
        takeUntil(this.destroy$)
      )
      .subscribe();
  }

  ngOnDestroy(): void {
    this.destroy$.next(true);
    this.destroy$.complete();
  }

  private mapScheduleToScoreHeader(schedule: Schedule): ScoreTripHeaderLineDefinitions {
    const black = '#000000';
    const blue = '#3F74A7';
    this.stripTitle = [schedule.competition.title, schedule.phase?.name, schedule?.round ? `${schedule.round}. forduló` : null, schedule.playoffGroup]
      .filter(Boolean)
      .join(', ');

    return {
      img1: schedule.homeTeam.logo || schedule.homeTeam.team.logo,
      club1: schedule.homeTeam.title,
      color1: black,
      score1: +schedule.homeScore,
      img2: schedule.awayTeam.logo || schedule.awayTeam.team.logo,
      club2: schedule.awayTeam.title,
      color2: blue,
      score2: +schedule.awayScore,
      matchDate: new Date(schedule.scheduleDate.date),
      scoreTripTitle: {
        championship: schedule.competition.title,
        stage: schedule.phase?.name,
        round: `${schedule.round}. forduló`,
        playoffGroup: `${schedule.playoffGroup}`,
      },
      isLive: !NON_LIVING_STATUSES.includes(schedule.scheduleStatus as string),
      isFuture: FUTURE_STATUSES.includes(schedule.scheduleStatus as string),
    };
  }

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage(`merkozes/${this.route.snapshot.data['schedule']?.slug}`);
    if (canonical) this.seo.updateCanonicalUrl(canonical);
    const scheduleData = this.route.snapshot.data?.['schedule'];
    const matchTitle = `${scheduleData?.homeTeam?.title} - ${scheduleData?.awayTeam?.title}`;
    const title = createNSOTitle(matchTitle);
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    };
    this.seo.setMetaData(metaData);
  }
}
