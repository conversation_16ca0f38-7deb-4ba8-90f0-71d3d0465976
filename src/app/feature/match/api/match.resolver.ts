import { Injectable } from '@angular/core';
import { Router, ActivatedRouteSnapshot } from '@angular/router';
import { Observable, switchMap, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { MatchService } from './match.service';
import { Schedule } from '../../team-page/team-end-page.definitions';

@Injectable()
export class MatchResolver {
  constructor(
    private readonly matchService: MatchService,
    private readonly router: Router
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<Schedule> {
    const slug: string = route.params['slug'];
    return this.matchService.getSchedule(slug).pipe(
      map(({ data }) => data),
      switchMap((schedule) =>
        this.matchService.getLive(slug).pipe(
          map(
            ({ data }) =>
              ({
                ...schedule,
                ...data,
              }) as Schedule
          )
        )
      ),
      catchError((error) => {
        this.router
          .navigate(['/', '404'], {
            skipLocationChange: true,
          })
          .then();
        return throwError(error);
      })
    );
  }
}
