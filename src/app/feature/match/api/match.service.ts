import { Injectable } from '@angular/core';
import { ReqService } from '@trendency/kesma-core';
import { Observable } from 'rxjs';
import { Schedule } from '../../team-page/team-end-page.definitions';
import { ApiListResult, ApiResult } from '@trendency/kesma-ui';
import { ChampionshipSchedule } from '../../../shared';

@Injectable({
  providedIn: 'root',
})
export class MatchService {
  constructor(private readonly reqService: ReqService) {}

  getSchedule(scheduleSlug: string): Observable<ApiResult<ChampionshipSchedule>> {
    return this.reqService.get(`/sport/schedule/${scheduleSlug}`);
  }

  getLive(scheduleSlug: string): Observable<ApiResult<Schedule>> {
    return this.reqService.get(`/sport/schedule/${scheduleSlug}/live`, {
      params: {
        timestamp: Date.now(),
      },
    });
  }

  getVersus(teamAId: string, teamBId: string): Observable<ApiListResult<Schedule>> {
    return this.reqService.get(`/sport/schedule/versus/${teamAId}/${teamBId}`);
  }

  getScheduleSocial(scheduleSlug: string): Observable<ApiListResult<{ id: string; minute: string; eventText: string }>> {
    return this.reqService.get(`/sport/schedule/${scheduleSlug}/social-live`);
  }
}
