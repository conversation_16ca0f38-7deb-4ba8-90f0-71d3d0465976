import { Routes } from '@angular/router';
import { MatchStatsComponent } from './components/match-stats/match-stats.component';
import { MatchAnalysisComponent } from './components/match-analysis/match-analysis.component';
import { LineupTabComponent } from './components/lineup-tab/lineup-tab.component';
import { LineupResolver } from './components/lineup-tab/lineup.resolver';
import { LiveComponent } from './components/live/live.component';
import { LiveResolver } from './components/live/live.resolver';
import { MatchComponent } from './match.component';
import { MatchStatResolver } from './components/match-stats/match-stat.resolver';
import { MatchResolver } from './api/match.resolver';
import { MatchAnalysisResolver } from './components/match-analysis/match-analysis.resolver';
import { MatchSocialResolver } from './components/social/social.resolver';
import { MatchSocialComponent } from './components/social/social.component';

export const matchRouting: Routes = [
  {
    path: '',
    component: MatchComponent,
    resolve: { schedule: MatchResolver },
    providers: [MatchResolver],
    children: [
      {
        path: 'elemzes',
        component: MatchAnalysisComponent,
        resolve: { data: MatchAnalysisResolver },
        providers: [MatchAnalysisResolver],
      },
      {
        path: 'elo',
        component: LiveComponent,
        resolve: { data: LiveResolver },
        providers: [LiveResolver],
      },
      {
        path: 'felallas',
        component: LineupTabComponent,
        resolve: { data: LineupResolver },
        providers: [LineupResolver],
      },
      {
        path: 'stat',
        component: MatchStatsComponent,
        resolve: { data: MatchStatResolver },
        providers: [MatchStatResolver],
      },
      {
        path: 'kozosseg',
        component: MatchSocialComponent,
        resolve: { data: MatchSocialResolver },
        providers: [MatchSocialResolver],
      },
    ],
  },
];
