<section [class.mobile-app-match]="showInWebview">
  <div class="wrapper">
    <!--
    <nso-sport-radio
      [playing]="(radioService.isPlaying$ | async) || false"
      (playingChange)="radioService.radioClick()"
      (volumeChange)="radioService.volumeChange($event)">
    </nso-sport-radio>
    -->
    <ng-container *ngIf="header">
      <div class="strip-title" *ngIf="stripTitle">{{ stripTitle }}</div>
      <nso-scoretrip-header-line [data]="header"></nso-scoretrip-header-line>
    </ng-container>
    <div class="page-tabs">
      <app-sub-page-tab [pageTabs]="championshipTabs" [isChampionshipPage]="false" [pageType]="pageType"></app-sub-page-tab>
    </div>
  </div>
</section>

<section class="sub-page" [class.mobile-app-match]="showInWebview">
  <div class="wrapper">
    <router-outlet></router-outlet>
  </div>
</section>
