@use 'shared' as *;

:host {
  display: block;
  margin-top: 30px;
  width: 100%;

  ::ng-deep {
    h2 {
      font-size: 24px;
    }
  }

  .mobile-app-match {
    margin: 0;
    .wrapper {
      width: 100%;
      max-width: none;
    }
  }

  .page-tabs {
    @extend %full-width;
    padding: 0 80px;

    @include media-breakpoint-down(md) {
      padding: 0 10px;
    }
  }

  .sub-page {
    @extend %full-width;
    padding: 40px 0;
  }
}

.strip-title {
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--kui-gray-200);
  height: 42px;
  margin-top: 40px;
  font-size: 16px;
  gap: 5px;
  @extend %full-width;
}

%full-width {
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
}
