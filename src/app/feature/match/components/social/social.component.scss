@use 'shared' as *;

:host {
  display: block;
  @include media-breakpoint-down(sm) {
    max-width: calc(100% - 2px);
  }
}

.twitter-follow {
  max-width: 848px;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--kui-gray-200);
  margin: auto auto 75px;
}
.social-item {
  width: 100%;
  max-width: 848px;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  margin: auto;
  gap: 60px;

  @include media-breakpoint-down(md) {
    flex-direction: column;
    gap: 30px;
    align-items: center;
  }
  &-minute {
    width: 70px;
    min-width: 70px;
    font-weight: 700;
    font-size: 24px;
    @include media-breakpoint-down(md) {
      width: 100%;
      max-width: 550px;
    }
  }
  &-wysiwyg {
    flex-grow: 1;
  }
}
