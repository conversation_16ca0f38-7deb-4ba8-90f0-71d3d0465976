import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot } from '@angular/router';
import { Observable } from 'rxjs';
import { MatchService } from '../../api/match.service';
import { SocialLiveItem } from './social.definitions';
import { map } from 'rxjs/operators';

@Injectable()
export class MatchSocialResolver {
  constructor(private readonly matchService: MatchService) {}

  resolve(route: ActivatedRouteSnapshot): Observable<SocialLiveItem[]> {
    const scheduleSlug = route.parent?.data['schedule'].slug;

    return this.matchService.getScheduleSocial(scheduleSlug).pipe(
      map(({ data }) =>
        data.map((item: { id: string; minute: string; eventText: string }) => ({
          id: item.id,
          minute: item.minute,
          eventText: item.eventText,
        }))
      )
    );
  }
}
