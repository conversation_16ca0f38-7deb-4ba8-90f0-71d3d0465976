import { Component, ChangeDetectionStrategy, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { map } from 'rxjs/operators';
import { Observable } from 'rxjs';
import { SocialLiveItem } from './social.definitions';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import { defaultMetaInfo } from 'src/app/shared/constants/meta.consts';
import { createNSOTitle, NsoTwitterFollowComponent, NsoWysiwygBoxComponent } from '../../../../shared';
import { AsyncPipe, NgForOf } from '@angular/common';
import { createCanonicalUrlForPageablePage } from '@trendency/kesma-ui';

@Component({
  selector: 'app-match-social',
  templateUrl: './social.component.html',
  styleUrls: ['./social.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NsoTwitterFollowComponent, NgForOf, AsyncPipe, NsoWysiwygBoxComponent],
})
export class MatchSocialComponent implements OnInit {
  constructor(
    private readonly route: ActivatedRoute,
    private readonly seo: SeoService
  ) {}

  data$: Observable<SocialLiveItem[]> = this.route.data.pipe(map(({ data }) => data));

  ngOnInit(): void {
    this.setMetaData();
  }

  trackById(_: number, item: SocialLiveItem): string {
    return item.id;
  }

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage('merkozes/kozosseg');
    if (canonical) this.seo.updateCanonicalUrl(canonical);
    const title = createNSOTitle('Mérkőzés - Közösség');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    };
    this.seo.setMetaData(metaData);
  }
}
