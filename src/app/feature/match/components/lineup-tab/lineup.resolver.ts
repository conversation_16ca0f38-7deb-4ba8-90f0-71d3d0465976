import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot } from '@angular/router';
import { forkJoin, map, Observable, of } from 'rxjs';
import { LineupResolverData, LineupTable } from './lineup.definitions';
import { LiveEvent, TeamPlayer } from '../../../team-page/team-end-page.definitions';
import { ChampionshipSchedule, FootballPitchData, LeagueTeam, Player, PlayerActions, PlayerBase, PositionType, TeamDetails } from '../../../../shared';

@Injectable()
export class LineupResolver {
  resolve(route: ActivatedRouteSnapshot): Observable<LineupResolverData> {
    const schedule$ = of(route.parent?.data?.['schedule']);
    const pitchData$: Observable<FootballPitchData> = schedule$.pipe(
      map((data: ChampionshipSchedule) => ({
        awayTeam: { lineUp: data.awayLineupForm as string, players: this.getPlayerByTeam(data.players, data.awayTeam?.id) },
        homeTeam: { lineUp: data.homeLineupForm as string, players: this.getPlayerByTeam(data.players, data.homeTeam?.id) },
      }))
    );
    const tableData$: Observable<LineupTable[]> = schedule$.pipe(map((data) => this.mapTableData(data)));

    return forkJoin({ schedule: pitchData$, table: tableData$ });
  }

  getPlayerByTeam(players: Player[] | undefined, teamId: string): Player[] {
    return players?.filter((player) => player.competitionTeamId === teamId)?.sort((a, b) => Number(a.fieldPosition) - Number(b.fieldPosition)) as Player[];
  }

  private mapTableData(data: ChampionshipSchedule & { liveEvents: LiveEvent[] }): LineupTable[] {
    if (!data.players) {
      return [];
    }

    const result = this.gatherTeamsData(data);
    this.gatherPlayerData(data, result);

    // Map events:
    for (const event of data?.liveEvents || []) {
      if (!event.competitionTeamPlayer || !event.competitionTeam) {
        continue;
      }

      const player = this.extractEventPlayer(event, result);

      if (!player) {
        continue;
      }

      this.handleLiveEvent(event, player);
    }

    return Object.values(result);
  }

  private handleLiveEvent(event: LiveEvent, player: PlayerBase): void {
    switch (event.scheduleEvent?.title?.toLowerCase()) {
      case 'gól':
        player.g++;
        player.actions.push(PlayerActions.GOAL);
        break;
      case 'sárgalap':
        player.fc++;
        player.actions.push(PlayerActions.YELLOWCARD);
        break;
      case 'piroslap':
        player.fc++;
        player.actions.push(PlayerActions.REDCARD);
        break;
      case 'csere':
        player.hasSubbed = true;
        player.substitionInfo = {
          date: event.minute?.replace(/\D/g, ''), // Remove non-numeric characters (e.g. 'min', apostrophe, etc.)
          newPlayer:
            event.substituteCompetitionTeamPlayer?.publicName ||
            `${event.substituteCompetitionTeamPlayer?.firstName} ${event.substituteCompetitionTeamPlayer?.lastName}`,
        };
        break;
    }
  }

  private extractEventPlayer(event: LiveEvent, result: Record<string, LineupTable>): PlayerBase | undefined {
    const eventPlayer: TeamPlayer = event.competitionTeamPlayer;
    return result[event.competitionTeam.id].tables[this.getPos(eventPlayer)]?.players.find((player) => player.name === this.getPlayerName(eventPlayer));
  }

  private getPlayerName(eventPlayer: TeamPlayer | Player): string {
    return eventPlayer?.publicName || `${eventPlayer?.firstName} ${eventPlayer?.lastName}` || '';
  }

  private gatherPlayerData(data: ChampionshipSchedule & { liveEvents: LiveEvent[] }, result: Record<string, LineupTable>): void {
    for (const player of data.players ?? []) {
      const pos = this.getPos(player);
      if (!result[player.competitionTeamId]?.tables) {
        result = {
          ...result,
          [player.competitionTeamId]: {
            ...result[player.competitionTeamId],
            tables: {},
          },
        };
      }
      if (!result[player.competitionTeamId]?.tables?.[pos]) {
        result[player.competitionTeamId].tables[pos] = {
          playerType: pos === 'kapus' ? PositionType.GOALKEEPER : PositionType.OTHER,
          playerTypeName: pos,
          players: [
            {
              name: this.getPlayerName(player),
              g: 0,
              fc: 0,
              actions: [],
              hasSubbed: false,
            },
          ],
        };
        continue;
      }

      result[player.competitionTeamId]?.tables?.[pos]?.players?.push({
        name: this.getPlayerName(player),
        g: 0,
        fc: 0,
        actions: [],
        hasSubbed: false,
      });
    }
  }

  private gatherTeamsData(data: ChampionshipSchedule & { liveEvents: LiveEvent[] }): Record<string, LineupTable> {
    return {
      [data.homeTeam?.id ?? '']: {
        team: this.mapScheduledEventTeamToTeamDetails(data.homeTeam),
        tables: {},
      },
      [data.awayTeam?.id ?? '']: {
        team: this.mapScheduledEventTeamToTeamDetails(data.awayTeam),
        tables: {},
      },
    };
  }

  private getPos(player: Player | TeamPlayer): string {
    if (this.isTeamPlayer(player)) {
      return player.playerPosition?.toLowerCase() === 'cs' ? 'csere' : player.playerPosition?.toLowerCase();
    }

    return player.fieldPosition?.toLowerCase() === 'cs' ? 'csere' : player.position?.toLowerCase();
  }

  private mapScheduledEventTeamToTeamDetails(homeTeam: LeagueTeam): TeamDetails {
    return {
      teamName: homeTeam.title,
      teamLogo: homeTeam.logo ?? '', // Todo: Placeholder?
    };
  }

  private isTeamPlayer(player: Player | TeamPlayer): player is TeamPlayer {
    return (player as TeamPlayer).playerPosition !== undefined;
  }
}
