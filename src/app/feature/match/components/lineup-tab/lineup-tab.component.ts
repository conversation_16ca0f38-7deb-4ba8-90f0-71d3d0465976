import { ChangeDetectionStrategy, Component, OnDestroy, OnInit } from '@angular/core';
import { Subject, takeUntil } from 'rxjs';
import { ActivatedRoute } from '@angular/router';
import { LineUpPositionData, LineupTable } from './lineup.definitions';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import { defaultMetaInfo } from 'src/app/shared/constants/meta.consts';
import { createNSOTitle, FootballPitchData, NsoFootballPitchComponent, NsoPositionTablesComponent } from '../../../../shared';
import { NgForOf, NgIf } from '@angular/common';
import { SidebarComponent } from '../../../layout/components/sidebar/sidebar.component';
import { createCanonicalUrlForPageablePage } from '@trendency/kesma-ui';

@Component({
  selector: 'app-lineup-tab',
  templateUrl: './lineup-tab.component.html',
  styleUrls: ['./lineup-tab.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NsoFootballPitchComponent, NgForOf, NsoPositionTablesComponent, SidebarComponent],
})
export class LineupTabComponent implements OnInit, OnDestroy {
  readonly orderOfPosition = ['kapus', 'védő', 'középpályás', 'támadó', 'csere'];

  constructor(
    private readonly route: ActivatedRoute,
    private readonly seo: SeoService
  ) {}

  private readonly destroy$: Subject<boolean> = new Subject();
  //CHANGE ME: Change this to the column that is associated with the current match/anything...
  categorySlug = '';
  tableData: LineupTable[];
  footballPitchData: FootballPitchData;

  ngOnInit(): void {
    this.route.data.pipe(takeUntil(this.destroy$)).subscribe(({ data: { schedule, table } }) => {
      this.tableData = table;
      this.footballPitchData = schedule;
    });
    this.setMetaData();
  }

  ngOnDestroy(): void {
    this.destroy$.next(true);
    this.destroy$.complete();
  }

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage('merkozes/felallas');
    if (canonical) this.seo.updateCanonicalUrl(canonical);
    const title = createNSOTitle('Mérkőzés - Felállás');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    };
    this.seo.setMetaData(metaData);
  }

  tables(entry: LineupTable): LineUpPositionData[] {
    return Object.values(entry.tables).sort((a, b) => {
      return !this.orderOfPosition.includes(a.playerTypeName)
        ? 1
        : this.orderOfPosition.indexOf(a.playerTypeName) - this.orderOfPosition.indexOf(b.playerTypeName);
    });
  }
}
