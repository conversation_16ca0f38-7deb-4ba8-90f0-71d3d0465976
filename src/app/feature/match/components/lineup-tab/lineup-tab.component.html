<section class="lineup">
  <div class="wrapper with-aside">
    <div class="left-column" *ngIf="tableData?.length">
      <nso-football-pitch [data]="footballPitchData"></nso-football-pitch>

      <div class="tables">
        <div *ngFor="let entry of tableData; let index = index" class="tables-column">
          <div class="team-title">
            <img *ngIf="entry.team.teamLogo" [src]="entry.team.teamLogo" alt="" />
            {{ entry.team.teamName }}
          </div>
          <nso-position-tables *ngFor="let table of tables(entry)" [data]="table"></nso-position-tables>
        </div>
      </div>
    </div>
    <aside>
      <app-sidebar [categorySlug]="categorySlug"></app-sidebar>
    </aside>
  </div>
</section>
