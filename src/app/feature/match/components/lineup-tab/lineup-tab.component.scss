@use 'shared' as *;

:host {
  nso-football-pitch::ng-deep {
    .football-pitch {
      max-width: $global-wrapper-width;
    }
  }

  @include media-breakpoint-down(md) {
    .left-column .team-toggle {
      display: flex;
      justify-content: center;
      margin-bottom: 40px;
    }
    .left-column .tables {
      display: block;
      &-column {
        width: 100%;
        &:last-child {
          margin-top: 30px;
        }
        &.mobile-active {
          visibility: visible;
          display: block;
        }
      }
    }

    aside {
      margin-top: 30px;
    }
  }

  .tables {
    display: flex;
    flex-direction: row;
    gap: 75px;
    flex-wrap: nowrap;
    &-column {
      width: 50%;
      display: flex;
      flex-direction: column;
      gap: 30px;
      .team-title {
        display: flex;
        gap: 8px;
        align-content: center;
        align-items: center;
        font-size: 22px;
        font-weight: 700;
        text-transform: uppercase;
        font-family: var(--kui-font-condensed);
        img {
          width: 40px;
          height: 40px;
          object-fit: contain;
          background-color: var(--kui-white);
          border: 1px solid var(--kui-gray-150);
          border-radius: 40px;
        }
      }
    }
  }
}
