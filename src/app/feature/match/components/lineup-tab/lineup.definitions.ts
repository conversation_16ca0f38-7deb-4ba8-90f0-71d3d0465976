import { FootballPitchData, PlayerBase, PositionType, TeamDetails } from '../../../../shared';

export type LineupResolverData = {
  schedule: FootballPitchData;
  table: LineupTable[];
};

export type LineUpPositionData = {
  playerType: PositionType;
  playerTypeName: string;
  players: PlayerBase[];
};

export type LineupTable = {
  team: TeamDetails;
  tables: { [key: string]: LineUpPositionData };
};
