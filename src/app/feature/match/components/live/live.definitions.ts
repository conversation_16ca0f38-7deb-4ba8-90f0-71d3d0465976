import { MinuteByminuteDefinitions } from '../../../../shared';
import { Facility } from '../../../team-page/team-end-page.definitions';

export type LiveResolverData = {
  timeline: MinuteByminuteDefinitions;
  commentary: LiveCommentaryEvent[];
  miscInfo: MatchMiscInfo;
};

export type MatchMiscInfo = {
  referees: Referees;
  facility: Facility;
  reporter: string;
  visitors: number;
  scheduleDate: Date;
  assistants?: string[];
  scores?: {
    homeScore?: string;
    awayScore?: string;
  };
  scheduleStatus?: string;
};

type Referees = { main: string; secondary: string };

export type LiveCommentaryEvent = {
  eventTitle: string;
  htmlBlock?: string;
  iconTitle?: string;
  iconUrl?: string;
  playerName?: string;
};

export type Timeline = {
  timelineTitle: string;
  homeTeamName: string;
  homeTeamImg: string;
  awayTeamName: string;
  awaTeamImg: string;
  posLeft: string;
  posRight: string;
  timeLineData: TimeLineData[];
};

export type TimeLineData = {
  icon: string;
  time: number;
  percent: string;
  pos: string;
  player: string;
  playerName: string;
  onSubstitutedPlayerName?: string;
};
