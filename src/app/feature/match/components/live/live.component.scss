@use 'shared' as *;

.commentary {
  display: flex;
  flex-direction: column;
  gap: 20px;

  .name {
    font-size: 18px;
    line-height: 26px;
  }

  h2 {
    color: var(--kui-red-400);
    font-family: var(--kui-font-primary);
  }
  &-item {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    gap: 10px;
    &-meta {
      display: flex;
      flex-direction: row;
      flex-wrap: nowrap;
      gap: 10px;
      min-width: 100px;
      @include media-breakpoint-down(md) {
        min-width: 40px;
        flex-direction: column;
      }
      &-title {
        font-size: 24px;
        font-family: var(--kui-font-primary);
        font-weight: 700;

        @include media-breakpoint-down(sm) {
          font-size: 18px;
        }
      }
      &-icon img {
        width: 24px;
        height: 24px;
        object-fit: contain;
      }
    }
    &-content {
      flex-grow: 1;
    }
  }
}

aside {
  .misc-info {
    display: flex;
    flex-direction: column;
    border: 1px solid;
    border-color: var(--kui-gray-200);
    padding: 30px 20px;
    margin: 20px auto;
    gap: 10px;

    @include media-breakpoint-down(md) {
      width: 100%;
    }

    .text-section {
      color: var(--kui-gray-550);
      font-weight: 400;
      line-height: 160%;

      &:first-child {
        font-weight: 700;
        line-height: 120%;
        letter-spacing: 0.16px;
      }
    }
  }
}

.bold {
  font-weight: 700;
}
