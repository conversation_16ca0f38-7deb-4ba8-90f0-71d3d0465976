import { MatchMiscInfo, Timeline } from './live.definitions';
import { backendDateToDate } from '@trendency/kesma-ui';
import { mapLiveEventToTimeLine } from '../../match.utils';
import { Schedule } from '../../../team-page/team-end-page.definitions';

export function getMiscInfo(schedule: Schedule): MatchMiscInfo {
  const refereeList = schedule?.referees?.map(({ lastName, firstName }) => `${lastName} ${firstName}`);
  const assistantsList = schedule?.assistants?.map(({ lastName, firstName }) => `${lastName} ${firstName}`);

  const miscInfo: MatchMiscInfo = {
    visitors: schedule.visitors,
    facility: schedule?.facility,
    reporter: schedule?.reporter,
    scheduleDate: <Date>backendDateToDate(schedule.scheduleDate.date),
    referees: { main: String(refereeList[0] ?? ''), secondary: String(refereeList.slice(1)).replace(',', ', ') },
    assistants: assistantsList,
    scores: {
      awayScore: schedule.awayScore,
      homeScore: schedule.homeScore,
    },
    scheduleStatus: schedule?.scheduleStatus,
  };

  return miscInfo;
}

export function makeTimeline(schedule: Schedule): Timeline {
  return {
    timelineTitle: 'Idővonal',
    homeTeamName: schedule.homeTeam.title,
    homeTeamImg: schedule.homeTeam.logo || schedule.homeTeam.team.logo,
    awayTeamName: schedule.awayTeam.title,
    awaTeamImg: schedule.awayTeam.logo || schedule.awayTeam.team.logo,
    posLeft: 'KR',
    posRight: 'LF',
    timeLineData: (schedule.liveEvents ?? []).map((liveEvent) => mapLiveEventToTimeLine(liveEvent, schedule)),
  };
}
