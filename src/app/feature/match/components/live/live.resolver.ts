import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot } from '@angular/router';
import { Observable, of } from 'rxjs';
import { mapLiveEventToCommentary } from '../../match.utils';
import { LiveCommentaryEvent, LiveResolverData, Timeline } from './live.definitions';
import { getMiscInfo, makeTimeline } from './live.utils';
import { Schedule } from '../../../team-page/team-end-page.definitions';

@Injectable()
export class LiveResolver {
  resolve(route: ActivatedRouteSnapshot): Observable<LiveResolverData> {
    const schedule: Schedule = route.parent?.data['schedule'];

    const timeline: Timeline = makeTimeline(schedule);

    const commentary: LiveCommentaryEvent[] = (schedule.liveEvents ?? []).map(mapLiveEventToCommentary);

    return of({
      timeline,
      commentary,
      miscInfo: getMiscInfo(schedule),
    });
  }
}
