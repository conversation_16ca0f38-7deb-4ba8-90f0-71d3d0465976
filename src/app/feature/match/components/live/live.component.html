<section class="lineup">
  <div class="wrapper with-aside">
    <div class="left-column" *ngIf="routeData$ | async as data">
      <!--
      <nso-minute-byminute [data]="data.timeline"></nso-minute-byminute>
      -->
      <div class="commentary">
        <hr />
        <div *ngFor="let item of data.commentary" class="commentary-item">
          <div class="commentary-item-meta">
            <div class="commentary-item-meta-title">
              {{ item.eventTitle }}
            </div>
            <div *ngIf="item.iconUrl" class="commentary-item-meta-icon">
              <img [src]="item.iconUrl" [alt]="item.iconTitle" [title]="item.iconTitle" loading="lazy" />
            </div>
          </div>
          <div class="commentary-item-content">
            <nso-wysiwyg-box *ngIf="item.htmlBlock" [html]="item.htmlBlock"></nso-wysiwyg-box>
            <p class="name" *ngIf="!item?.htmlBlock && item?.playerName">{{ item.playerName }}</p>
            <hr *ngIf="!item.htmlBlock && !item?.playerName" />
          </div>
        </div>
      </div>
    </div>
    <aside>
      <div *ngIf="routeData$ | async as data" class="misc-info">
        <div *ngIf="data.miscInfo?.scheduleDate" class="text-section date">
          {{ data.miscInfo.scheduleDate | dfnsFormat: 'LLLL d. cccc HH:mm' | titlecase }}
        </div>
        <div *ngIf="data.miscInfo?.facility?.title" class="text-section">{{ data.miscInfo.facility.title }}</div>
        <div *ngIf="data.miscInfo?.facility?.address" class="text-section">{{ data.miscInfo.facility.address }}</div>

        <ng-container *ngIf="data.miscInfo.referees.main">
          <div class="text-section">
            <p>Játékvezető:</p>
            <div class="referees">
              <p class="bold">{{ data.miscInfo.referees.main }}</p>
              <p *ngIf="data.miscInfo.referees.secondary">{{ ', ' + data.miscInfo.referees.secondary }}</p>
            </div>
          </div>
        </ng-container>

        <ng-container *ngIf="data.miscInfo?.assistants?.length">
          <div class="text-section">
            <p>Partjelzők:</p>
            <div class="referees">
              <ng-container *ngFor="let assistant of data.miscInfo?.assistants; let last = last">
                <p class="bold">{{ assistant }}<ng-container *ngIf="!last">, </ng-container></p>
              </ng-container>
            </div>
          </div>
        </ng-container>

        <ng-container *ngIf="data.miscInfo.visitors">
          <div class="text-section">
            <p>Nézőszám:</p>
            <p class="bold">{{ data.miscInfo.visitors }}</p>
          </div>
        </ng-container>

        <ng-container *ngIf="data?.miscInfo?.reporter && data.miscInfo.reporter.length > 0">
          <div class="text-section">
            <p>Tudósítók:</p>
            <div class="referees">
              <p class="bold">{{ data.miscInfo?.reporter }}</p>
            </div>
          </div>
        </ng-container>
      </div>

      <app-sidebar [categorySlug]="categorySlug"></app-sidebar>
    </aside>
  </div>
</section>
