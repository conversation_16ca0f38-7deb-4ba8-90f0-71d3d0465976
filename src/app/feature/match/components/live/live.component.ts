import { Component, ChangeDetectionStrategy, OnInit, ChangeDetectorRef, OnDestroy } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { IMetaData, SeoService, UtilService } from '@trendency/kesma-core';
import { map, switchMap, takeUntil } from 'rxjs/operators';
import { defaultMetaInfo } from 'src/app/shared/constants/meta.consts';
import { createNSOTitle, NsoWysiwygBoxComponent } from '../../../../shared';
import { LiveCommentaryEvent, LiveResolverData } from './live.definitions';
import { MatchService } from '../../api/match.service';
import { interval, of, Subject, take } from 'rxjs';
import { mapLiveEventToCommentary } from '../../match.utils';
import { Schedule } from '../../../team-page/team-end-page.definitions';
import { LiveService } from './live.service';
import { Async<PERSON>ipe, <PERSON><PERSON>orO<PERSON>, NgIf, TitleCasePipe } from '@angular/common';
import { FormatPipeModule } from 'ngx-date-fns';
import { SidebarComponent } from '../../../layout/components/sidebar/sidebar.component';
import { createCanonicalUrlForPageablePage } from '@trendency/kesma-ui';

@Component({
  selector: 'app-live',
  templateUrl: './live.component.html',
  styleUrls: ['./live.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, AsyncPipe, NgForOf, NsoWysiwygBoxComponent, FormatPipeModule, TitleCasePipe, SidebarComponent],
  providers: [LiveService],
})
export class LiveComponent implements OnInit, OnDestroy {
  categorySlug = '';
  routeData$ = this.route.data.pipe(map(({ data }) => data as LiveResolverData));

  private readonly unsubscribe$: Subject<boolean> = new Subject();

  constructor(
    private readonly route: ActivatedRoute,
    private readonly matchService: MatchService,
    private readonly seo: SeoService,
    private readonly cdr: ChangeDetectorRef,
    private readonly utilService: UtilService,
    private readonly liveService: LiveService
  ) {}

  ngOnInit(): void {
    const slug = (this.route.parent?.snapshot.params as { slug: string })?.slug;
    if (this.utilService.isBrowser()) {
      interval(30_000)
        .pipe(takeUntil(this.unsubscribe$))
        .subscribe(() => this.getLive(slug));
    }
    this.setMetaData();
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next(true);
    this.unsubscribe$.complete();
  }

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage('merkozes/elo');
    if (canonical) this.seo.updateCanonicalUrl(canonical);
    const title = createNSOTitle('Mérkőzés - Percról percre');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    };
    this.seo.setMetaData(metaData);
  }

  private getLive(slug: string): void {
    this.routeData$
      .pipe(
        take(1),
        switchMap((routeData) =>
          this.matchService.getLive(slug).pipe(
            map(({ data }) => data),
            map((schedule: Schedule) => {
              const commentary: LiveCommentaryEvent[] = (schedule.liveEvents ?? []).map(mapLiveEventToCommentary);
              const liveData = {
                ...routeData,
                miscInfo: {
                  ...routeData.miscInfo,
                  scores: {
                    awayScore: schedule.awayScore,
                    homeScore: schedule.homeScore,
                  },
                  scheduleStatus: schedule?.scheduleStatus,
                },
                commentary,
              };
              this.routeData$ = of(liveData);
              this.liveService.data$.next(liveData);
              this.cdr.markForCheck();
            })
          )
        )
      )
      .subscribe();
  }
}
