import { Component, ChangeDetectionStrategy, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { SeoService, IMetaData } from '@trendency/kesma-core';
import { ApiResult, createCanonicalUrlForPageablePage } from '@trendency/kesma-ui';
import { Observable, map } from 'rxjs';
import { defaultMetaInfo } from 'src/app/shared/constants/meta.consts';
import { createNSOTitle, NsoMatchStatsTeamsComponent } from '../../../../shared';
import { MatchStats } from '../../match.definition';
import { AsyncPipe, NgIf, NgTemplateOutlet } from '@angular/common';
@Component({
  selector: 'app-match-analysis',
  templateUrl: './match-analysis.component.html',
  styleUrls: ['./match-analysis.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, AsyncPipe, NgTemplateOutlet, NsoMatchStatsTeamsComponent],
})
export class MatchAnalysisComponent implements OnInit {
  readonly routeData$ = (this.route.data as Observable<ApiResult<MatchStats>>).pipe(map((result) => result.data));

  constructor(
    private readonly route: ActivatedRoute,
    private readonly seo: SeoService
  ) {}

  ngOnInit(): void {
    this.setMetaData();
  }

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage('merkozes/elemzes');
    if (canonical) this.seo.updateCanonicalUrl(canonical);
    const title = createNSOTitle('Mérkőzés - Elemzés');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    };
    this.seo.setMetaData(metaData);
  }
}
