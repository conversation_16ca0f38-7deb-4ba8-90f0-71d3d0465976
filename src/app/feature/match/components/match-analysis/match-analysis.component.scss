@use 'shared' as *;

:host {
  display: flex;
  flex-direction: column;
  margin-top: 20px;
  gap: 50px;

  .team {
    &-header {
      display: flex;
      align-items: center;
      gap: 16px;
      margin-bottom: -18px;
    }

    &-thumbnail {
      width: 42px;
      height: 42px;
      object-fit: cover;
      border-radius: 42px;
      border: 1px solid var(--kui-gray-200);
    }

    &-name {
      color: var(--kui-gray-600);
      font-size: 38px;
      font-weight: 700;
      font-family: var(--kui-font-condensed);
      text-transform: uppercase;
    }
  }

  nso-match-stats-teams {
    ::ng-deep {
      kesma-data-table-generator table {
        border: 1px solid var(--kui-gray-150);
      }
    }
  }
}
