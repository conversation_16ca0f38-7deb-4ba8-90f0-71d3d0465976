<ng-container *ngIf="routeData$ | async as data">
  <ng-container *ngTemplateOutlet="headerComponent; context: { $implicit: data.home.team }"> </ng-container>
  <nso-match-stats-teams [data]="data.home.matchList" [showTeamName]="false"></nso-match-stats-teams>

  <ng-container *ngTemplateOutlet="headerComponent; context: { $implicit: data.away.team }"> </ng-container>
  <nso-match-stats-teams [data]="data.away.matchList" [showTeamName]="false"></nso-match-stats-teams>
</ng-container>

<ng-template #headerComponent let-team>
  <div class="team-header">
    <img class="team-thumbnail" [src]="team?.logo" alt="{{ team?.name }} logó" />
    <h2 class="team-name">{{ team?.name }}</h2>
  </div>
</ng-template>
