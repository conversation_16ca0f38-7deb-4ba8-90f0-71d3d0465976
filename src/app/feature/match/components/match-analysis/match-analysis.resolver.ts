import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot } from '@angular/router';
import { forkJoin, map, Observable } from 'rxjs';
import { mapToMatches } from '../../../../shared';
import { MatchStat, MatchStats } from '../../match.definition';
import { TeamPageService } from '../../../team-page/api/team-page.service';
import { Team } from '../../../team-page/team-end-page.definitions';

@Injectable()
export class MatchAnalysisResolver {
  constructor(private readonly teamPageService: TeamPageService) {}

  resolve(route: ActivatedRouteSnapshot): Observable<MatchStats> {
    const schedule = route.parent?.data['schedule'];

    return forkJoin({
      home: this.getMatches(schedule.competition.slug, schedule.homeTeam.team),
      away: this.getMatches(schedule.competition.slug, schedule.awayTeam.team),
    });
  }

  private getMatches(competitionSlug: string, team: Team): Observable<MatchStat> {
    return this.teamPageService.getTeamSchedules(team.slug, competitionSlug).pipe(
      map(({ data: { schedules } }) => ({
        team: team,
        matchList: mapToMatches(schedules, false, team),
      }))
    );
  }
}
