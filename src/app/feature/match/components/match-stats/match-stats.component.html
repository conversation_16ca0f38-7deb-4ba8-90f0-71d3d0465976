<ng-container *ngIf="routeData$ | async as data">
  <div class="team">
    <div class="team-header">
      <div class="team-header-icon"><img [src]="data.home.team.logo" [alt]="data.home.team.logo" /></div>
      <h1 class="team-header-name">{{ data.home.team.name }}</h1>
    </div>
    <nso-match-stats-teams [data]="data.home.matchList" [showTeamName]="false"></nso-match-stats-teams>
  </div>
  <div class="team">
    <div class="team-header">
      <div class="team-header-icon"><img [src]="data.away.team.logo" [alt]="data.away.team.logo" /></div>
      <h1 class="team-header-name">{{ data.away.team.name }}</h1>
    </div>
    <nso-match-stats-teams [data]="data.away.matchList" [showTeamName]="false"></nso-match-stats-teams>
  </div>
  <div class="versus">
    <h1 class="versus-title">Egymás elleni találkozók</h1>
    <nso-match-stats-teams class="versus-stat" [data]="data?.versus"></nso-match-stats-teams>
  </div>
</ng-container>
