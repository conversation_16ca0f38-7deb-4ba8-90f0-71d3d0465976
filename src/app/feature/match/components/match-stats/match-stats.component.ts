import { Component, ChangeDetectionStrategy, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Observable, map } from 'rxjs';
import { MatchStats } from '../../match.definition';
import { ApiResult, createCanonicalUrlForPageablePage } from '@trendency/kesma-ui';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import { defaultMetaInfo } from 'src/app/shared/constants/meta.consts';
import { createNSOTitle, NsoMatchStatsTeamsComponent } from '../../../../shared';
import { AsyncPipe, NgIf } from '@angular/common';

@Component({
  selector: 'app-match-stats',
  templateUrl: './match-stats.component.html',
  styleUrls: ['./match-stats.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, AsyncPipe, NsoMatchStatsTeamsComponent],
})
export class MatchStatsComponent implements OnInit {
  readonly routeData$ = (this.route.data as Observable<ApiResult<MatchStats>>).pipe(map((result) => result.data));

  constructor(
    private readonly route: ActivatedRoute,
    private readonly seo: SeoService
  ) {}

  ngOnInit(): void {
    this.setMetaData();
  }

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage('merkozes/stat');
    if (canonical) this.seo.updateCanonicalUrl(canonical);
    const title = createNSOTitle('Mérkőzés - Statisztika');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    };
    this.seo.setMetaData(metaData);
  }
}
