@use 'shared' as *;

:host {
  display: flex;
  flex-direction: column;
  margin: auto;
  gap: 60px;
  padding-bottom: 60px;

  nso-match-stats-teams {
    ::ng-deep {
      kesma-data-table-generator table {
        border: 1px solid var(--kui-gray-150);
      }
    }
  }
}

.team {
  &-header {
    display: flex;
    align-items: center;
    margin-bottom: 32px;
    gap: 16px;

    &-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      object-fit: cover;
      width: 42px;
      height: 42px;
      border-radius: 42px;
      border: 1px solid var(--kui-gray-150);

      img {
        border-radius: 42px;
      }
    }

    &-name {
      font-size: 38px;
      text-transform: uppercase;
    }
  }
}

.versus {
  &-title {
    font-size: 38px;
    text-transform: uppercase;
    margin-bottom: 32px;
  }

  &-stat {
    ::ng-deep {
      .first-cell a {
        display: none;
      }
    }
  }
}
