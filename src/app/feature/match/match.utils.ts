import { LiveEvent, Schedule } from '../team-page/team-end-page.definitions';
import { LiveCommentaryEvent, TimeLineData } from './components/live/live.definitions';

export function mapLiveEventToCommentary(liveEvent: LiveEvent): LiveCommentaryEvent {
  return {
    eventTitle: liveEvent?.minute,
    htmlBlock: liveEvent?.eventText,
    iconTitle: liveEvent.scheduleEvent?.title,
    iconUrl: liveEvent.scheduleEvent?.icon,
    playerName:
      liveEvent.competitionTeamPlayer?.lastName && liveEvent.competitionTeamPlayer?.firstName
        ? `${liveEvent.competitionTeamPlayer?.lastName} ${liveEvent.competitionTeamPlayer?.firstName}`
        : undefined,
  };
}

export function mapLiveEventToTimeLine(liveEvent: LiveEvent, schedule: Schedule): TimeLineData {
  const time = parseMinute(liveEvent?.minute, schedule?.scheduleTime);
  return {
    icon: liveEvent.scheduleEvent?.icon,
    time: time,
    percent: calculateMatchEvent(schedule?.scheduleTime, time) + '%',
    pos: liveEvent.competitionTeam?.id === schedule.homeTeam.id ? 'homeTeamName' : 'awayTeamName',
    player: liveEvent.competitionTeamPlayer?.jersey,
    playerName: liveEvent.competitionTeamPlayer?.publicName,
    onSubstitutedPlayerName: liveEvent.substituteCompetitionTeamPlayer?.publicName,
  };
}

function parseMinute(minute: string, scheduleTime = '90'): number {
  // eslint-disable-next-line quotes
  const timeMinute: number = minute?.includes("'") ? +minute.substring(0, minute.indexOf("'")) : +minute;
  const timeWithoutQuote: number = minute === null || minute === undefined ? +scheduleTime : timeMinute;
  return timeWithoutQuote;
}

function calculateMatchEvent(scheduleTime = '90', eventTime: number): number {
  return Math.round((eventTime / +scheduleTime) * 100);
}
