@use 'shared' as *;

.subscription-steps {
  display: flex;
  margin: 0 auto 30px;
  align-items: center;
  overflow-x: auto;
  overflow-y: hidden;
}

.subscription-step {
  color: var(--kui-gray-550);
  font-size: 14px;
  font-family: var(--kui-font-primary);
  text-transform: uppercase;
  line-height: 14px;
  padding: 10px 31px 10px 20px;
  position: relative;
  border-top: 1px solid var(--kui-gray-300);
  border-bottom: 1px solid var(--kui-gray-300);
  width: 100%;
  text-align: center;
  white-space: nowrap;

  &:before {
    content: '';
    width: 17px;
    height: 36px;
    background-color: var(--kui-white);
    border-top: 1px solid var(--kui-gray-300);
    border-bottom: 1px solid var(--kui-gray-300);
    position: absolute;
    top: -1px;
    right: 0;
  }

  &:after {
    content: '';
    width: 0;
    height: 0;
    border-width: 17px 0 17px 16px;
    border-color: transparent transparent transparent var(--kui-white);
    border-style: solid;
    filter: drop-shadow(1px 0px 0px var(--kui-gray-300));
    position: absolute;
    top: 0;
    right: 1px;
  }

  &:first-child {
    border-left: 1px solid var(--kui-gray-300);
  }

  &:last-child {
    padding-right: 20px;
    border-right: 1px solid var(--kui-gray-300);

    &:before,
    &:after {
      display: none;
    }
  }

  &.active,
  &.done {
    background-color: var(--kui-gray-550);
    border-top-color: var(--kui-gray-550);
    border-bottom-color: var(--kui-gray-550);

    &:first-child {
      border-left-color: var(--kui-gray-550);
    }

    &:last-child {
      border-right-color: var(--kui-gray-550);
    }

    &:after {
      border-left-color: var(--kui-gray-550);
    }
  }

  &.active {
    color: var(--kui-white);
    font-weight: bold;

    &:after {
      filter: none;
    }
  }

  &.done {
    color: var(--kui-gray-300);

    &.clickable {
      cursor: pointer;
    }

    &:before {
      background-color: var(--kui-gray-550);
      border-top-color: var(--kui-gray-550);
      border-bottom-color: var(--kui-gray-550);
    }
  }
}
