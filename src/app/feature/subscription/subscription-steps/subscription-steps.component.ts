import { ChangeDetectionStrategy, Component } from '@angular/core';
import { SubscriptionStep, SubscriptionStepType } from '../subscription.definitions';
import { SubscriptionService } from '../subscription.service';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { AsyncPipe, NgClass, NgForOf } from '@angular/common';

@Component({
  selector: 'app-subscription-steps',
  templateUrl: './subscription-steps.component.html',
  styleUrls: ['./subscription-steps.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgForOf, NgClass, AsyncPipe],
})
export class SubscriptionStepsComponent {
  subscriptionSteps: SubscriptionStep[] = [
    {
      type: SubscriptionStepType.SELECT_PRODUCT,
      name: 'Előfizetés kiválasztása',
    },
    {
      type: SubscriptionStepType.DATA_FORM,
      name: 'Adatok megadása',
    },
    {
      type: SubscriptionStepType.SUMMARY,
      name: '<PERSON><PERSON>zegzés',
    },
  ];

  constructor(private readonly subscriptionService: SubscriptionService) {}

  getStepClass(step: SubscriptionStepType): Observable<string> {
    return this.subscriptionService.currentStep$.pipe(
      map((currentStep: SubscriptionStepType) => {
        if (step === currentStep) {
          return 'active';
        } else if (step < currentStep) {
          return 'done' + (currentStep !== SubscriptionStepType.SUMMARY ? ' clickable' : '');
        }

        return '';
      })
    );
  }

  navigateStep(step: SubscriptionStepType): void {
    if (step < this.subscriptionService.currentStep && this.subscriptionService.currentStep !== SubscriptionStepType.SUMMARY) {
      this.subscriptionService.currentStep$.next(step);
    }
  }
}
