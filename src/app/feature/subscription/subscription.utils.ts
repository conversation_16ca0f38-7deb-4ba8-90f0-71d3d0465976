import { BackendSubscriptionSaveRequest, SubscriptionSessionData } from './subscription.definitions';

export const getPeriodForPrice = (periodInDay: number | undefined): string | undefined => {
  switch (periodInDay) {
    case 30:
      return '1 hónapra';
    case 90:
      return '3 hónapra';
    case 180:
      return '6 hónapra';
    case 365:
      return '12 hónapra';
    default:
      return `${periodInDay} napra`;
  }
};

export function subscriptionSessionDataToBackendRequest(data: SubscriptionSessionData, recaptchaToken: string): BackendSubscriptionSaveRequest {
  return {
    campaignId: data?.selectedCampaign?.id,
    productId: data?.selectedProduct?.id,
    email: data?.email,
    phoneNumber: data?.phoneNumber,
    invoiceName: data?.billingAddress?.name,
    invoiceZip: data?.billingAddress?.zip,
    invoiceCity: data?.billingAddress?.city,
    invoiceAddress: data?.billingAddress?.address,
    shippingName: data?.isShippingAddressSame ? data?.billingAddress?.name : data?.shippingAddress?.name,
    shippingZip: data?.isShippingAddressSame ? data?.billingAddress?.zip : data?.shippingAddress?.zip,
    shippingCity: data?.isShippingAddressSame ? data?.billingAddress?.city : data?.shippingAddress?.city,
    shippingAddress: data?.isShippingAddressSame ? data?.billingAddress?.address : data?.shippingAddress?.address,
    taxNumber: data?.billingAddress?.tax ?? undefined,
    paymentMethod: data?.paymentMethod,
    startDate: data?.startDate,
    terms: data?.terms ? 1 : 0,
    confirmation: data?.confirmation ? 1 : 0,
    recaptcha: recaptchaToken,
  };
}
