<section>
  <div class="wrapper subscription-wrapper" id="subscription-top">
    <nso-breadcrumb [items]="[{ label: '<PERSON><PERSON><PERSON>zeté<PERSON>' }]"></nso-breadcrumb>

    <h1 class="subscription-title">Nemzeti Sport előfizetés</h1>

    <app-subscription-steps></app-subscription-steps>

    <ng-container *ngIf="currentStep$ | async as currentStep">
      <app-subscription-select-product *ngIf="currentStep === SubscriptionStepType.SELECT_PRODUCT"></app-subscription-select-product>
      <app-subscription-data-form *ngIf="currentStep === SubscriptionStepType.DATA_FORM"></app-subscription-data-form>
      <app-subscription-summary *ngIf="currentStep === SubscriptionStepType.SUMMARY"></app-subscription-summary>
    </ng-container>
  </div>
</section>
