import { ChangeDetectionStrategy, Component } from '@angular/core';
import {
  nsoSubscriptionCampaigns,
  SubscriptionCampaign,
  SubscriptionProduct,
  SubscriptionSelectProductStepData,
  SubscriptionStepType,
} from '../subscription.definitions';
import { SubscriptionService } from '../subscription.service';
import { getPeriodForPrice } from '../subscription.utils';
import { ArticleCard, ThousandSeparatorPipe } from '@trendency/kesma-ui';
import { map } from 'rxjs/operators';
import { Observable } from 'rxjs';
import { ApiService, NsoSimpleButtonComponent } from '../../../shared';
import { AsyncPipe, NgForOf, NgIf } from '@angular/common';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'app-subscription-select-product',
  templateUrl: './subscription-select-product.component.html',
  styleUrls: ['./subscription-select-product.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgForOf, ThousandSeparatorPipe, NsoSimpleButtonComponent, NgIf, AsyncPipe, RouterLink],
})
export class SubscriptionSelectProductComponent {
  getPeriodForPrice = getPeriodForPrice;
  campaigns: SubscriptionCampaign[] = nsoSubscriptionCampaigns;

  digitalArticle$: Observable<ArticleCard> = this.apiService.getCategoryArticles('napilap', 0, 1).pipe(map((res) => res?.data?.[0] as ArticleCard));

  constructor(
    private readonly subscriptionService: SubscriptionService,
    private readonly apiService: ApiService
  ) {}

  selectProduct(campaign: SubscriptionCampaign, product: SubscriptionProduct): void {
    this.subscriptionService.handleStep(SubscriptionStepType.SELECT_PRODUCT, {
      selectedCampaign: campaign,
      selectedProduct: product,
    } as SubscriptionSelectProductStepData);
  }
}
