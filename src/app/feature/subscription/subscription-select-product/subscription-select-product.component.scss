@use 'shared' as *;

.subscription-campaigns {
  &-text {
    margin-bottom: 30px;
  }
}

.subscription-campaign {
  &-title {
    font-weight: bold;
    font-size: 20px;
    line-height: 22px;
    margin: 10px 0 20px;
    font-family: var(--kui-font-primary);
    text-transform: uppercase;
  }

  &-content {
    > div {
      margin-bottom: 20px;
    }
  }

  &-digital {
    margin-bottom: 50px;
    padding: 20px;
    border: 1px solid var(--kui-gray-200);

    > .row {
      align-items: center;
    }

    &-text {
      @include media-breakpoint-down(sm) {
        margin-top: 20px;
      }

      ul {
        list-style: disc;
        display: inline-block;
        text-align: left;
        margin: 0 0 0 20px;

        li {
          padding: 5px 0;
        }
      }

      nso-simple-button {
        margin: 20px 0 0;
      }
    }

    &-advantages {
      margin: 20px 0;
    }

    h2 {
      font-family: var(--kui-font-primary);
      color: var(--kui-gray-500);
      font-weight: 700;
      font-size: 30px;
      line-height: 36px;
      margin: 30px 0 20px;
    }

    &-newspaper {
      display: block;
      border: 1px solid var(--kui-gray-200);
      color: var(--kui-gray-550);
      text-align: center;

      img {
        width: 100%;
        min-height: 170px;
      }

      p {
        background-color: var(--kui-gray-100);
        font-weight: bold;
        font-size: 16px;
        line-height: 20px;
        margin: 0;
        font-family: var(--kui-font-primary);
        padding: 10px;
      }
    }
  }
}

.subscription-product {
  border: 1px solid var(--kui-gray-200);
  text-align: center;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  &-title {
    background-color: var(--kui-gray-100);
    font-weight: bold;
    font-size: 16px;
    line-height: 20px;
    margin-bottom: 20px;
    font-family: var(--kui-font-primary);
    padding: 10px;
  }

  &-price {
    font-weight: bold;
    font-size: 18px;
    line-height: 20px;
    color: var(--kui-gray-550);
    padding: 0 10px;

    &-sub {
      color: var(--kui-gray-550);
      font-size: 12px;
      line-height: 14px;
      padding: 0 10px;
    }
  }

  &-action {
    margin: 20px 0 10px 0;
  }
}
