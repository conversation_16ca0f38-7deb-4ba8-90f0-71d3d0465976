<!-- Campaign list -->
<div class="subscription-campaigns">
  <div class="subscription-campaigns-text">
    Amennyiben szeretne előfizetni Nemzeti Sport napilapunkra kérjük válasszon az alábbi előfizetések közül, majd töltse ki az adatlapot.
  </div>
  <div *ngFor="let campaign of campaigns" class="subscription-campaign">
    <h2 class="subscription-campaign-title">{{ campaign.name }}</h2>
    <div class="row subscription-campaign-content">
      <!-- Product list -->
      <div *ngFor="let product of campaign.products" class="col-12 col-sm-6 col-md-3">
        <div class="subscription-product">
          <h3 class="subscription-product-title">{{ product.name }}</h3>
          <div class="subscription-product-price">{{ product.price | thousandSeparator }} Ft</div>
          <div class="subscription-product-price-sub">{{ getPeriodForPrice(product.periodInDay) }}</div>
          <div class="subscription-product-action">
            <nso-simple-button (click)="selectProduct(campaign, product)" round="round">KIVÁLASZTOM » </nso-simple-button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div *ngIf="digitalArticle$ | async as digitalArticle" class="subscription-campaign">
    <h2 class="subscription-campaign-title">Digitális formátum</h2>
    <div class="subscription-campaign-digital">
      <div class="row">
        <div class="col-12 col-md-6 col-lg-4">
          <a [routerLink]="['/', 'napilap']" class="subscription-campaign-digital-newspaper">
            <img [alt]="digitalArticle.title" [src]="digitalArticle.thumbnail ?? '/assets/images/nemzetisport.png'" loading="lazy" />
            <p>{{ digitalArticle.title }}</p>
          </a>
        </div>
        <div class="col-12 col-md-6 col-lg-8">
          <div class="subscription-campaign-digital-text">
            Aktuális és korábbi lapszámaink digitális formátumban is elérhetőek.
            <div class="subscription-campaign-digital-advantages"><strong>Előnyei:</strong></div>
            <div>
              <ul>
                <li>bárhol és bármikor elérhető</li>
                <li>környezetbarát</li>
                <li>azonnali</li>
              </ul>
            </div>
            <div>
              <nso-simple-button [routerLink]="['', 'napilap']" round="round">TOVÁBBI INFORMÁCIÓ</nso-simple-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
