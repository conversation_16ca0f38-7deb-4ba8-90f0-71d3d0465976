import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import { createNSOTitle, defaultMetaInfo, NsoBreadcrumbComponent } from '../../shared';
import { SubscriptionService } from './subscription.service';
import { SubscriptionStepType } from './subscription.definitions';
import { Observable } from 'rxjs';
import { SubscriptionStepsComponent } from './subscription-steps/subscription-steps.component';
import { AsyncPipe, NgIf } from '@angular/common';
import { SubscriptionSelectProductComponent } from './subscription-select-product/subscription-select-product.component';
import { SubscriptionDataFormComponent } from './subscription-data-form/subscription-data-form.component';
import { SubscriptionSummaryComponent } from './subscription-summary/subscription-summary.component';
import { createCanonicalUrlForPageablePage } from '@trendency/kesma-ui';

@Component({
  selector: 'app-subscription',
  templateUrl: './subscription.component.html',
  styleUrls: ['./subscription.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NsoBreadcrumbComponent,
    SubscriptionStepsComponent,
    NgIf,
    AsyncPipe,
    SubscriptionSelectProductComponent,
    SubscriptionDataFormComponent,
    SubscriptionSummaryComponent,
  ],
})
export class SubscriptionComponent implements OnInit {
  currentStep$: Observable<SubscriptionStepType> = this.subscriptionService.currentStep$.asObservable();

  SubscriptionStepType = SubscriptionStepType;

  constructor(
    private readonly subscriptionService: SubscriptionService,
    private readonly seo: SeoService,
    private readonly cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.setMetaData();
    this.subscriptionService.loadSavedSessionData();
    this.cdr.detectChanges();
  }

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage('elofizetes');
    if (canonical) this.seo.updateCanonicalUrl(canonical);
    const title = createNSOTitle('Előfizetés');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    };
    this.seo.setMetaData(metaData);
  }
}
