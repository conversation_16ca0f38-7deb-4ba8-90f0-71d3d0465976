import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import {
  SubscriptionDataFormStepData,
  SubscriptionSelectProductStepData,
  SubscriptionSessionData,
  SubscriptionStepType,
  SubscriptionSummaryStepData,
} from './subscription.definitions';
import { StorageService, UtilService } from '@trendency/kesma-core';
import { ViewportScroller } from '@angular/common';

@Injectable({
  providedIn: 'root',
})
export class SubscriptionService {
  static readonly SESSION_KEY: string = 'subscriptionSessionData';
  currentStep$: BehaviorSubject<SubscriptionStepType> = new BehaviorSubject<SubscriptionStepType>(SubscriptionStepType.SELECT_PRODUCT);
  sessionData: SubscriptionSessionData | undefined;

  constructor(
    private readonly storageService: StorageService,
    private readonly viewportScroller: ViewportScroller,
    private readonly utilsService: UtilService
  ) {}

  get currentStep(): SubscriptionStepType {
    return this.currentStep$.getValue();
  }

  loadSavedSessionData(): void {
    const storedData = this.storageService.getSessionStorageData(SubscriptionService.SESSION_KEY);

    if (storedData) {
      this.sessionData = storedData;
    }

    this.currentStep$.next(this.sessionData?.nextStep ?? SubscriptionStepType.SELECT_PRODUCT);
  }

  saveStepData(data: SubscriptionSelectProductStepData | SubscriptionDataFormStepData | SubscriptionSummaryStepData, nextStep: SubscriptionStepType): void {
    this.sessionData = {
      ...(this.sessionData ?? {}),
      ...data,
      nextStep,
    };

    this.storageService.setSessionStorageData(SubscriptionService.SESSION_KEY, this.sessionData);
  }

  determineNextStep(currentStep: SubscriptionStepType): SubscriptionStepType {
    switch (currentStep) {
      case SubscriptionStepType.SELECT_PRODUCT:
        return SubscriptionStepType.DATA_FORM;
      default:
        return SubscriptionStepType.SUMMARY;
    }
  }

  handleStep(currentStep: SubscriptionStepType, data: SubscriptionSelectProductStepData | SubscriptionDataFormStepData | SubscriptionSummaryStepData): void {
    const calculatedNextStep: SubscriptionStepType = this.determineNextStep(currentStep);

    // Save all data to maintain progress
    this.saveStepData(data, calculatedNextStep);

    // Navigate to next step except SUMMARY
    if (currentStep !== SubscriptionStepType.SUMMARY) {
      this.navigateToNextStep(calculatedNextStep);
    }
  }

  navigateToNextStep(nextStep: SubscriptionStepType): void {
    this.currentStep$.next(nextStep);

    if (this.utilsService.isBrowser()) {
      this.viewportScroller.scrollToAnchor('subscription-top');
    }
  }

  resetSessionData(): void {
    this.sessionData = undefined;
    this.storageService.setSessionStorageData(SubscriptionService.SESSION_KEY, undefined);
  }
}
