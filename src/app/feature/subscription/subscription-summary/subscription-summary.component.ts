import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { SubscriptionService } from '../subscription.service';
import { SubscriptionPaymentMethod, SubscriptionSessionData, SubscriptionStepType, SubscriptionSummaryStepData } from '../subscription.definitions';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { KesmaFormControlComponent, markControlsTouched, ThousandSeparatorPipe } from '@trendency/kesma-ui';
import { addMonths, format } from 'date-fns';
import { Router, RouterLink } from '@angular/router';
import { environment } from '../../../../environments/environment';
import { ReCaptchaV3Service } from 'ngx-captcha';
import { ApiService, NsoDateTimePickerComponent, NsoPopupComponent, NsoSimpleButtonComponent } from '../../../shared';
import { NgIf } from '@angular/common';

@Component({
  selector: 'app-subscription-summary',
  templateUrl: './subscription-summary.component.html',
  styleUrls: ['./subscription-summary.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    ReactiveFormsModule,
    NgIf,
    KesmaFormControlComponent,
    ThousandSeparatorPipe,
    NsoDateTimePickerComponent,
    RouterLink,
    NsoSimpleButtonComponent,
    NsoPopupComponent,
  ],
})
export class SubscriptionSummaryComponent implements OnInit {
  sessionData: SubscriptionSessionData | undefined = this.subscriptionService?.sessionData;
  formGroup: FormGroup | undefined;

  isLoading = false;
  isSubmitted = false;
  error: string | null = null;

  minDate: string = format(new Date(), 'yyyy-MM-dd');
  maxDate: string = format(addMonths(new Date(), 3), 'yyyy-MM-dd');

  SubscriptionStepType = SubscriptionStepType;
  SubscriptionPaymentMethod = SubscriptionPaymentMethod;

  constructor(
    private readonly formBuilder: FormBuilder,
    private readonly subscriptionService: SubscriptionService,
    private readonly cdr: ChangeDetectorRef,
    private readonly router: Router,
    private readonly reCaptchaV3Service: ReCaptchaV3Service,
    private readonly apiService: ApiService
  ) {}

  ngOnInit(): void {
    this.initForm();
  }

  initForm(): void {
    this.formGroup = this.formBuilder.group({
      paymentMethod: [this.sessionData?.paymentMethod ?? SubscriptionPaymentMethod.POSTAL_CHECK, Validators.required],
      startDate: [this.sessionData?.startDate ?? format(new Date(), 'yyyy-MM-dd'), Validators.required],
      confirmation: [this.sessionData?.confirmation ?? false, Validators.requiredTrue],
      terms: [this.sessionData?.terms ?? false, Validators.requiredTrue],
    });
  }

  backToPreviousStep(step: SubscriptionStepType): void {
    this.subscriptionService.currentStep$.next(step);
  }

  navigateToNextStep(): void {
    if (this.formGroup) {
      markControlsTouched(this.formGroup);
    }

    if (!this.formGroup?.valid) {
      return;
    }

    this.error = null;
    this.isLoading = true;

    // First handle the step
    this.subscriptionService.handleStep(SubscriptionStepType.SUMMARY, this.formGroup?.value as SubscriptionSummaryStepData);

    this.reCaptchaV3Service.execute(
      environment.googleSiteKey ?? '',
      'app_publicapi_portal_simple_subscription',
      (recaptchaToken: string) => {
        this.apiService.saveSubscription(this.subscriptionService?.sessionData ?? {}, recaptchaToken).subscribe({
          next: () => {
            this.isLoading = false;
            this.isSubmitted = true;
            this.cdr.detectChanges();
          },
          error: () => {
            this.error = 'Ismeretlen hiba történt! Kérjük ellenőrizze az adatokat és próbálja újra vagy vegye fel a kapcsolatot a szerkesztőséggel!';
            this.isLoading = false;
            this.cdr.detectChanges();
          },
        });
      },
      {
        useGlobalDomain: false,
      },
      () => {
        this.error = 'Captcha: Robot ellenőrzés hiba!';
        this.isLoading = false;
        this.cdr.detectChanges();
      }
    );
  }

  handlePopupResult(): void {
    this.router.navigate(['/']);
    this.subscriptionService.resetSessionData();
  }
}
