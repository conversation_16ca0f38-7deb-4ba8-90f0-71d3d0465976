<div class="subscription-summary">
  <form (ngSubmit)="navigateToNextStep()" *ngIf="formGroup" [formGroup]="formGroup" class="subscription-summary-form">
    <div class="subscription-summary-title">Adatok ellenőrzése</div>

    <!-- E-mail -->
    <div class="subscription-summary-group">
      <div class="subscription-summary-group-title">
        Előfizetői adatok
        <a (click)="backToPreviousStep(SubscriptionStepType.DATA_FORM)" class="subscription-summary-group-title-link"> <i class="icon edit"></i><PERSON><PERSON><PERSON><PERSON><PERSON> </a>
      </div>
      <div class="subscription-summary-value">
        <div>E-mail cím: {{ sessionData?.email }}</div>
        <div>Telefonszám: {{ sessionData?.phoneNumber }}</div>
      </div>
    </div>

    <!-- Billing data -->
    <div class="subscription-summary-group">
      <div class="subscription-summary-group-title">
        Számlázási adatok
        <a (click)="backToPreviousStep(SubscriptionStepType.DATA_FORM)" class="subscription-summary-group-title-link"> <i class="icon edit"></i>Módosítom </a>
      </div>
      <div class="subscription-summary-value">
        <div>{{ sessionData?.billingAddress?.name }}</div>
        <div>{{ sessionData?.billingAddress?.zip }} {{ sessionData?.billingAddress?.city }}</div>
        <div>{{ sessionData?.billingAddress?.address }}</div>
        <div *ngIf="sessionData?.billingAddress?.tax">Adószám: {{ sessionData?.billingAddress?.tax }}</div>
      </div>
    </div>

    <!-- Shipping data -->
    <div class="subscription-summary-group">
      <div class="subscription-summary-group-title">
        Szállítási adatok
        <a (click)="backToPreviousStep(SubscriptionStepType.DATA_FORM)" class="subscription-summary-group-title-link"> <i class="icon edit"></i>Módosítom </a>
      </div>
      <div class="subscription-summary-value">
        <div>{{ sessionData?.shippingAddress?.name ?? sessionData?.billingAddress?.name }}</div>
        <div>
          {{ sessionData?.shippingAddress?.zip ?? sessionData?.billingAddress?.zip }}
          {{ sessionData?.shippingAddress?.city ?? sessionData?.billingAddress?.city }}
        </div>
        <div>{{ sessionData?.shippingAddress?.address ?? sessionData?.billingAddress?.address }}</div>
      </div>
    </div>

    <!-- Payment method -->
    <div class="subscription-summary-group">
      <div class="subscription-summary-group-title">Fizetési mód</div>
      <div class="subscription-summary-value">
        <div class="nso-form-row">
          <label class="nso-form-radio" for="paymentMethodPostalCheck">
            <input
              [value]="SubscriptionPaymentMethod.POSTAL_CHECK"
              formControlName="paymentMethod"
              id="paymentMethodPostalCheck"
              name="paymentMethod"
              type="radio"
            />
            <span>Postai csekken történő befizetés</span>
          </label>
          <!-- Put validation only on last element -->
          <kesma-form-control>
            <label class="nso-form-radio" for="paymentMethodBankTransfer">
              <input
                [value]="SubscriptionPaymentMethod.BANK_TRANSFER"
                formControlName="paymentMethod"
                id="paymentMethodBankTransfer"
                name="paymentMethod"
                type="radio"
              />
              <span>Banki átutalás</span>
            </label>
          </kesma-form-control>
        </div>
      </div>
    </div>

    <!-- Product -->
    <div class="subscription-summary-group">
      <div class="subscription-summary-group-title">
        Kiválasztott előfizetés
        <a (click)="backToPreviousStep(SubscriptionStepType.SELECT_PRODUCT)" class="subscription-summary-group-title-link">
          <i class="icon edit"></i>Módosítom
        </a>
      </div>
      <div class="subscription-summary-value">
        <table>
          <thead>
            <tr>
              <th>Termék</th>
              <th>Bruttó ár</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>{{ sessionData?.selectedProduct?.name }} ({{ sessionData?.selectedCampaign?.name }})</td>
              <td>
                {{ sessionData?.selectedProduct?.price | thousandSeparator }}
                Ft
              </td>
            </tr>
          </tbody>
          <tfoot>
            <tr>
              <td>Összesen fizetendő:</td>
              <td>{{ sessionData?.selectedProduct?.price | thousandSeparator }} Ft</td>
            </tr>
          </tfoot>
        </table>
      </div>
    </div>

    <!-- Subscription start date -->
    <div class="subscription-summary-group">
      <div class="subscription-summary-group-title">Előfizetés kezdete</div>
      <div class="subscription-summary-value">
        <div class="nso-form-row">
          <nso-date-time-picker
            [enableTime]="false"
            [formGroup]="formGroup"
            [maxDateTime]="maxDate"
            [minDateTime]="minDate"
            controlName="startDate"
            displayFormat="yyyy. MM. dd."
            [id]="'startDate'"
            nowText="Mai nap"
            valueFormat="yyyy-MM-dd"
          ></nso-date-time-picker>
          <div class="subscription-summary-value-info">
            Előfizetése a következő hónap elsejétől válik aktívvá, amennyiben megrendelését elküldi folyó hónap 24-ig.
          </div>
        </div>
      </div>
    </div>

    <!-- Terms -->
    <div class="subscription-summary-group">
      <div class="subscription-summary-group-title">Nyilatkozatok</div>
      <div class="nso-form-checkboxes">
        <kesma-form-control class="checkbox">
          <label class="nso-form-checkbox" for="terms">
            <input formControlName="terms" id="terms" type="checkbox" />
            <span
              >A megrendelésemmel hozzájárulok az adataim kezeléséhez. Tájékoztatjuk, hogy a megrendelés véglegesítéséhez szükség van az Ön adatkezelési
              hozzájárulásához. A kiadvány tekintetében minden esetben az N.S. Média és Vagyonkezelő Kft. számít adatkezelőnek. A részletes adatvédelmi
              tájékoztatót a
              <a [routerLink]="['/adatvedelmi-tajekoztato']" class="subscription-summary-terms-link" target="_blank">Nemzeti Sport Online</a>
              oldalon találhatja meg. (Nem adjuk ki harmadik személynek, erre mindösszesen azért van szükség, hogy az adatait kezelni tudjuk rendszerünkben.)
              *</span
            >
          </label>
        </kesma-form-control>

        <kesma-form-control class="checkbox">
          <label class="nso-form-checkbox" for="confirmation">
            <input formControlName="confirmation" id="confirmation" type="checkbox" />
            <span>Megerősítem, hogy a fent megadott adatok alapján megrendelem a Nemzeti Sport napilapot. *</span>
          </label>
        </kesma-form-control>
      </div>
      <div class="subscription-summary-text">A megrendelés véglegesítése után a megadott elérhetőségeken tájékoztatjuk Önt a további lépésekről.</div>
    </div>

    <div *ngIf="error" class="general-form-error">
      {{ error }}
    </div>

    <nso-simple-button [disabled]="isLoading" [isSubmit]="true" round="round">{{ isLoading ? 'KÉREM, VÁRJON...' : 'MEGRENDELÉS »' }}</nso-simple-button>
  </form>
</div>

<nso-popup
  (resultEvent)="handlePopupResult()"
  *ngIf="isSubmitted"
  [acceptButtonLabel]="'VISSZA A FŐOLDALRA'"
  [showCancelButton]="false"
  [title]="'Sikeres megrendelés'"
>
  Megrendelését sikeresen leadta! Kollégánk hamarosan keresi Önt a további lépésekkel kapcsolatban a megadott elérhetőségek egyikén.
</nso-popup>
