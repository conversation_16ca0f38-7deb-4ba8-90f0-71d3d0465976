@use 'shared' as *;

.subscription-summary {
  max-width: 600px;
  margin: 0 auto;

  &-title {
    font-weight: 700;
    font-size: 18px;
    line-height: 20px;
    margin-bottom: 20px;
  }

  &-group {
    margin-bottom: 30px;

    &-title {
      font-weight: 700;
      font-size: 14px;
      line-height: 20px;
      margin-bottom: 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      &-link {
        color: var(--kui-red-200);
        text-decoration: underline;
        font-weight: normal;
        cursor: pointer;

        .icon {
          width: 12px;
          height: 12px;
          margin-right: 5px;
        }
      }
    }
  }

  &-value {
    font-size: 14px;
    line-height: 20px;

    &-info {
      font-size: 12px;
      line-height: 16px;
    }

    table {
      width: 100%;

      th,
      td {
        &:last-child {
          text-align: right;
        }
      }

      th {
        margin-bottom: 10px;
        padding-bottom: 5px;
        color: var(--kui-gray-550);
      }

      td {
        padding: 5px 0;
      }

      tbody {
        tr:last-child {
          td {
            padding-bottom: 10px;
          }
        }
      }

      tfoot {
        td {
          border-top: 1px solid var(--kui-gray-200);
          padding-top: 5px;
          font-weight: bold;
          color: var(--kui-red-200);
        }
      }
    }
  }

  &-terms {
    font-size: 14px;
    line-height: 20px;

    &-link {
      color: var(--kui-red-200);
      text-decoration: underline;
    }
  }

  &-text {
    text-align: center;
    font-size: 14px;
    line-height: 18px;
    margin: 50px 0;
  }

  .general-form-error {
    margin-bottom: 20px;
    text-align: center;
    color: var(--kui-red-200);
  }

  nso-date-time-picker {
    width: 250px;
    display: block;
  }

  ::ng-deep {
    .nso-form-input {
      margin-bottom: 10px;
    }
  }
}
