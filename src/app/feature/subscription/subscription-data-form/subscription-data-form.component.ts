import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { emailValidator, KesmaFormControlComponent, markControlsTouched, nonWhitespaceOnlyValidator } from '@trendency/kesma-ui';
import { SubscriptionService } from '../subscription.service';
import { SubscriptionDataFormStepData, SubscriptionSessionData, SubscriptionStepType } from '../subscription.definitions';
import { NgIf } from '@angular/common';
import { NsoSimpleButtonComponent } from '../../../shared';

@Component({
  selector: 'app-subscription-data-form',
  templateUrl: './subscription-data-form.component.html',
  styleUrls: ['./subscription-data-form.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [ReactiveFormsModule, NgIf, KesmaFormControlComponent, NsoSimpleButtonComponent],
})
export class SubscriptionDataFormComponent implements OnInit {
  formGroup: FormGroup | undefined;

  constructor(
    private readonly formBuilder: FormBuilder,
    private readonly subscriptionService: SubscriptionService,
    private readonly cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.initForm();
  }

  initForm(): void {
    const data: SubscriptionSessionData | undefined = this.subscriptionService.sessionData;

    this.formGroup = this.formBuilder.group({
      email: [data?.email ?? null, [Validators.required, emailValidator]],
      phoneNumber: [data?.phoneNumber ?? null, [Validators.required, Validators.pattern('^[0-9]{9,11}$')]],
      billingAddress: this.formBuilder.group({
        name: [data?.billingAddress?.name ?? null, [Validators.required, nonWhitespaceOnlyValidator]],
        zip: [data?.billingAddress?.zip ?? null, [Validators.required, Validators.pattern('^[0-9]{4,5}$')]],
        city: [data?.billingAddress?.city ?? null, [Validators.required, nonWhitespaceOnlyValidator]],
        address: [data?.billingAddress?.address ?? null, [Validators.required, nonWhitespaceOnlyValidator]],
        tax: [data?.billingAddress?.tax ?? null, [Validators.pattern('^[0-9]{8}-[0-9]{1}-[0-9]{2}$')]],
      }),
      shippingAddress: this.formBuilder.group({
        name: [
          {
            value: data?.shippingAddress?.name ?? null,
            disabled: data?.isShippingAddressSame ?? true,
          },
          [Validators.required, nonWhitespaceOnlyValidator],
        ],
        zip: [
          {
            value: data?.shippingAddress?.zip ?? null,
            disabled: data?.isShippingAddressSame ?? true,
          },
          [Validators.required, Validators.pattern('^[0-9]{4,5}$')],
        ],
        city: [
          {
            value: data?.shippingAddress?.city ?? null,
            disabled: data?.isShippingAddressSame ?? true,
          },
          [Validators.required, nonWhitespaceOnlyValidator],
        ],
        address: [
          {
            value: data?.shippingAddress?.address ?? null,
            disabled: data?.isShippingAddressSame ?? true,
          },
          [Validators.required, nonWhitespaceOnlyValidator],
        ],
      }),
      isShippingAddressSame: [data?.isShippingAddressSame ?? true],
    });

    this.handleFormValueChanges();
  }

  handleFormValueChanges(): void {
    this.formGroup?.get('isShippingAddressSame')?.valueChanges.subscribe((isShippingAddressSame: boolean) => {
      Object.values((this.formGroup?.get('shippingAddress') as FormGroup)?.controls).forEach((control: AbstractControl) => {
        if (isShippingAddressSame) {
          control.disable();
        } else {
          control.enable();
        }
      });

      this.cdr.detectChanges();
    });
  }

  navigateToNextStep(): void {
    if (this.formGroup) {
      markControlsTouched(this.formGroup);
    }

    if (!this.formGroup?.valid) {
      return;
    }

    this.subscriptionService.handleStep(SubscriptionStepType.DATA_FORM, {
      ...this.formGroup?.value,
      shippingAddress: this.formGroup?.value.shippingAddress ?? {},
    } as SubscriptionDataFormStepData);
  }
}
