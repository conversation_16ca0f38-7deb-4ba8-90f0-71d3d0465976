<div class="subscription-data">
  <form (ngSubmit)="navigateToNextStep()" *ngIf="formGroup" [formGroup]="formGroup" class="subscription-data-form">
    <!-- User data -->
    <div class="subscription-data-form-group">
      <div class="subscription-data-form-group-title">Előfizetői adatok</div>

      <!-- E-mail address -->
      <div class="nso-form-row">
        <kesma-form-control>
          <label class="nso-form-label" for="email">E-mail cím <strong>*</strong></label>
          <input class="nso-form-input" formControlName="email" id="email" type="text" />
        </kesma-form-control>
      </div>

      <!-- Phone number -->
      <div class="nso-form-row">
        <kesma-form-control>
          <label class="nso-form-label" for="phoneNumber">Telefonszám <strong>*</strong></label>
          <input class="nso-form-input" formControlName="phoneNumber" id="phoneNumber" type="text" />
        </kesma-form-control>
        <small class="nso-form-small">Csa<PERSON> s<PERSON>, például: 0611234567</small>
      </div>
    </div>

    <!-- Billing data -->
    <div class="subscription-data-form-group">
      <div class="subscription-data-form-group-title">Számlázási adatok</div>
      <ng-container formGroupName="billingAddress">
        <div class="nso-form-row">
          <kesma-form-control>
            <label class="nso-form-label" for="billingAddressName">Név <strong>*</strong></label>
            <input class="nso-form-input" formControlName="name" id="billingAddressName" type="text" />
          </kesma-form-control>
          <small class="nso-form-small">Teljes név vagy jogi személy esetén a cég neve</small>
        </div>
        <div class="row">
          <div class="col-12 col-sm-4">
            <div class="nso-form-row">
              <kesma-form-control class="only-border">
                <label class="nso-form-label" for="billingAddressZip">Irányítószám <strong>*</strong></label>
                <input class="nso-form-input" formControlName="zip" id="billingAddressZip" type="text" />
              </kesma-form-control>
            </div>
          </div>
          <div class="col-12 col-sm-8">
            <div class="nso-form-row">
              <kesma-form-control>
                <label class="nso-form-label" for="billingAddressCity">Település <strong>*</strong></label>
                <input class="nso-form-input" formControlName="city" id="billingAddressCity" type="text" />
              </kesma-form-control>
            </div>
          </div>
        </div>
        <div class="nso-form-row">
          <kesma-form-control>
            <label class="nso-form-label" for="billingAddressAddress">Cím <strong>*</strong></label>
            <input class="nso-form-input" formControlName="address" id="billingAddressAddress" type="text" />
          </kesma-form-control>
        </div>
      </ng-container>

      <!-- Tax info -->
      <div class="nso-form-row" formGroupName="billingAddress">
        <kesma-form-control>
          <label class="nso-form-label" for="billingAddressTax">Adószám</label>
          <input class="nso-form-input" formControlName="tax" id="billingAddressTax" type="text" />
        </kesma-form-control>
        <small class="nso-form-small">Opcionális, jogi személy esetén kötelező</small>
      </div>
    </div>

    <!-- Shipping data -->
    <div class="subscription-data-form-group">
      <div class="subscription-data-form-group-title">Szállítási adatok</div>
      <div class="nso-form-checkboxes">
        <kesma-form-control class="checkbox">
          <label class="nso-form-checkbox" for="isShippingAddressSame">
            <input formControlName="isShippingAddressSame" id="isShippingAddressSame" type="checkbox" />
            <span>A szállítási adatok megegyeznek a számlázási adatokkal.</span>
          </label>
        </kesma-form-control>
      </div>
      <ng-container *ngIf="!formGroup.get('isShippingAddressSame')?.value" formGroupName="shippingAddress">
        <div class="nso-form-row">
          <kesma-form-control>
            <label class="nso-form-label" for="shippingAddressName">Név <strong>*</strong></label>
            <input class="nso-form-input" formControlName="name" id="shippingAddressName" type="text" />
          </kesma-form-control>
          <small class="nso-form-small">Teljes név vagy jogi személy esetén a cég neve</small>
        </div>
        <div class="row">
          <div class="col-12 col-sm-4">
            <div class="nso-form-row">
              <kesma-form-control class="only-border">
                <label class="nso-form-label" for="shippingAddressZip">Irányítószám <strong>*</strong></label>
                <input class="nso-form-input" formControlName="zip" id="shippingAddressZip" type="text" />
              </kesma-form-control>
            </div>
          </div>
          <div class="col-12 col-sm-8">
            <div class="nso-form-row">
              <kesma-form-control>
                <label class="nso-form-label" for="shippingAddressCity">Település <strong>*</strong></label>
                <input class="nso-form-input" formControlName="city" id="shippingAddressCity" type="text" />
              </kesma-form-control>
            </div>
          </div>
        </div>
        <div class="nso-form-row">
          <kesma-form-control>
            <label class="nso-form-label" for="shippingAddressAddress">Cím <strong>*</strong></label>
            <input class="nso-form-input" formControlName="address" id="shippingAddressAddress" type="text" />
          </kesma-form-control>
        </div>
      </ng-container>
    </div>

    <div class="subscription-data-form-button">
      <nso-simple-button [isSubmit]="true" round="round">TOVÁBB AZ ÁTTEKINTÉSRE »</nso-simple-button>
    </div>
  </form>
</div>
