export enum SubscriptionStepType {
  SELECT_PRODUCT = 1,
  DATA_FORM = 2,
  SUMMARY = 3,
}

export interface SubscriptionStep {
  type: SubscriptionStepType;
  name: string;
}

export interface SubscriptionAddress {
  name: string;
  zip: string;
  city: string;
  address: string;
  tax?: string;
}

export interface SubscriptionSelectProductStepData {
  selectedCampaign?: SubscriptionCampaign;
  selectedProduct?: SubscriptionProduct;
}

export interface SubscriptionDataFormStepData {
  billingAddress?: SubscriptionAddress;
  shippingAddress?: SubscriptionAddress;
  isShippingAddressSame?: boolean;
  phoneNumber?: string;
  email?: string;
}

export interface SubscriptionSummaryStepData {
  paymentMethod?: SubscriptionPaymentMethod;
  startDate?: string;
  confirmation?: boolean;
  terms?: boolean;
}

export interface SubscriptionSessionData extends SubscriptionSelectProductStepData, SubscriptionDataFormStepData, SubscriptionSummaryStepData {
  nextStep?: SubscriptionStepType;
}

export interface SubscriptionCampaign {
  id: string;
  name: string;
  products: SubscriptionProduct[];
}

export interface SubscriptionProduct {
  id: string;
  name: string;
  periodInDay: number;
  price: number;
}

export enum SubscriptionPaymentMethod {
  BANK_TRANSFER = 'BANK_TRANSFER',
  POSTAL_CHECK = 'POSTAL_CHECK',
}

export interface BackendSubscriptionSaveRequest {
  campaignId?: string;
  productId?: string;
  email?: string;
  phoneNumber?: string;
  invoiceName?: string;
  invoiceZip?: string;
  invoiceCity?: string;
  invoiceAddress?: string;
  shippingName?: string;
  shippingZip?: string;
  shippingCity?: string;
  shippingAddress?: string;
  taxNumber?: string;
  paymentMethod?: string;
  startDate?: string;
  terms?: number;
  confirmation?: number;
  recaptcha?: string;
}

export const nsoSubscriptionCampaigns: SubscriptionCampaign[] = [
  {
    id: 'NS-6-NAP',
    name: '6 napos Nemzeti Sport',
    products: [
      {
        id: 'NS-6-NAP-1-HO',
        name: '1 hónapos előfizetés',
        periodInDay: 30,
        price: 8000,
      },
      {
        id: 'NS-6-NAP-3-HO',
        name: '3 hónapos előfizetés',
        periodInDay: 90,
        price: 23300,
      },
      {
        id: 'NS-6-NAP-6-HO',
        name: '6 hónapos előfizetés',
        periodInDay: 180,
        price: 44600,
      },
      {
        id: 'NS-6-NAP-12-HO',
        name: '12 hónapos előfizetés',
        periodInDay: 365,
        price: 85300,
      },
    ],
  },
  {
    id: 'NS-7-NAP',
    name: '7 napos Nemzeti Sport',
    products: [
      {
        id: 'NS-7-NAP-1-HO',
        name: '1 hónapos előfizetés',
        periodInDay: 30,
        price: 9200,
      },
      {
        id: 'NS-7-NAP-3-HO',
        name: '3 hónapos előfizetés',
        periodInDay: 90,
        price: 26800,
      },
      {
        id: 'NS-7-NAP-6-HO',
        name: '6 hónapos előfizetés',
        periodInDay: 180,
        price: 51900,
      },
      {
        id: 'NS-7-NAP-12-HO',
        name: '12 hónapos előfizetés',
        periodInDay: 365,
        price: 99700,
      },
    ],
  },
];
