import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import { SeoService } from '@trendency/kesma-core';
import { createNSOTitle, defaultMetaInfo } from '../../../../shared';

@Component({
  selector: 'app-newsletter-confirm-page',
  templateUrl: './newsletter-confirm-page.component.html',
  styleUrls: ['./newsletter-confirm-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class NewsletterConfirmPageComponent implements OnInit {
  constructor(private readonly seo: SeoService) {}

  ngOnInit(): void {
    const title = createNSOTitle('H<PERSON>rlevél megerősítés');
    this.seo.setMetaData({
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    });
  }
}
