import { ChangeDetectionStrategy, Component, OnDestroy, OnInit } from '@angular/core';
import { Subscription } from 'rxjs';
import { ActivatedRoute } from '@angular/router';
import { SeoService } from '@trendency/kesma-core';
import { createNSOTitle, defaultMetaInfo } from '../../../../shared';
import { NgIf } from '@angular/common';

@Component({
  selector: 'app-newsletter-success-page',
  templateUrl: './newsletter-success-page.component.html',
  styleUrls: ['./newsletter-success-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf],
})
export class NewsletterSuccessPageComponent implements OnInit, OnDestroy {
  pageTextSuffix = '';
  routeSubscription$ = new Subscription();

  constructor(
    private readonly route: ActivatedRoute,
    private readonly seo: SeoService
  ) {}

  ngOnInit(): void {
    this.routeSubscription$ = this.route.data.subscribe((data) => {
      this.pageTextSuffix = data['pageTextSuffix'];
      const title = createNSOTitle(`Hírlevél ${this.pageTextSuffix}`);
      this.seo.setMetaData({
        ...defaultMetaInfo,
        title: title,
        ogTitle: title,
      });
    });
  }

  ngOnDestroy(): void {
    this.routeSubscription$.unsubscribe();
  }
}
