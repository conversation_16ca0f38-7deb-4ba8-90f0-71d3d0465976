@use 'shared' as *;

.wrapper {
  padding: 65px 0;
  margin: 0 20px;
}

.form-wrapper {
  width: 630px;
  margin: auto;
  background-color: var(--kui-white);
  padding: 40px 80px;

  @include media-breakpoint-down(md) {
    width: 100%;
    padding: 40px 20px;
    border-radius: 10px;
  }

  input[type='checkbox'] {
    accent-color: var(--kui-red-400);
    width: 24px;
    height: 24px;
  }

  .close {
    margin: 30px 0;
    text-decoration: underline;
    font-weight: 400;
    font-size: 16px;
    color: var(--kui-gray-550);
    cursor: pointer;

    @include media-breakpoint-down(md) {
      margin-bottom: 0;
    }
  }

  .newsletter-signup-header {
    max-width: 630px;
    text-align: center;
    margin: auto;

    @include media-breakpoint-down(md) {
      padding: 0;
    }

    &-title {
      font-weight: 700;
      font-size: 38px;
      font-family: var(--kui-font-condensed);
      text-transform: uppercase;
      margin-bottom: 20px;

      @include media-breakpoint-down(md) {
        line-height: 40px;
        font-size: 32px;
      }
    }

    &-text {
      font-weight: 400;
      font-size: 16px;
      margin-bottom: 20px;
      line-height: 25px;
      color: var(--kui-gray-350);
      padding: 0 30px;

      @include media-breakpoint-down(md) {
        padding: 0;
      }
    }
  }

  .automizy-form-form {
    display: flex;
    flex-direction: column;
    font-family: var(--kui-font-primary);
    font-weight: 700;

    .automizy-form-fields {
      display: flex;
      flex-direction: column;
      align-items: center;

      .automizy-form-input-box {
        width: 100%;

        .automizy-form-input-label {
          font-size: 16px;
          font-weight: 400;
          line-height: 25px;
          color: var(--kui-gray-550);
          font-family: var(--kui-font-primary);
          margin-bottom: 12px;

          &.marketing {
            font-family: var(--kui-font-primary);
            font-size: 12px;
            line-height: 20px;
            margin-bottom: 20px;
            font-weight: 400;
          }
        }

        .automizy-form-input {
          width: 100%;
          border: 1px solid var(--kui-gray-200);
          padding: 15px 30px 15px 15px;
          margin-bottom: 20px;
        }

        &.marketing {
          display: flex;
          align-items: flex-start;
          gap: 15px;

          input[type='checkbox'] {
            accent-color: var(--kui-red-400);
            width: 24px;
            height: 24px;
          }
        }
      }

      .automizy-form-button-box {
        .automizy-form-button {
          font-family: var(--kui-font-primary);
          width: 470px;
          font-weight: 700;
          align-self: center;
          margin-top: 20px;
          padding: 15px 30px;
          background-color: var(--kui-red-400);
          border-radius: 5px;
          font-size: 20px;
          color: var(--kui-white);
          cursor: pointer;

          @include media-breakpoint-down(md) {
            font-size: 14px;
            width: auto;
          }
        }
      }

      .automizy-form-privacy {
        display: flex;
        align-items: flex-start;
        gap: 15px;
        font-family: var(--kui-font-primary);
        font-size: 14px;
        line-height: 20px;
        margin-bottom: 20px;
        font-weight: 400;

        &-text {
          font-size: 12px;
          font-weight: 400;
        }

        .terms {
          cursor: pointer;
          color: var(--kui-red-400);
          text-decoration: underline;
        }
      }
    }
  }
}
