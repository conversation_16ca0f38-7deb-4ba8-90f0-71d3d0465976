import { ChangeDetectionStrategy, Component, Inject, OnInit, Renderer2 } from '@angular/core';
import { BypassPipe, IMetaData, SeoService } from '@trendency/kesma-core';
import { DOCUMENT } from '@angular/common';
import { createNSOTitle, defaultMetaInfo } from '../../../../shared';
import { RouterLink } from '@angular/router';
import { createCanonicalUrlForPageablePage } from '@trendency/kesma-ui';

@Component({
  selector: 'app-newsletter-signup-page',
  templateUrl: './newsletter-signup-page.component.html',
  styleUrls: ['./newsletter-signup-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [BypassPipe, RouterLink],
})
export class NewsletterSignupPageComponent implements OnInit {
  recaptcha = 'https://www.google.com/recaptcha/api.js';
  action = 'https://api.automizy.com/v2/forms/submit/9xccNZwfWPkUZfAH1jiQIXeryEFTGtSsw1JfNGejoHM/kMDy6VgiM6V9u8qXo482zgHDH08';

  constructor(
    private readonly seo: SeoService,
    private readonly renderer2: Renderer2,
    @Inject(DOCUMENT) private readonly _document: Document
  ) {}

  ngOnInit(): void {
    this.renderRecaptcha();
    this.setMetaData();
  }

  private renderRecaptcha(): void {
    const script = this.renderer2.createElement('script');
    script.type = 'text/javascript';
    script.src = this.recaptcha;
    script.text = ``;
    this.renderer2.appendChild(this._document.body, script);
  }

  private setMetaData(): void {
    const title = createNSOTitle('Hírlevél-feliratkozás');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    };
    this.seo.setMetaData(metaData);
    const canonical = createCanonicalUrlForPageablePage('hirlevel/feliratkozas');
    if (canonical) {
      this.seo.updateCanonicalUrl(canonical);
    }
  }
}
