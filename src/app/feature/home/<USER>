import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import { ActivatedRoute, Data } from '@angular/router';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import { LayoutApiData, LayoutPageType } from '@trendency/kesma-ui';
import { map, Observable } from 'rxjs';
import { addHomeAds, createNSOTitle, defaultMetaInfo } from '../../shared';
import { AsyncPipe, NgIf } from '@angular/common';
import { LayoutComponent } from '../layout/components/layout/layout.component';

@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, AsyncPipe, LayoutComponent],
})
export class HomeComponent implements OnInit {
  layoutApiData$: Observable<LayoutApiData> = this.route.data.pipe(
    map((data: Data | { layoutData: LayoutApiData }) => {
      this.seo.setMetaData({
        ...defaultMetaInfo,
        keywords: 'hírek, információk, sporthírek, sztárok, életmód, időjárás, programajánló',
      });

      const layoutData = addHomeAds(data.layoutData);
      return layoutData;
    })
  );

  LayoutPageType = LayoutPageType;

  constructor(
    private readonly route: ActivatedRoute,
    private readonly seo: SeoService
  ) {
    this.seo.updateCanonicalUrl('');
  }

  ngOnInit(): void {
    this.setMetaData();
  }

  private setMetaData(): void {
    const title = createNSOTitle('');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    };
    this.seo.setMetaData(metaData);
    this.seo.updateCanonicalUrl('');
  }
}
