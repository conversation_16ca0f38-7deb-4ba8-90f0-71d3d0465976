import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { Observable, throwError } from 'rxjs';
import { LayoutApiData, LayoutService } from '@trendency/kesma-ui';
import { catchError, map } from 'rxjs/operators';

@Injectable()
export class HomeResolver {
  constructor(
    private readonly layoutService: LayoutService,
    private readonly router: Router
  ) {}

  resolve(): Observable<LayoutApiData> {
    return this.layoutService.getHomePage().pipe(
      catchError((err) => {
        this.router.navigate(['/', '500'], {
          skipLocationChange: true,
        });
        return throwError(err);
      }),
      map(({ data }) => data)
    );
  }
}
