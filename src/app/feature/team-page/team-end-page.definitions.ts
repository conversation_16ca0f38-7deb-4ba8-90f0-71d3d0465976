import { BackendArticleSearchResult } from '@trendency/kesma-ui';

export type TeamPage = Readonly<{
  schedules: Schedule[];
  competition: CompetitionTeam;
  teamArticles: BackendArticleSearchResult[];
}>;

export type CompetitionTeam = Readonly<{
  id: string;
  title: string;
  manager: string;
  logo: string;
  team: Team;
  players: TeamPlayer[];
}>;

export type Station = Readonly<{
  id: string;
  logo: string;
  officialPage: string;
  title: string;
}>;

export type Schedule = Readonly<{
  id: string;
  scheduleTime: string;
  information: string;
  homeScore: string;
  awayScore: string;
  competition: Competition;
  homeTeam: ScheduleTeam;
  awayTeam: ScheduleTeam;
  scheduleStatus?: string;
  tvStation?: Station;
  round: string;
  playoffGroup: string;
  phase: Phase;
  liveEvents?: LiveEvent[];
  slug: string;
  referees: Referee[];
  facility: Facility;
  visitors: number;
  scheduleDate: TimeZone;
  assistants: Referee[];
  reporter: string;
}>;

type Referee = { firstName: string; lastName: string };

export type Phase = Readonly<{
  id: string;
  name: string;
}>;

export type Competition = Readonly<{
  id: string;
  logo: string;
  publicTitle: string;
  slug: string;
  title: string;
}>;

export type LiveEvent = Readonly<{
  id: string;
  eventText: string;
  minute: string;
  scheduleEvent: ScheduleEvent;
  schedule: Schedule;
  competitionTeam: ScheduleTeam;
  competitionTeamPlayer: TeamPlayer;
  substituteCompetitionTeamPlayer?: TeamPlayer;
}>;

export type ScheduleEvent = Readonly<{
  id: string;
  icon: string;
  title: string;
}>;

export type ScheduleTeam = Readonly<{
  id: string;
  title: string;
  logo: string;
  publicTitle?: string;
  team: Team;
}>;

export type Team = Readonly<{
  id: string;
  name: string;
  logo: string;
  slug: string;
  shortName?: string;
  officialPage?: string;
  instagram?: string;
  twitter?: string;
  facebook?: string;
  sport?: string;
  facilities?: Facility[];
  columns?: MatchRelatedColumns[];
}>;

export type Facility = Readonly<{
  address: string;
  capacity: number;
  id: string;
  latitude: string;
  longitude: string;
  publicTitle: string;
  title: string;
}>;

export type TeamPlayer = Readonly<{
  id: string;
  lastName: string;
  firstName: string;
  publicName: string;
  placeOfBirth: string;
  birthDate: TimeZone;
  nationality: string;
  height: number;
  weight: number;
  jersey: string;
  playerPosition: string;
}>;

export type TimeZone = Readonly<{
  date: string;
  timezone_type: number;
  timezone: string;
}>;

export type TeamTimeLine = Readonly<{
  positionName: string;
  players: string[];
}>;

export type MatchList = Readonly<{
  teamName: string;
  hasTitle: boolean;
  matches: Array<Match>;
}>;

export type Match = Readonly<{
  result: MatchResult;
  teamA: TeamDetails;
  teamB: TeamDetails;
  championshipName: string;
}>;

export type MatchRelatedColumns = Readonly<{
  id: string;
  title: string;
  slug: string;
}>;

export enum MatchResult {
  VICTORY = 'Győzelem',
  DRAW = 'Döntetlen',
  DEFEAT = 'Vereség',
}

export type TeamDetails = Readonly<{
  teamName: string;
  teamLogo: string;
  score?: number;
  short?: string;
}>;
