import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot } from '@angular/router';
import { forkJoin, Observable, of } from 'rxjs';
import { TeamPageService } from './team-page.service';
import { CompetitionTeam, Schedule, TeamPage } from '../team-end-page.definitions';
import { map, switchMap } from 'rxjs/operators';
import { ApiResult } from '@trendency/kesma-ui';
import { ApiService } from '../../../shared';

@Injectable()
export class NewsPageResolver {
  constructor(
    private readonly teamPageService: TeamPageService,
    private readonly apiService: ApiService
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<TeamPage> {
    const teamSlug = route.params['teamSlug'];
    const competitionSlug = route.params['competitionSlug'];
    return forkJoin({
      competition: this.teamPageService.getTeam(teamSlug, competitionSlug).pipe(map((result: ApiResult<CompetitionTeam>) => result?.data)),
      teamArticles: this.teamPageService.getTeam(teamSlug, competitionSlug).pipe(
        map(({ data: { team } }) => team),
        switchMap((team) => {
          if (team?.columns?.length) {
            const teamRelatedColumns = team?.columns;
            return this.apiService
              .searchByKeyword(
                '',
                0,
                5,
                teamRelatedColumns.map((column) => column?.slug)
              )
              .pipe(map(({ data }) => data));
          }
          return of([]);
        })
      ),
      schedules: this.teamPageService
        .getTeamSchedules(teamSlug, competitionSlug)
        .pipe(map((result: ApiResult<{ schedules: Schedule[] }>) => result?.data?.schedules)),
    });
  }
}
