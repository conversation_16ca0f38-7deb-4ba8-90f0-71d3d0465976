import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ReqService } from '@trendency/kesma-core';
import { CompetitionTeam, Schedule } from '../team-end-page.definitions';
import { ApiResult } from '@trendency/kesma-ui';

@Injectable({
  providedIn: 'root',
})
export class TeamPageService {
  constructor(private readonly reqService: ReqService) {}

  getTeam(teamSlug: string, competitionSlug: string): Observable<ApiResult<CompetitionTeam>> {
    return this.reqService.get<ApiResult<CompetitionTeam>>(`/sport/team/${teamSlug}/${competitionSlug}`);
  }

  getTeamSchedules(teamSlug: string, competitionSlug: string, limit = 5): Observable<ApiResult<{ schedules: Schedule[] }>> {
    return this.reqService.get(`/sport/team/${teamSlug}/${competitionSlug}/schedules?limit=${limit}`);
  }
}
