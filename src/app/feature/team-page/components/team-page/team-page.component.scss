@use 'shared' as *;

:host {
  display: block;
  font-family: var(--kui-font-condensed);
  margin: 30px 0;

  .related-articles {
    color: var(--kui-red-400);
    font-size: 38px;
    font-weight: 700;
    margin-bottom: 25px;
  }

  .wrapper {
    display: flex;
    flex-direction: column;
    gap: 50px;
  }

  .desktop {
    @include media-breakpoint-down(md) {
      display: none;
    }
  }

  .mobile {
    @include media-breakpoint-up(md) {
      display: none;
    }
  }
}

::ng-deep {
  .team-stats {
    width: 100%;
    font-size: 16px;

    &-header {
      background-color: var(--kui-red-400);
      height: 61px;
      padding: 16px 32px;
      font-size: 24px;
      color: var(--kui-white);
    }

    &-score {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
    }

    tr {
      height: 61px;
      color: var(--kui-gray-550);
    }

    tr:nth-child(even) {
      background-color: var(--kui-gray-75);
    }

    .first-cell {
      padding-left: 16px;
    }
  }
}

.team-basic-data {
  display: flex;
  flex-direction: row;
  justify-content: space-between;

  @include media-breakpoint-down(md) {
    flex-direction: column;
  }

  .icon-link {
    @include icon('icons/icon-link.svg');
    width: 24px;
    height: 24px;
    margin: 0;
  }
  .icon-instagram {
    @include icon('icons/icon-instagram.svg');
    width: 24px;
    height: 24px;
  }
  .icon-twitter {
    @include icon('icons/icon-tw.svg');
    width: 24px;
    height: 24px;
  }
  .icon-facebook {
    @include icon('icons/icon-facebook.svg');
    width: 24px;
    height: 24px;
  }

  &-left {
    display: flex;
    column-gap: 16px;
    flex-direction: column;
  }
  &-right {
    display: flex;
    align-items: flex-start;
  }

  &-title {
    display: flex;
    flex-direction: row;
    align-items: center;
    column-gap: 16px;
    margin-bottom: 32px;

    @include media-breakpoint-down(md) {
      justify-content: flex-start;
      margin-bottom: 20px;
      column-gap: 8px;
    }

    &-icon {
      width: 64px;
      height: 64px;
      object-fit: cover;
      border-radius: 50%;

      @include media-breakpoint-down(md) {
        width: 37px;
        height: 37px;
      }
    }
    &-link {
      color: var(--kui-gray-600);
      font-family: var(--kui-font-condensed);
      font-size: 48px;
      text-transform: uppercase;
      line-height: 41px;
      font-weight: 700;
      white-space: nowrap;

      @include media-breakpoint-down(md) {
        font-size: 32px;
      }
    }
  }

  &-content {
    &-text {
      font-size: 16px;
      line-height: 25px;
      font-weight: 400;
      margin-bottom: 16px;
      color: var(--kui-gray-550);

      @include media-breakpoint-down(md) {
        margin-bottom: 8px;
      }
    }

    &-link {
      display: inline-flex;
      gap: 6px;
      color: var(--kui-red-400);
      font-size: 16px;
      font-style: normal;
      font-weight: 700;
    }
  }

  &-highlighted-text {
    font-size: 16px;
    font-weight: 700;
  }

  &-menu {
    display: flex;
    column-gap: 20px;
    margin-top: 20px;

    @include media-breakpoint-down(md) {
      gap: 10px;
      flex-wrap: wrap;
      margin-top: 4px;
    }

    &-link {
      color: var(--kui-gray-550);
      font-size: 16px;
      line-height: 25px;
      font-weight: 400;
      font-family: var(--kui-font-primary);
      cursor: pointer;

      @include media-breakpoint-down(md) {
        font-size: 13px;
      }
    }
  }

  &-social-icons {
    display: flex;
    column-gap: 10px;

    .icon {
      width: 48px;
      height: 48px;

      &:first-of-type {
        margin-top: 16px;
      }
    }
  }
}
