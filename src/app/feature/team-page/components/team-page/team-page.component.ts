import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>orO<PERSON>, <PERSON><PERSON><PERSON>, ViewportScroller } from '@angular/common';
import { ChangeDetectionStrategy, Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import { ApiResult, createCanonicalUrlForPageablePage, mapBackendArticleDataToArticleCard } from '@trendency/kesma-ui';
import { combineLatest, Observable } from 'rxjs';
import { map, tap } from 'rxjs/operators';
import {
  ArticleCardType,
  createNSOTitle,
  defaultMetaInfo,
  mapToMatches,
  mapToPlayers,
  NsoArticleCardComponent,
  NsoMatchStatsTeamsComponent,
  NsoTeamLineupsTableComponent,
  RadioService,
} from '../../../../shared';
import { CompetitionTeam, TeamPage, TeamTimeLine } from '../../team-end-page.definitions';

@Component({
  selector: 'app-team-page',
  templateUrl: './team-page.component.html',
  styleUrls: ['./team-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, AsyncPipe, NgForOf, NsoMatchStatsTeamsComponent, NsoTeamLineupsTableComponent, NsoArticleCardComponent],
})
export class TeamPageComponent {
  readonly routeData$ = this.route.data as Observable<ApiResult<TeamPage>>;
  readonly competition$ = this.routeData$.pipe(
    map(({ data }) => data.competition),
    tap((competition) => this.setMeta(competition))
  );
  readonly schedules$ = this.routeData$.pipe(map(({ data }) => data.schedules));
  readonly team$ = this.routeData$.pipe(map(({ data }) => data.competition.team));
  readonly players$ = this.routeData$.pipe(
    map(
      ({
        data: {
          competition: { players },
        },
      }) => mapToPlayers(players)
    )
  );
  readonly teamArticles$ = this.routeData$.pipe(map(({ data }) => data.teamArticles.map(mapBackendArticleDataToArticleCard)));
  readonly timeLines$ = this.competition$.pipe(map((competition) => this.mapToTimeLines(competition)));
  readonly matches$ = combineLatest([this.team$, this.schedules$]).pipe(map(([team, schedule]) => mapToMatches(schedule, true, team)));

  subMenus = [
    { title: 'Statisztika', id: 'statistics' },
    { title: 'Csapat', id: 'team' },
    { title: 'Kapcsolódó cikkek', id: 'relatedArticles' },
  ];

  readonly ArticleCardType = ArticleCardType;

  constructor(
    private readonly route: ActivatedRoute,
    private readonly viewportScroller: ViewportScroller,
    public readonly radioService: RadioService,
    private readonly seo: SeoService
  ) {}

  public scrollTo(anchorId: string): void {
    this.viewportScroller.scrollToAnchor(anchorId);
  }

  private mapToTimeLines(competition: CompetitionTeam): TeamTimeLine[] {
    return competition.players.map((player) => ({
      players: [...competition.players.filter(({ playerPosition }) => playerPosition === player.playerPosition).map(({ publicName }) => publicName)],
      positionName: player.playerPosition,
    }));
  }

  private setMeta(competition: CompetitionTeam): void {
    const title = createNSOTitle(`Csapat ${competition?.team?.name ? ` - ${competition?.team?.name}` : ''}`);
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    };
    this.seo.setMetaData(metaData);
    const canonical = createCanonicalUrlForPageablePage('csapat');
    canonical && this.seo.updateCanonicalUrl(canonical);
  }
}
