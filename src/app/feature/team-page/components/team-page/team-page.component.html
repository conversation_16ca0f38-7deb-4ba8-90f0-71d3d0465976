<section>
  <div class="wrapper">
    <div class="team-basic-data">
      <div class="team-basic-data-left">
        <ng-container *ngIf="competition$ | async as competition">
          <div class="team-basic-data-title">
            <img
              class="team-basic-data-title-icon"
              [src]="competition?.team?.logo || './assets/images/nemzetisport.png'"
              [alt]="competition?.team?.name || ''"
              loading="lazy"
            />
            <a class="team-basic-data-title-link">
              {{ competition?.team?.name }}
            </a>
          </div>

          <div class="team-basic-data-content">
            <p class="team-basic-data-content-text">
              Stadion:
              <span class="team-basic-data-highlighted-text">
                <ng-container *ngFor="let facility of competition?.team?.facilities; last as last">
                  {{ facility.title }}<ng-container *ngIf="!last">, </ng-container>
                </ng-container>
              </span>
            </p>
            <p class="team-basic-data-content-text" *ngIf="competition?.manager as manager">
              Manager: <span class="team-basic-data-highlighted-text">{{ manager }}</span>
            </p>
            <p class="team-basic-data-content-text">
              Hivatalos weboldal:
              <a class="team-basic-data-content-link" [href]="competition?.team?.officialPage" target="_blank">
                {{ competition?.team?.officialPage }} <i class="icon icon-link"></i>
              </a>
            </p>
          </div>
        </ng-container>

        <div class="team-basic-data-social-icons" *ngIf="(competition$ | async)?.team as team">
          <a *ngIf="team?.instagram" [href]="team?.instagram" target="_blank">
            <i class="icon icon-instagram"></i>
          </a>
          <a *ngIf="team?.twitter" [href]="team?.twitter" target="_blank">
            <i class="icon icon-twitter"></i>
          </a>
          <a *ngIf="team?.facebook" [href]="team?.facebook" target="_blank">
            <i class="icon icon-facebook"></i>
          </a>
        </div>
      </div>

      <div class="team-basic-data-right">
        <ul class="team-basic-data-menu">
          <li class="team-basic-data-menu-item" *ngFor="let menu of subMenus">
            <a class="team-basic-data-menu-link" (click)="scrollTo(menu.id)"> {{ menu.title }}</a>
          </li>
        </ul>
      </div>
    </div>

    <div id="statistics" *ngIf="matches$ | async as matches">
      <nso-match-stats-teams id="match-stats" [data]="matches" [showTeamName]="false"></nso-match-stats-teams>
    </div>

    <div id="team" *ngIf="players$ | async as players">
      <nso-team-lineups-table [data]="players"></nso-team-lineups-table>
    </div>

    <div id="relatedArticles" *ngIf="teamArticles$ | async as teamRelatedArticles">
      <h2 class="related-articles">Kapcsolódó cikkek</h2>
      <ng-container *ngFor="let article of teamRelatedArticles">
        <nso-article-card [data]="article" [styleID]="ArticleCardType.FeaturedSidedImgBottomColumnTitleLead"></nso-article-card>
      </ng-container>
    </div>
  </div>
</section>
