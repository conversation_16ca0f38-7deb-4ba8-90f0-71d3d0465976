<section>
  <div class="main-wrap">
    <div class="search-wrap">
      <div class="search-bg"></div>
      <nso-search [data]="{ searchQuery: keyword }" (sendSearchKey)="onFilter($event)"></nso-search>
    </div>
    <section class="results">
      <ng-container *ngIf="articles$ | async as searchResults; else noResults">
        <div *ngFor="let result of searchResults; let i = index">
          <nso-article-card class="article-card" [data]="result" [styleID]="ArticleCardType.FeaturedSidedImgColumnTitleLead"></nso-article-card>
        </div>
      </ng-container>

      <ng-template #noResults>
        <ng-container *ngIf="isLoading; else showNoResults">
          <p>Betöltés...</p>
        </ng-container>
        <ng-template #showNoResults>
          <p>{{ noResultsText }}</p>
        </ng-template>
      </ng-template>
      <ng-container *ngIf="limitables$ | async as limitable">
        <nso-pager
          *ngIf="limitable?.pageMax! > 0"
          [rowAllCount]="limitable.rowAllCount!"
          [rowOnPageCount]="limitable.rowOnPageCount!"
          [hasFirstLastButton]="true"
          [showTotalPagesPositionAtRight]="true"
          [isCountPager]="true"
        >
        </nso-pager>
      </ng-container>
    </section>
  </div>
</section>
