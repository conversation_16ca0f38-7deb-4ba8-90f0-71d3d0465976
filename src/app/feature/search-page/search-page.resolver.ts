import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';

import { ArticleSearchResult, RedirectService } from '@trendency/kesma-ui';

import { searchResultToArticleCard } from '../../shared';
import { SearchPageData } from './search-page.definitions';
import { SearchPageService } from './search-page.service';

@Injectable()
export class SearchPageResolver {
  constructor(
    private readonly searchPageService: SearchPageService,
    private readonly router: Router,
    private readonly redirectService: RedirectService
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<SearchPageData> {
    const keyword = route.queryParams['global_filter'] ?? '';

    const currentPage = route.queryParams['page'] ? parseInt(route.queryParams['page'], 10) - 1 : 0;

    const articlesObservable$ = this.searchPageService.searchArticle(keyword, currentPage);

    return articlesObservable$.pipe(
      map((articlesResponse) => {
        if (this.redirectService.shouldBeRedirect(currentPage, articlesResponse?.data)) {
          this.redirectService.redirectOldUrl(`kereses`, false, 302);
        }

        return {
          articles: articlesResponse?.data?.map((sr: ArticleSearchResult) => searchResultToArticleCard(sr)),
          limitable: articlesResponse?.meta?.limitable,
        };
      }),
      catchError((err) => {
        this.router
          .navigate(['/', '404'], {
            skipLocationChange: true,
          })
          .then(null);
        return throwError(err);
      })
    );
  }
}
