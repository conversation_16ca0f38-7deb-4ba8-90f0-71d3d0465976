import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnDestroy, OnInit } from '@angular/core';
import { ArticleCard, createCanonicalUrlForPageablePage, LimitableMeta } from '@trendency/kesma-ui';
import { ActivatedRoute, Router } from '@angular/router';
import { FormatDatePipe, IMetaData, SeoService } from '@trendency/kesma-core';
import { map, Observable, Subject, Subscription } from 'rxjs';
import { ArticleCardType, createNSOTitle, defaultMetaInfo, NsoArticleCardComponent, NsoPagerComponent, NsoSearchComponent } from '../../shared';
import { AsyncPipe, NgForOf, NgIf } from '@angular/common';

@Component({
  selector: 'app-search-page',
  templateUrl: './search-page.component.html',
  styleUrls: ['./search-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [FormatDatePipe],
  imports: [NsoSearchComponent, NgIf, AsyncPipe, NgForOf, NsoArticleCardComponent, NsoPagerComponent],
})
export class SearchPageComponent implements OnInit, OnDestroy {
  readonly ArticleCardType = ArticleCardType;
  private readonly unsubscribe$: Subject<boolean> = new Subject();
  routeSubscription$ = new Subscription();

  limitables$: Observable<LimitableMeta> = this.route.data.pipe(map((res) => res['data']?.limitable));

  articles$: Observable<ArticleCard[]> = this.route.data.pipe(
    map((res) => {
      const results = res?.['data']?.articles;
      return results.length ? results : null;
    })
  );

  isLoading = false;
  page = 0;
  keyword = '';
  noResultsText: string;

  constructor(
    private readonly cd: ChangeDetectorRef,
    private readonly seo: SeoService,
    private readonly activatedRoute: ActivatedRoute,
    private readonly router: Router,
    private readonly route: ActivatedRoute
  ) {}

  ngOnInit(): void {
    this.subscribeToResolverDataChange();
    this.setMetaData();
  }

  private subscribeToResolverDataChange(): void {
    this.routeSubscription$ = this.route.queryParamMap.subscribe((params) => {
      this.keyword = params.get('global_filter') ?? '';
      this.setNoResultsText(this.keyword);
      this.cd.detectChanges();
    });
  }
  setNoResultsText(keyword: string): void {
    this.noResultsText = `Nincs találat erre: ${keyword}`;
  }
  ngOnDestroy(): void {
    this.unsubscribe$.next(true);
    this.unsubscribe$.complete();
    this.routeSubscription$.unsubscribe();
  }

  onFilter(keyword: string): void {
    this.keyword = keyword;
    this.page = 0;
    this.setMetaData();
    this.setUrl();
    this.setNoResultsText(this.keyword);
  }

  private setUrl(): void {
    const global_filter = this.keyword ? { global_filter: this.keyword } : null;
    const pageIndex = { page: this.page >= 0 ? this.page + 1 : null };

    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: { ...global_filter, ...pageIndex },
    });
  }

  private setMetaData(): void {
    const title = createNSOTitle(this?.keyword ? `Keresés: ${this.keyword}` : 'Keresés');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
      robots: 'noindex, nofollow',
    };
    this.seo.setMetaData(metaData);
    const canonical = createCanonicalUrlForPageablePage('kereses');
    if (canonical) {
      this.seo.updateCanonicalUrl(canonical);
    }
  }
}
