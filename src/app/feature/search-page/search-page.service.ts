import { Injectable } from '@angular/core';
import { ApiResponseMetaList, ArticleSearchResult, BackendArticleSearchResult } from '@trendency/kesma-ui';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { ApiService, backendArticlesSearchResultsToArticleSearchResArticles } from '../../shared';

@Injectable({
  providedIn: 'root',
})
export class SearchPageService {
  constructor(private readonly apiService: ApiService) {}

  searchArticle(keyword: string, page = 0, itemsPerPage = 8): Observable<{ data: ArticleSearchResult[]; meta: ApiResponseMetaList }> {
    return this.apiService.searchByKeyword(keyword, page, itemsPerPage).pipe(
      map(({ data, meta }: { data: BackendArticleSearchResult[]; meta: ApiResponseMetaList }) => ({
        data: data.map(backendArticlesSearchResultsToArticleSearchResArticles),
        meta,
      }))
    );
  }
}
