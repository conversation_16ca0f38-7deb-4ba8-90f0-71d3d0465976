@use 'shared' as *;

.main-wrap {
  .search-wrap {
    padding: 30px 0;
    margin-bottom: 30px;

    .search-bg {
      background: var(--kui-gray-100);
      position: absolute;
      width: 100%;
      height: 172px;
      z-index: 0;
      top: 130px;
      left: 0;

      @include media-breakpoint-down(md) {
        top: 88px;
        height: 225px;
      }
    }

    @include media-breakpoint-down(md) {
      padding-left: 15px;
      padding-right: 15px;
    }
  }

  .results {
    @include media-breakpoint-down(md) {
      padding-left: 15px;
      padding-right: 15px;
    }
  }
}

:host {
  nso-search {
    position: relative;
    max-width: 900px;
    z-index: 1;
    margin: auto;
  }
}
