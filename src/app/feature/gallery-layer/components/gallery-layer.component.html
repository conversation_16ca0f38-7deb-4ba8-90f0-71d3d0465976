<ng-container *ngIf="!isUserAdultChoice && gallery?.isAdult; else galleryContent">
  <nso-adult (isUserAdult)="onUserAdultChoose($event)"></nso-adult>
</ng-container>

<ng-template #galleryContent>
  <section class="gallery">
    <i class="icon icon-close" (click)="goBack()"></i>
    <div class="wrapper gallery-header">
      <div class="gallery-title">
        <ng-container *ngIf="gallery?.images?.[currentIndex]?.caption as caption; else galleryDescription">
          {{ caption }}
        </ng-container>
        <ng-template #galleryDescription>
          {{ gallery.description }}
        </ng-template>
        <ng-container *ngIf="gallery.photographer as photographer"> (Fotók: {{ photographer }}) </ng-container>
      </div>
      <div class="count">{{ currentIndex + 1 }} / {{ gallery.images.length }}</div>
    </div>

    <div class="wrapper">
      <div class="gallery-content">
        <div class="gallery-main-thumbnail">
          <button class="gallery-arrow prev" (click)="onPreviousClick()">
            <i class="icon arrow-left"></i>
          </button>
          <div class="gallery-content-card">
            <img
              class="gallery-content-thumbnail"
              [src]="(gallery.images[currentIndex] || { url: {} }).url.fullSize"
              [alt]="(gallery.images[currentIndex] || { altText: '' }).altText"
              loading="lazy"
            />
          </div>
          <button class="gallery-arrow next" (click)="onNextClick()">
            <i class="icon arrow-right"></i>
          </button>
        </div>

        <div class="gallery-swiper">
          <button class="gallery-swiper-arrow prev" (click)="swipePrev()">
            <i class="icon arrow-left"></i>
          </button>
          <swiper-container
            #swiper
            slides-per-view="2.4"
            slides-per-group="3"
            speed="1000"
            rewind="true"
            center-insufficient-slides="true"
            [breakpoints]="swiperBreakpoints"
          >
            <swiper-slide *ngFor="let gallery of gallery.images; index as index">
              <div class="gallery-swiper-box">
                <img class="gallery-swiper-image" [alt]="gallery?.altText" [src]="gallery?.url?.thumbnail" (click)="onThumbnailClick(index)" />
              </div>
            </swiper-slide>
          </swiper-container>
          <button class="gallery-swiper-arrow next" (click)="swipeNext()">
            <i class="icon arrow-right"></i>
          </button>
        </div>
      </div>
    </div>
  </section>
</ng-template>
