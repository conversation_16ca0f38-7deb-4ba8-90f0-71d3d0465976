import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { createCanonicalUrlForPageablePage, GalleryData, RedirectService, SwiperBaseComponent } from '@trendency/kesma-ui';
import { ActivatedRoute, Router } from '@angular/router';
import { IMetaData, SeoService, StorageService } from '@trendency/kesma-core';
import { Gallery } from '@trendency/kesma-ui/lib/definitions/gallery.definitions';
import { createNSOTitle, defaultMetaInfo, NsoAdultComponent, UrlService } from '../../../shared';
import { map, takeUntil } from 'rxjs/operators';
import { Location, NgForOf, NgIf } from '@angular/common';
import { Subject } from 'rxjs';

@Component({
  selector: 'app-gallery-layer',
  templateUrl: './gallery-layer.component.html',
  styleUrls: ['./gallery-layer.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NsoAdultComponent, NgForOf],
})
export class GalleryLayerComponent extends SwiperBaseComponent<never> implements OnInit, AfterViewInit {
  #currentIndex = 0;
  get currentIndex(): number {
    return this.#currentIndex;
  }

  set currentIndex(value: number) {
    this.#currentIndex = value;
    this.swiperRef()?.slideTo(value);
  }

  isUserAdultChoice: boolean;
  gallery: GalleryData;
  previousUrl = '';

  swiperBreakpoints = {
    576: {
      slidesPerView: 4,
      slidesPerGroup: 4,
      speed: 1500,
    },
    1024: {
      slidesPerView: 5,
      slidesPerGroup: 5,
    },
  };

  private readonly unsubscribe$: Subject<void> = new Subject<void>();

  constructor(
    private readonly router: Router,
    private readonly route: ActivatedRoute,
    private readonly seo: SeoService,
    private readonly changeDetector: ChangeDetectorRef,
    private readonly urlService: UrlService,
    private readonly location: Location,
    private readonly storage: StorageService,
    private readonly redirectService: RedirectService
  ) {
    super();
  }

  override ngOnInit(): void {
    super.ngOnInit();
    if (this.route.snapshot.params['index']) {
      const indexParam = parseInt(this.route.snapshot.params['index']);
      if (isNaN(indexParam) || indexParam < 0) {
        this.currentIndex = 0;
      } else {
        this.currentIndex = indexParam - 1;
      }
    }
    const slug: string = this.route.snapshot.params['slug'];
    this.route.parent?.data
      ?.pipe(
        map((result) => result?.['pageData']),
        takeUntil(this.unsubscribe$)
      )
      .subscribe((pageData) => {
        this.gallery = pageData?.galleryDetails;
        if (this.gallery.images.length !== 0 && this.gallery.images.length < this.route.snapshot.params['index'] + 1) {
          this.redirectService.redirectOldUrl(`galeria/${slug}`, false, 302);
        }
        this.setMetaData();
        this.isUserAdultChoice = this.isUserAdultChoiceFromStorage;
        this.changeDetector.markForCheck();
      });

    this.urlService.previousUrl$.pipe(takeUntil(this.unsubscribe$)).subscribe((previousUrl) => {
      if (previousUrl) {
        this.previousUrl = previousUrl;
      }
    });
  }

  override ngOnDestroy(): void {
    super.ngOnDestroy();
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }

  get isUserAdultChoiceFromStorage(): boolean {
    return this.storage.getSessionStorageData('isAdultChoice', false) ?? false;
  }

  onUserAdultChoose(isAdult: boolean): void {
    this.isUserAdultChoice = isAdult;
  }

  public goBack(): void {
    if (this.previousUrl) {
      this.location.back();
    } else {
      this.router.navigateByUrl('/').then();
    }
  }

  public onThumbnailClick(index: number): void {
    this.currentIndex = index;
    this.router
      .navigate([`../${++index}`], {
        relativeTo: this.route,
        replaceUrl: true,
      })
      .then();
  }

  public onPreviousClick(): void {
    let index = this.currentIndex - 1;
    if (this.currentIndex - 1 < 0) {
      index = this.gallery.images.length - 1;
    }
    this.currentIndex = index;
    this.router
      .navigate([`../${this.currentIndex + 1}`], {
        relativeTo: this.route,
        replaceUrl: true,
      })
      .then();
  }

  public onNextClick(): void {
    let index = this.currentIndex + 1;
    if (this.currentIndex + 1 >= this.gallery.images.length) {
      index = 0;
    }
    this.currentIndex = index;
    this.router
      .navigate([`../${this.currentIndex + 1}`], {
        relativeTo: this.route,
        replaceUrl: true,
      })
      .then();
  }

  private setMetaData(): void {
    const title = createNSOTitle(this.gallery.title);
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title,
      ogTitle: title,
      ogImage: (this.gallery as unknown as Gallery).highlightedImage.url,
    };
    this.seo.setMetaData(metaData);

    const canonical = createCanonicalUrlForPageablePage('galeria', this.route.snapshot);
    canonical && this.seo.updateCanonicalUrl(canonical);
  }
}
