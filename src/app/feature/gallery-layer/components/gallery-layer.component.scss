@use 'shared' as *;

:host {
  display: block;

  nso-adult {
    margin-top: 40px;
    display: block;
  }

  .gallery {
    padding-top: 40px;
    color: var(--kui-white);
    background-color: var(--kui-black);
    position: fixed;
    overflow: scroll;
    width: 100%;
    height: 100%;
    z-index: 9999;
    top: 0;
    left: 0;

    @include media-breakpoint-down(md) {
      overflow-x: hidden;
    }

    &-header {
      display: flex;
      justify-content: space-between;
    }

    &-content {
      margin: 30px 0;
      text-align: center;
      font-size: 16px;
      position: relative;

      &-card {
        width: 100%;
        height: 100%;
        background-color: rgba(255, 255, 255, 0.1);
        background-position: center;
        background-repeat: no-repeat;
        background-size: contain;
        margin-top: 10px;
      }

      &-thumbnail {
        max-height: 580px;
        object-fit: scale-down;
      }
    }

    &-swiper {
      position: relative;
      display: flex;
      align-items: center;

      swiper-container {
        width: 100%;
      }

      &-box {
        margin-top: 20px;
        padding-right: 20px;
        cursor: pointer;

        @include media-breakpoint-down(md) {
          margin-top: 10px;
          padding-right: 10px;
        }
      }

      &-image {
        object-fit: cover;
        height: 112.5px;
      }

      &-arrow {
        height: 100%;
        width: 100px;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 2;
        top: 0;

        @include media-breakpoint-down(md) {
          position: absolute;
        }

        &.next {
          right: 0;
        }

        .icon {
          width: 25px;
          height: 25px;
        }
      }

      @include media-breakpoint-down(md) {
        margin: 0 -15px;
      }
    }

    &-main-thumbnail {
      display: flex;
      align-items: center;
      gap: 20px;

      @include media-breakpoint-down(md) {
        margin: 0 -15px;
      }
    }

    &-arrow {
      &.next {
        right: 0;
      }

      @include media-breakpoint-down(md) {
        position: absolute;

        &.prev {
          left: 0;
        }
      }
    }

    .count {
      font-size: 24px;
      font-weight: 700;
      white-space: nowrap;
      margin: 0 60px 0 15px;
    }

    .icon-close {
      width: 30px;
      height: 30px;
      cursor: pointer;
      position: absolute;
      right: 30px;
      top: 38px;
    }
  }

  .arrow {
    &-left,
    &-right {
      background-image: url('/assets/images/icons/icon-nso-arrow-down-white.svg');
      width: 35px;
      height: 35px;
    }

    &-right {
      transform: rotate(-90deg);
    }

    &-left {
      transform: rotate(90deg);
    }
  }

  ::ng-deep {
    .swiper-button-next,
    .swiper-button-prev {
      display: none;
    }

    .swiper-slide:last-child {
      .gallery-layer-swiper-box {
        padding-right: 0;
      }
    }
  }

  ::-webkit-scrollbar {
    width: 0;
    background: transparent;
  }
}
