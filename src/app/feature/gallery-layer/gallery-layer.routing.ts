import { Routes } from '@angular/router';
import { GalleryLayerResolver } from './api/gallery-layer.resolver';
import { GalleryLayerComponent } from './components/gallery-layer.component';

export const galleryLayerRoutes: Routes = [
  {
    path: ':slug',
    redirectTo: ':slug/',
    pathMatch: 'full',
  },
  {
    path: ':slug',
    resolve: {
      pageData: GalleryLayerResolver,
    },
    providers: [GalleryLayerResolver],
    children: [
      {
        path: ':index',
        component: GalleryLayerComponent,
      },
    ],
  },
  { path: '**', redirectTo: '/404' },
];
