export interface StaticPageResponse {
  readonly body: readonly IComponentData[];
  readonly id: string;
  readonly publishDate: IDate;
  readonly slug: string;
  readonly title: string;
  readonly type: string;
}

export type IComponentData = Readonly<{
  readonly type: string;
  readonly uuid: string;
  readonly key: string;
  readonly subComponents: readonly IComponentData[];
  readonly details: readonly IComponentFieldData[];
  readonly autoplay?: boolean;
  readonly adId?: number;
  readonly cssClass?: string;
}>;
export enum CustomStaticPageType {
  StaticPage = 'staticPage',
  CustomPage = 'customBuiltPage',
}

export type IDate = Readonly<{
  readonly date: string;
}>;

export type IComponentFieldData = Readonly<{
  readonly type: string;
  readonly inputType: string;
  readonly key: string;
  readonly uuid: string;
  readonly value: SubsequentDossierValue;
  readonly multiple?: boolean;
  readonly properties?: any;
  readonly valueDetails?: any;
}>;

export type SubsequentDossierValue = Readonly<{
  readonly coverImage: string;
  readonly headerColor: string;
  readonly id: string;
  readonly isActive: boolean;
  readonly isDeleted: string;
  readonly slug: string;
  readonly relatedArticles: readonly DossierRelatedArticles[];
  readonly title: string;
}>;

export type DossierRelatedArticles = Readonly<{
  readonly columnId: string;
  readonly columnParentId?: string;
  readonly columnParentSlug?: string;
  readonly columnParentTitle?: string;
  readonly columnSlug: string;
  readonly columnTitle: string;
  readonly publishDate: string;
  readonly slug: string;
  readonly id: string;
  readonly title: string;
}>;
