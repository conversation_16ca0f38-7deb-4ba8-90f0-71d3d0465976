import { Injectable } from '@angular/core';
import { ReqService } from '@trendency/kesma-core';
import { HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { CustomStaticPageType } from './static-page.definitions';
import { ApiResult } from '@trendency/kesma-ui';

@Injectable({
  providedIn: 'root',
})
export class StaticPageService {
  public constructor(private readonly reqService: ReqService) {}

  public getStaticPage(slug: string): Observable<ApiResult<CustomStaticPageType>> {
    return this.reqService.get(`/custom-static-page-by-slug/${slug}`);
  }

  public getStaticPagePreview(slug: string, previewHash: string): Observable<ApiResult<CustomStaticPageType>> {
    const params = new HttpParams().append('previewHash', previewHash);
    return this.reqService.get(`/content-page/static-page/${slug}/preview/view`, { params: params });
  }
}
