import { AfterViewInit, Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { EmbeddingService, SeoService } from '@trendency/kesma-core';
import { AnalyticsService, ArticleBodyType, ArticleFileLinkDirective, createCanonicalUrlForPageablePage, LayoutApiData } from '@trendency/kesma-ui';
import { CustomStaticPageType, IComponentData, StaticPageResponse } from '../../api/static-page.definitions';
import { createNSOTitle, defaultMetaInfo, NsoWysiwygBoxComponent } from '../../../../shared';
import { map, Observable } from 'rxjs';
import { AsyncPipe, NgForOf, NgIf, NgSwitch, NgSwitchCase } from '@angular/common';
import { SidebarComponent } from '../../../layout/components/sidebar/sidebar.component';
import { LayoutComponent } from '../../../layout/components/layout/layout.component';

@Component({
  selector: 'app-static-page',
  templateUrl: './static-page.component.html',
  styleUrls: ['./static-page.component.scss'],
  imports: [NgIf, AsyncPipe, NgForOf, NgSwitch, NgSwitchCase, NsoWysiwygBoxComponent, ArticleFileLinkDirective, SidebarComponent, LayoutComponent],
})
export class StaticPageComponent implements OnInit, AfterViewInit {
  public title: string;

  public body: IComponentData[];
  public customStaticPageType: CustomStaticPageType;
  public staticPageResponse: StaticPageResponse;
  public layoutApiData: LayoutApiData;

  constructor(
    private readonly route: ActivatedRoute,
    private readonly embedding: EmbeddingService,
    private readonly seo: SeoService,
    private readonly analyticsService: AnalyticsService
  ) {}

  showCompletePage$: Observable<boolean> = this.route.data.pipe(
    map((data) => data['isMobileApp']),
    map((onlyBody) => !onlyBody)
  );

  ngOnInit(): void {
    this.route.data.subscribe((res: any) => {
      this.customStaticPageType = res.data.meta?.customStaticPageType;

      if (!this.customStaticPageType || this.customStaticPageType === CustomStaticPageType.StaticPage) {
        this.staticPageResponse = res.data?.data;
        this.title = this.staticPageResponse?.title;
        this.body = this.staticPageResponse?.body as IComponentData[];
        this.analyticsService.sendPageView(undefined, 'Statikus oldal');
      }

      if (this.customStaticPageType === CustomStaticPageType.CustomPage) {
        this.title = res?.data?.meta?.customBuiltPageTitle;
        this.layoutApiData = res.data?.data;
        this.analyticsService.sendPageView(undefined, 'Egyedi oldal');
      }
    });

    const canonical = createCanonicalUrlForPageablePage('', this.route.snapshot);
    canonical && this.seo.updateCanonicalUrl(canonical);
    this.seo.setMetaData({
      ...defaultMetaInfo,
      title: createNSOTitle(this.title),
      ogTitle: this.title,
    });
  }

  ngAfterViewInit(): void {
    this.embedding.loadEmbedMedia();
  }

  readonly ArticleBodyType = ArticleBodyType;
}
