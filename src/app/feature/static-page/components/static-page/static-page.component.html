<section
  class="static-page"
  *ngIf="!customStaticPageType || customStaticPageType === 'staticPage'"
  [class.mobile-app-static-page]="(showCompletePage$ | async) === false"
>
  <div class="wrapper" [class.with-aside]="showCompletePage$ | async">
    <div class="left-column">
      <div class="heading-line">
        <h1 class="title">{{ title }}</h1>
      </div>

      <ng-container *ngFor="let element of body">
        <ng-container [ngSwitch]="element.type">
          <ng-container *ngSwitchCase="ArticleBodyType.Wysywyg">
            <ng-container *ngFor="let wysiwygDetail of element?.details">
              <nso-wysiwyg-box [html]="$any(wysiwygDetail)?.value || ''" trArticleFileLink></nso-wysiwyg-box>
            </ng-container>
          </ng-container>
        </ng-container>
      </ng-container>
    </div>

    <aside *ngIf="showCompletePage$ | async">
      <app-sidebar></app-sidebar>
    </aside>
  </div>
</section>

<section *ngIf="customStaticPageType === 'customBuiltPage'" [class.mobile-app-static-page]="(showCompletePage$ | async) === false">
  <div [class.mobile-app-static-page]="(showCompletePage$ | async) === false" class="wrapper">
    <div class="static-page-container-no-side">
      <app-layout [structure]="layoutApiData.struct" [configuration]="layoutApiData.content"> </app-layout>
    </div>
  </div>
</section>
