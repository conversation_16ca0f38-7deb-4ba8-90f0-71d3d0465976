import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import { ArticleCard, createCanonicalUrlForPageablePage, LimitableMeta, VideoCard } from '@trendency/kesma-ui';

import { Observable, Subject } from 'rxjs';
import { map, takeUntil } from 'rxjs/operators';
import { RadioService } from 'src/app/shared/services/radio.service';
import { createNSOTitle, defaultMetaInfo, NsoPagerComponent, NsoVideoCardComponent, VideoCardType } from '../../shared';
import { AsyncPipe, NgForOf, NgIf } from '@angular/common';
import { SidebarComponent } from '../layout/components/sidebar/sidebar.component';

@Component({
  selector: 'app-nso-tv-page',
  templateUrl: './nso-tv-page.component.html',
  styleUrls: ['./nso-tv-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, AsyncPipe, NgForOf, NsoVideoCardComponent, NsoPagerComponent, SidebarComponent],
})
export class NsoTvPageComponent implements OnInit, OnDestroy {
  readonly VideoCardType = VideoCardType;
  private readonly unsubscribe$: Subject<boolean> = new Subject();

  videos$: Observable<VideoCard[]> = this.route.data.pipe(map((res) => res?.['data']?.videos));

  limitables$: Observable<LimitableMeta> = this.route.data.pipe(map((res) => res['data'].limitable));

  sidebarExcludedIds: Array<string> = [];

  constructor(
    public readonly radioService: RadioService,
    private readonly cdr: ChangeDetectorRef,
    private readonly seo: SeoService,
    private readonly route: ActivatedRoute
  ) {}

  ngOnInit(): void {
    this.subscribeToResolverDataChange();
  }

  subscribeToResolverDataChange(): void {
    this.route.data.pipe(takeUntil(this.unsubscribe$)).subscribe((res) => {
      this.setPageMeta();
      this.populateSidebarExcludedIds(res?.['data']['articles']);
      this.cdr.detectChanges();
    });
  }

  setPageMeta(): void {
    const canonical = createCanonicalUrlForPageablePage('nso-tv', this.route.snapshot);
    canonical && this.seo.updateCanonicalUrl(canonical);
    const title = createNSOTitle(`NSO-TV oldal`);
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    };
    this.seo.setMetaData(metaData);
  }

  capitalize(txt: string): string {
    return txt.charAt(0).toUpperCase() + txt.slice(1);
  }

  populateSidebarExcludedIds(articles: ArticleCard[]): void {
    this.sidebarExcludedIds = articles?.map((item) => item.id!) ?? [];
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next(true);
    this.unsubscribe$.complete();
  }
}
