import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { ArticleSearchResult, RedirectService } from '@trendency/kesma-ui';
import { Observable, throwError } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import { searchResultToVideoCard } from '../../shared';
import { VideoListCategory, VideoListPageData } from './nso-tv-page.definitions';
import { NsoTvPageService } from './nso-tv-page.service';

@Injectable()
export class NsoTvPageResolver {
  constructor(
    private readonly nsoTvPagePageService: NsoTvPageService,
    private readonly router: Router,
    private readonly redirectService: RedirectService
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<VideoListPageData> {
    const maxResultsPerPage = 8;
    const currentPage = route.queryParams['page'] ? parseInt(route.queryParams['page'], 10) - 1 : 0;

    const articlesObservable$ = this.nsoTvPagePageService.searchArticleByCategory(VideoListCategory.VIDEO_LIST, currentPage, maxResultsPerPage);

    return articlesObservable$.pipe(
      map((articlesResponse) => {
        return {
          videos: articlesResponse?.data?.map((sr: ArticleSearchResult) => searchResultToVideoCard(sr)),
          limitable: articlesResponse?.meta?.limitable,
        };
      }),
      tap(({ videos }) => {
        if (this.redirectService.shouldBeRedirect(currentPage, videos)) {
          this.redirectService.redirectOldUrl(`nso-tv`, false, 302);
        }
      }),
      catchError((err) => {
        this.router
          .navigate(['/', '404'], {
            skipLocationChange: true,
          })
          .then(null);
        return throwError(() => err);
      })
    );
  }
}
