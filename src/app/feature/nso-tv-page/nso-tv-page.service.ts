import { Injectable } from '@angular/core';
import { ReqService } from '@trendency/kesma-core';
import { ApiResponseMetaList, ArticleSearchResult } from '@trendency/kesma-ui';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { backendArticlesSearchResultsToArticleSearchResArticles } from '../../shared';

@Injectable({
  providedIn: 'root',
})
export class NsoTvPageService {
  constructor(private readonly reqService: ReqService) {}

  searchArticleByCategory(column: string, page = 0, itemsPerPage = 8): Observable<{ data: ArticleSearchResult[]; meta: ApiResponseMetaList }> {
    return this.reqService
      .get(`/content-page/search`, {
        params: {
          column,
          'content_types[]': ['video'],
          rowCount_limit: itemsPerPage.toString(),
          page_limit: page.toString(),
        },
      })
      .pipe(
        map(({ data, meta }: any) => ({
          data: data.map(backendArticlesSearchResultsToArticleSearchResArticles),
          meta,
        }))
      );
  }
}
