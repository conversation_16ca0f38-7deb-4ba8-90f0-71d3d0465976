import { Routes } from '@angular/router';
import { NsoTvPageComponent } from './nso-tv-page.component';
import { NsoTvPageResolver } from './nso-tv-page.resolver';
import { PageValidatorGuard } from '@trendency/kesma-ui';

export const nsoTvPageRoutes: Routes = [
  {
    path: '',
    component: NsoTvPageComponent,
    runGuardsAndResolvers: 'paramsOrQueryParamsChange',
    resolve: { data: NsoTvPageResolver },
    canActivate: [PageValidatorGuard],
    providers: [NsoTvPageResolver],
  },
];
