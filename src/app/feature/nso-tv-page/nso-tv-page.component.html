<section class="nso-tv-page">
  <div class="wrapper sport-radio">
    <div class="left-column">
      <!--
      <nso-sport-radio
        [playing]="(radioService.isPlaying$ | async) || false"
        (playingChange)="radioService.radioClick()"
        (volumeChange)="radioService.volumeChange($event)">
      </nso-sport-radio>
      -->
    </div>
  </div>
  <div class="wrapper with-aside">
    <div class="left-column" *ngIf="videos$ | async as videos">
      <div class="article-list">
        <ng-container *ngFor="let video of videos; let i = index">
          <nso-video-card class="video-card" [data]="video" [styleID]="VideoCardType.FeaturedSidedImgColumnTitleLead"></nso-video-card>
        </ng-container>
      </div>

      <ng-container *ngIf="limitables$ | async as limitable">
        <nso-pager
          *ngIf="limitable?.pageMax! > 0"
          [rowAllCount]="limitable?.rowAllCount!"
          [rowOnPageCount]="limitable?.rowOnPageCount!"
          [hasFirstLastButton]="true"
          [showTotalPagesPositionAtRight]="true"
          [isCountPager]="true"
        >
        </nso-pager>
      </ng-container>
    </div>
    <aside>
      <app-sidebar [excludedIds]="sidebarExcludedIds"> </app-sidebar>
    </aside>
  </div>
</section>
