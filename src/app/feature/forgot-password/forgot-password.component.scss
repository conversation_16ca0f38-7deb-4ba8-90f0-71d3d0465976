@use 'shared' as *;

.forgot-password {
  padding: 100px 0;

  @include media-breakpoint-down(sm) {
    padding: 50px 15px;
  }

  .wrapper {
    width: 100%;
    max-width: 630px;
    border-radius: 10px;
    background-color: var(--kui-white);
    padding: 70px 80px 60px;

    @include media-breakpoint-down(sm) {
      padding: 45px 20px 30px;
    }
  }

  h1 {
    font-weight: bold;
    font-size: 38px;
    line-height: 42px;
    font-family: var(--kui-font-condensed);
    text-transform: uppercase;
    margin-bottom: 60px;
    text-align: center;
  }

  &-gray-text {
    font-size: 16px;
    line-height: 26px;
    color: var(--kui-gray-350);
    margin-bottom: 35px;
    text-align: center;
  }

  &-action {
    margin-top: 45px;
  }

  &-back {
    font-size: 16px;
    margin-top: 30px;
    text-align: center;

    &-link {
      color: var(--kui-gray-550);
      text-decoration: underline;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  &-second-email-attempt {
    margin-top: 10px;
  }

  &-error {
    text-align: center;
    margin-bottom: 0;
  }

  &-submitted {
    text-align: center;

    img {
      margin-bottom: 35px;
    }

    h1 {
      margin-bottom: 30px;
    }

    .forgot-password-gray-text {
      margin-bottom: 45px;
    }
  }

  nso-password-strength-meter {
    margin-top: 60px;
  }
}
