import { ChangeDetectionStrategy, ChangeDetectorRef, Component, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { ReactiveFormsModule, UntypedFormBuilder, UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { takeUntil } from 'rxjs/operators';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import {
  compareValidator,
  createCanonicalUrlForPageablePage,
  KesmaFormControlComponent,
  markControlsTouched,
  passwordContainsCharsAndNumbers,
  passwordContainsSpecial,
  passwordValidator,
} from '@trendency/kesma-ui';
import { Subject } from 'rxjs';
import { errorLabels, errorsToWatch } from 'src/app/shared/constants/formErrorLabels.consts';
import { ApiService, createNSOTitle, defaultMetaInfo, NsoPasswordStrengthMeterComponent, NsoPopupComponent, NsoSimpleButtonComponent } from '../../../shared';
import { NgIf } from '@angular/common';

@Component({
  selector: 'app-reset-password',
  templateUrl: './reset-password.component.html',
  styleUrls: ['./reset-password.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, ReactiveFormsModule, KesmaFormControlComponent, NsoPasswordStrengthMeterComponent, NsoSimpleButtonComponent, NsoPopupComponent],
})
export class ResetPasswordComponent implements OnInit, OnDestroy {
  unsubscribe$: Subject<void> = new Subject<void>();

  formGroup: UntypedFormGroup;
  showPassword = false;
  showConfirmPassword = false;
  showSuccessPopup = false;
  isLoading = false;
  error: string | null = null;
  errorsToWatch = errorsToWatch;
  errorLabels = errorLabels;
  email: string | null = null;
  token: string | null = null;

  constructor(
    private readonly apiService: ApiService,
    private readonly route: ActivatedRoute,
    private readonly router: Router,
    private readonly formBuilder: UntypedFormBuilder,
    private readonly cdr: ChangeDetectorRef,
    private readonly seo: SeoService
  ) {}

  ngOnInit(): void {
    this.route.queryParams.pipe(takeUntil(this.unsubscribe$)).subscribe((params: Params) => {
      this.email = params['email'];
      this.token = params['token'];
      if (this.email && this.token) {
        this.initForm();
      }
      this.setMetaData();
    });
  }

  get passwordControl(): UntypedFormControl {
    return this.formGroup.get('password') as UntypedFormControl;
  }

  initForm(): void {
    this.formGroup = this.formBuilder.group({
      password: [
        null,
        [Validators.required, Validators.minLength(6), passwordContainsSpecial, passwordContainsCharsAndNumbers, compareValidator('confirmPassword')],
      ],
      confirmPassword: [null, [Validators.required, passwordValidator, compareValidator('password')]],
    });

    this.updatePasswordValidty();
  }

  updatePasswordValidty(): void {
    this.formGroup.get('confirmPassword')?.valueChanges.subscribe(() => {
      this.formGroup.get('password')?.updateValueAndValidity();
    });
  }

  resetPassword(): void {
    if (this.formGroup) {
      markControlsTouched(this.formGroup);
    }

    if (!this.formGroup.valid) {
      return;
    }

    this.error = null;
    this.isLoading = true;

    this.apiService.resetPassword(this.email ?? '', this.formGroup.value.password, this.token ?? '').subscribe({
      next: () => {
        this.showSuccessPopup = true;
        this.cdr.detectChanges();
      },
      error: () => {
        this.error = 'Hiba, az új jelszó beállításra kiküldött link érvénytelen vagy lejárt, kérem próbálja újra!';
        this.isLoading = false;
        this.cdr.detectChanges();
      },
    });
  }

  handleSuccessPopupResult(): void {
    this.showSuccessPopup = false;
    this.router.navigate(['/bejelentkezes']);
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage('elfelejtett-jelszo/uj-jelszo');
    if (canonical) this.seo.updateCanonicalUrl(canonical);
    const title = createNSOTitle('Új jelszó beállítása');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
      robots: 'noindex, nofollow',
    };
    this.seo.setMetaData(metaData);
  }
}
