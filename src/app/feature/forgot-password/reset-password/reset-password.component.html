<section class="forgot-password">
  <div class="wrapper">
    <!-- Forgot password form -->
    <h1><PERSON><PERSON><PERSON><PERSON></h1>
    <div *ngIf="!formGroup" class="nso-form-general-error forgot-password-error">
      <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> link, k<PERSON><PERSON><PERSON> el<PERSON>rizd a böngészőben megadott hivatkozást!
    </div>
    <form (ngSubmit)="resetPassword()" *ngIf="formGroup" [formGroup]="formGroup" class="forgot-form">
      <div class="nso-form-row">
        <kesma-form-control>
          <div class="nso-form-input-password">
            <input [type]="showPassword ? 'text' : 'password'" class="nso-form-input" formControlName="password" id="password" placeholder="Új jelszó" />
            <img
              (click)="showPassword = !showPassword"
              [src]="showPassword ? '/assets/images/icons/icon-no-eye.svg' : '/assets/images/icons/icon-eye.svg'"
              alt="Jels<PERSON><PERSON> megtekintése"
              class="nso-form-input-password-img"
            />
          </div>
        </kesma-form-control>
      </div>
      <div class="nso-form-row">
        <kesma-form-control>
          <div class="nso-form-input-password">
            <input
              [type]="showConfirmPassword ? 'text' : 'password'"
              class="nso-form-input"
              formControlName="confirmPassword"
              id="confirmPassword"
              placeholder="Jelszó megerősítése"
            />
            <img
              (click)="showConfirmPassword = !showConfirmPassword"
              [src]="showConfirmPassword ? '/assets/images/icons/icon-no-eye.svg' : '/assets/images/icons/icon-eye.svg'"
              alt="Jelszó megtekintése"
              class="nso-form-input-password-img"
            />
          </div>
        </kesma-form-control>
      </div>
      <nso-password-strength-meter
        layout="password-reset"
        [passwordInput]="passwordControl"
        [errorsToWatch]="errorsToWatch"
        [errorLabels]="errorLabels"
      ></nso-password-strength-meter>
      <div *ngIf="error" class="nso-form-general-error">
        {{ error }}
      </div>
      <div class="forgot-password-action">
        <nso-simple-button [disabled]="isLoading" [isSubmit]="true" class="w-100" round="round">
          {{ isLoading ? 'Kérem várjon...' : 'JELSZÓ VISSZAÁLLÍTÁSA' }}
        </nso-simple-button>
      </div>
    </form>
  </div>
</section>

<!-- Submitted forgot-password form -->
<nso-popup
  (resultEvent)="handleSuccessPopupResult()"
  *ngIf="showSuccessPopup"
  [acceptButtonLabel]="'TOVÁBB A BEJELENTKEZÉSHEZ'"
  [showCancelButton]="false"
  [title]="'Sikeres jelszóváltoztatás'"
>
  Sikeresen módosította jelszavát. További kellemes olvasást kívánunk!
</nso-popup>
