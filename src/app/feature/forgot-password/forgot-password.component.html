<section class="forgot-password">
  <div class="wrapper">
    <!-- Forgot password form -->
    <ng-container *ngIf="!isSubmitted">
      <h1><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></h1>
      <p class="forgot-password-gray-text">Adja meg email címét és elküldjük az új j<PERSON> beállításához szükséges információkat.</p>
      <form *ngIf="formGroup" [formGroup]="formGroup" class="forgot-form" (ngSubmit)="requestPasswordReset(false)">
        <div class="nso-form-row">
          <kesma-form-control>
            <input class="nso-form-input" type="text" id="email" formControlName="email" placeholder="Email cím" />
          </kesma-form-control>
        </div>
        <div class="nso-form-general-error" *ngIf="error && !isSecondMailSendAttempt">
          {{ error }}
        </div>
        <div class="forgot-password-action">
          <nso-simple-button class="w-100" round="round" [disabled]="isLoading" [isSubmit]="true">
            {{ isLoading ? 'Kérem várjon...' : 'TOVÁBB' }}
          </nso-simple-button>
        </div>
        <div class="forgot-password-back">
          <a class="forgot-password-back-link" routerLink="/bejelentkezes">Vissza a Bejelentkezéshez</a>
        </div>
      </form>
    </ng-container>

    <!-- Submitted forgot password form -->
    <div class="forgot-password-submitted" *ngIf="isSubmitted">
      <img src="/assets/images/icons/icon-paper-plane.svg" alt="Az e-mailt elküldtük!" />
      <h1>Az e-mailt elküldtük!</h1>
      <div class="forgot-password-gray-text">Az új jelszó beállításához szükséges információkat elküldtük a megadott email címre.</div>
      <nso-simple-button class="w-100" round="round" (click)="requestPasswordReset(true)">EMAIL ÚJRAKÜLDÉSE</nso-simple-button>
      <div class="nso-form-general-error forgot-password-second-email-attempt" *ngIf="error && isSecondMailSendAttempt">
        {{ error }}
      </div>
    </div>
  </div>
</section>
