import { Competition, Team } from '../../team-page/team-end-page.definitions';

export interface BackendMatchDatesResponse {
  data: {
    match_days: Array<string>;
  };
}

export interface BackendTeam {
  id: string;
  title: string;
  logo: string;
  team: Team;
}

export interface BackendSchedule {
  awayTeam: BackendTeam;
  homeTeam: BackendTeam;
  scheduleDate: BackendScheduleDate;
  competition: Competition;
}

export interface BackendScheduleDate {
  date: string;
  timezone_type: number;
  timezone: string;
}
export interface BackendScheduleResponse {
  schedules: Array<BackendSchedule>;
}
