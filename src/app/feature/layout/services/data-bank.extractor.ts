import { Injectable } from '@angular/core';

import { Competition } from '../../team-page/team-end-page.definitions';
import {
  buildCompetitionUrl,
  DataExtractorIterableFunction,
  LayoutDataExtractorResult,
  LayoutDataIterableExtractorService,
  LayoutElementContent,
  LayoutElementContentConfigurationDataBank,
  BaseIterableExtractor,
} from '@trendency/kesma-ui';

type RESULT_TYPE = Competition | undefined;

@Injectable()
export class DataBankExtractor extends BaseIterableExtractor implements LayoutDataIterableExtractorService<RESULT_TYPE> {
  override extractData(element: LayoutElementContent): LayoutDataExtractorResult<RESULT_TYPE> {
    return super.extractData(element) as LayoutDataExtractorResult<RESULT_TYPE>;
  }
  override extractIteratorData: DataExtractorIterableFunction<RESULT_TYPE> = (element: LayoutElementContent, index: number | string) => {
    const conf = element.config as LayoutElementContentConfigurationDataBank;
    const competition = conf?.selectedCompetitions?.[Number.isNaN(index) ? 0 : (index as number)];

    if (!conf || !competition) {
      return;
    }

    return {
      data: {
        id: competition.id,
        column: competition.column || competition.original?.column,
        logo: competition.logo || competition.original?.logo?.fullSizeUrl,
        publicTitle: competition.publicTitle || competition.original?.publicTitle,
        season: competition.season || competition.original?.season,
        slug: competition.slug || competition.original?.slug,
        title: competition.title || competition.original?.title,
        url: buildCompetitionUrl(competition || competition.original),
      },
    };
  };

  override getIterator(layoutElement: LayoutElementContent): Array<string | number> {
    const conf = layoutElement.config as LayoutElementContentConfigurationDataBank;
    if (!conf) {
      return [];
    }
    const comps = conf?.selectedCompetitions;
    return comps.map((_, index) => index);
  }
}
