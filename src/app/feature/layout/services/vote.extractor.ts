import { Injectable } from '@angular/core';

import {
  backendVotingDataToVotingData,
  DataExtractorFunction,
  LayoutDataExtractorService,
  LayoutElementContent,
  LayoutElementContentConfigurationVote,
  VoteDataWithAnswer,
  VoteService,
} from '@trendency/kesma-ui';

@Injectable()
export class VoteExtractor implements LayoutDataExtractorService<VoteDataWithAnswer | undefined> {
  constructor(private readonly voteService: VoteService) {}
  extractData: DataExtractorFunction<VoteDataWithAnswer | undefined> = (element: LayoutElementContent) => {
    const conf = element.config as LayoutElementContentConfigurationVote;

    if (!conf || !conf?.selectedVote?.data) return;
    const voteData = backendVotingDataToVotingData(conf?.selectedVote?.data);

    const data = this.voteService.getVoteData(voteData);

    return {
      data,
      meta: {
        extractedBy: VoteExtractor.name,
      },
    };
  };
}
