import { Injectable } from '@angular/core';
import { BackendMatchDatesResponse, BackendSchedule, BackendScheduleResponse } from '../definitions/matches.definitions';
import { isAfter, isPast, isToday, parseISO } from 'date-fns';
import { BehaviorSubject, concatMap, from, Observable, toArray } from 'rxjs';
import { map, mergeMap } from 'rxjs/operators';
import { ApiResult, backendDateToDate } from '@trendency/kesma-ui';
import { ApiService, MatchData } from '../../../shared';

@Injectable({
  providedIn: 'root',
})
export class UpcomingMatchesService {
  upcomingMatchesDataCache: { [key: string]: BehaviorSubject<MatchData[] | undefined> } = {};

  constructor(private readonly api: ApiService) {}
  /**
   * Get upcoming match dates based on competition slug provided as an input,
   * then get the matches for each date
   */
  getUpcomingMatches(competitionSlug: string, datesToShow: number): Observable<Array<MatchData>> {
    return this.getMatchDates(competitionSlug).pipe(
      map((dates) => this.filterDates(dates.data.match_days, datesToShow)),
      mergeMap((dates) => from(dates)),
      concatMap((date) => this.getSchedulesForDate(competitionSlug, date).pipe(map((result: ApiResult<BackendScheduleResponse>) => ({ date, result })))),
      toArray(),
      map((result) => {
        const schedules = result.map((data) => {
          return data.result.data.schedules.map((schedule: BackendSchedule) => {
            return {
              date: <Date>backendDateToDate(schedule.scheduleDate.date),
              matches: [
                {
                  teamA: {
                    name: schedule.homeTeam.title,
                    icon: schedule.homeTeam.team.logo,
                    slug: schedule.homeTeam.team.slug,
                    competition: schedule.competition,
                  },
                  teamB: {
                    name: schedule.awayTeam.title,
                    icon: schedule.awayTeam.team.logo,
                    slug: schedule.awayTeam.team.slug,
                    competition: schedule.competition,
                  },
                },
              ],
            };
          });
        });
        // Return future matches only
        return schedules.reduce((acc, curr) => acc.concat(curr), []).filter((matchData) => !isPast(matchData.date));
      })
    );
  }

  /**
   * Returns all match days for input competition
   */
  private getMatchDates(competitionSlug: string): Observable<BackendMatchDatesResponse> {
    return this.api.getCompetitionMatchDates(competitionSlug);
  }

  /**
   * Returns schedule of the competition for the given day
   */
  private getSchedulesForDate(competitionSlug: string, date: string): Observable<ApiResult<BackendScheduleResponse>> {
    return this.api.getSchedulesByDay(competitionSlug, date);
  }

  /**
   * Returns a new array with match dates that are either today or in the future.
   */
  private filterDates(dates: string[], datesToShow: number): string[] {
    const today = new Date();
    return dates
      .filter((date) => {
        const dateISO = parseISO(date);
        return isToday(dateISO) || isAfter(dateISO, today);
      })
      .slice(0, datesToShow);
  }
}
