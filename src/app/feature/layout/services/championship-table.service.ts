import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { ApiResult } from '@trendency/kesma-ui';
import { ApiService, TeamsLeageTable } from '../../../shared';

@Injectable({
  providedIn: 'root',
})
export class ChampionshipTableService {
  championshipTableDataCache: { [key: string]: BehaviorSubject<any> } = {};

  constructor(private readonly api: ApiService) {}

  getChampionshipTableData(competitionId: string): Observable<ApiResult<TeamsLeageTable>> {
    return this.api.getChampionshipTable(competitionId);
  }
}
