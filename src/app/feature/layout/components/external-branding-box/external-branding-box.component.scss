@use 'shared' as *;

$img-height: 326px;

:host {
  background: var(--kui-gray-100);
  position: relative;
  display: block;

  .main-item {
    position: relative;
    display: block;

    &-image {
      width: 100%;
      height: $img-height;
      object-fit: cover;
    }

    .details {
      position: absolute;
      z-index: 1;
      bottom: 0;
      width: 100%;
      padding: 24px;
      background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, var(--kui-black) 150%);

      .generator {
        display: inline-block;
        background: var(--kui-red-400);
        color: var(--kui-white);
        padding: 4px 16px;
        margin-bottom: 10px;
        font-weight: 700;
        line-height: 120%;
        letter-spacing: 0.16px;
      }

      .title {
        font-family: var(--kui-font-condensed);
        font-size: 22px;
        font-weight: 700;
        line-height: 130%;
        text-transform: uppercase;
        color: var(--kui-white);
      }
    }

    &:after {
      content: '';
      background: linear-gradient(180deg, rgba(44, 44, 44, 0) 0%, rgba(44, 44, 44, 0.5) 100%);
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }
  }

  .side-items-wrapper {
    padding: 24px 24px 70px;
    display: flex;
    flex-direction: column;
    gap: 16px;
    background-color: var(--kui-gray-100);
    border: 1px solid var(--kui-gray-200);
    height: 335px;

    @include media-breakpoint-down(md) {
      height: auto;
    }

    .side-item {
      font-style: normal;
      font-weight: 700;
      line-height: 140%;
      letter-spacing: 0.16px;
      display: flex;

      &:before {
        content: '\2022';
        color: red;
        font-size: 22px;
        vertical-align: middle;
        padding-right: 8px;
      }

      &-link {
        color: var(--kui-gray-550);
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
    }
  }

  .navigate {
    font-weight: 700;
    line-height: 120%;
    letter-spacing: 0.16px;
    color: var(--kui-red-400);
    border-bottom: 2px solid var(--kui-red-400);
    padding-bottom: 3px;
    position: absolute;
    bottom: 24px;
    right: 24px;
  }
}
