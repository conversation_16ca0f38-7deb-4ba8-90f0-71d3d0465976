<ng-container *ngIf="data$ | async as data">
  <a [href]="data?.[0]?.url" target="_blank" class="main-item">
    <img [src]="data?.[0]?.imageUrl || '/assets/images/nemzetisport.png'" [alt]="data?.[0]?.title" class="main-item-image" loading="lazy" />

    <div class="details">
      <div class="generator">{{ boxTitle }}</div>
      <div class="title">{{ data?.[0]?.title }}</div>
    </div>
  </a>

  <ul class="side-items-wrapper">
    <li *ngFor="let item of data | slice: 1" class="side-item">
      <a [href]="item?.url" target="_blank" class="side-item-link">{{ item?.title }}</a>
    </li>
  </ul>

  <a [href]="sourceUrl" target="_blank" class="navigate">Tovább {{ buttonLabel }}</a>
</ng-container>
