import { ChangeDetectionStrategy, Component, inject, Input } from '@angular/core';
import { Observable, of } from 'rxjs';
import { BrandingBoxArticle } from '@trendency/kesma-ui';
import { ExternalBrandingBoxArticle } from '@trendency/kesma-ui/lib/definitions/external-branding-box.definitions';
import { Async<PERSON>ipe, NgForOf, NgIf, SlicePipe } from '@angular/common';
import { TrafficDeflectorService } from '../../../../shared';

@Component({
  selector: 'app-external-branding-box',
  templateUrl: './external-branding-box.component.html',
  styleUrls: ['./external-branding-box.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, AsyncPipe, NgForOf, SlicePipe],
})
export class ExternalBrandingBoxComponent {
  @Input() set type(type: string) {
    this.data$ = this.getDataByType(type);
  }

  boxTitle: string;
  buttonLabel: string;
  sourceUrl: string;
  data$: Observable<ExternalBrandingBoxArticle[] | undefined>;

  trafficDeflectorService = inject(TrafficDeflectorService);

  getDataByType(type: string): Observable<ExternalBrandingBoxArticle[] | undefined> {
    let data;

    switch (type) {
      case 'auto-motor':
        data = this.trafficDeflectorService.getTrafficDeflectorData('Auto-Motor for NSO', 4);
        this.boxTitle = 'autó-motor';
        this.buttonLabel = `az ${this.boxTitle}-ra`;
        this.sourceUrl = 'https://www.automotor.hu';
        break;
      case 'csupa-sport':
        data = this.trafficDeflectorService.getTrafficDeflectorData('Csupasport for NSO', 4);
        this.boxTitle = 'Csupasport';
        this.buttonLabel = `a ${this.boxTitle}-ra`;
        this.sourceUrl = 'https://www.csupasport.hu';
        break;
      case 'four-four-two':
        // Nincs hozzá RSS feed, és már nem is fejlesztik, így égetjük (egyeztetve).
        data = of([
          {
            url: 'https://fft-archivum.nemzetisport.hu/',
            imageUrl: './assets/images/fourfourtwo.jpg',
            title: 'Nemzeti Sport FFT Archivum',
          },
          {
            url: 'https://fft-archivum.nemzetisport.hu/',
            imageUrl: './assets/images/fourfourtwo.jpg',
            title: 'Tizenkét esztendő legjobb anyagai a futball szellemi csúcstermékéből',
          },
        ]);
        this.boxTitle = 'FourFourTwo';
        this.buttonLabel = `a ${this.boxTitle}-ra`;
        this.sourceUrl = 'https://fft-archivum.nemzetisport.hu/';
        break;
    }
    return data as Observable<BrandingBoxArticle[] | undefined>;
  }
}
