import { Injectable } from '@angular/core';

import { DataExtractorFunction, LayoutDataExtractorService, LayoutElementContent } from '@trendency/kesma-ui';
import { Observable, share } from 'rxjs';
import { UpcomingMatchesService } from '../services/upcoming-matches.service';
import { MatchData } from '../../../shared';

@Injectable()
export class UpcomingMatchesExtractor implements LayoutDataExtractorService<Observable<MatchData[] | undefined> | undefined> {
  constructor(private readonly upcomingMatches: UpcomingMatchesService) {}
  extractData: DataExtractorFunction<Observable<MatchData[] | undefined> | undefined> = (element: LayoutElementContent) => {
    const conf = element.config as any;

    if (!conf || !conf?.selectedCompetition) {
      return;
    }
    const competitionSlug = conf.selectedCompetition?.slug;
    const datesToShow = element.contentLength;

    return { data: this.upcomingMatches.getUpcomingMatches(competitionSlug, datesToShow).pipe(share()) };
  };
}
