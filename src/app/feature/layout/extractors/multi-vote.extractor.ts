import { inject, Injectable } from '@angular/core';
import { toZonedTime } from 'date-fns-tz';

import {
  backendMultiVoteDataToVoteData,
  DataExtractorFunction,
  LayoutDataExtractorService,
  LayoutElementContent,
  LayoutElementContentConfigurationMultiVote,
  MultiVoteDataWithVotedId,
  MultiVoteService,
} from '@trendency/kesma-ui';
import { MultiVotingCacheService } from '../../../shared/services/multi-voting-cache.service';

@Injectable()
export class MultiVoteExtractor implements LayoutDataExtractorService<MultiVoteDataWithVotedId | undefined> {
  private readonly service = inject(MultiVoteService);
  private readonly multiVotingCacheService = inject(MultiVotingCacheService);

  extractData: DataExtractorFunction<MultiVoteDataWithVotedId | undefined> = (element: LayoutElementContent) => {
    const conf = element.config as LayoutElementContentConfigurationMultiVote;

    const selectedMultivote = conf?.selectedMultiVote;

    if (!selectedMultivote) {
      return;
    }

    let isExpired = false;

    if (selectedMultivote.endDate) {
      isExpired = this.isExpired(selectedMultivote.endDate);
    }

    if (!isExpired) {
      this.multiVotingCacheService.setMultiVote(selectedMultivote.id, selectedMultivote.endDate || '');
    }

    const data = this.service.getVoteData(backendMultiVoteDataToVoteData(selectedMultivote));

    return {
      data,
      meta: {
        extractedBy: MultiVoteExtractor.name,
      },
    };
  };

  isExpired(voteEndDate: string): boolean {
    const endDate = toZonedTime(new Date(voteEndDate), 'Europe/Budapest');
    return endDate.getTime() < new Date().getTime();
  }
}
