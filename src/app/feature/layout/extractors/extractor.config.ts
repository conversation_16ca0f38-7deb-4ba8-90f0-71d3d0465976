import { ArticleArrayExtractor, DataExtractor, LayoutElementContentType, VoteExtractor } from '@trendency/kesma-ui';
import { DataBankExtractor } from '../services/data-bank.extractor';
import { MultiVoteExtractor } from './multi-vote.extractor';

export const NSO_EXTRACTORS_CONFIG: DataExtractor<unknown>[] = [
  {
    extractor: ArticleArrayExtractor,
    supportedContentTypes: [LayoutElementContentType.ARTICLES_WITH_VIDEO_CONTENT],
  },
  {
    extractor: DataBankExtractor,
    supportedContentTypes: [LayoutElementContentType.DATA_BANK],
  },
  {
    extractor: VoteExtractor,
    supportedContentTypes: [LayoutElementContentType.Vote],
    priority: 1000,
  },
  {
    extractor: MultiVoteExtractor,
    supportedContentTypes: [LayoutElementContentType.MULTI_VOTE],
    priority: 1000,
  },
];
