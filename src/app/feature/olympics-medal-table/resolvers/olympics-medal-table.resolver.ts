import { Observable, forkJoin } from 'rxjs';
import { inject } from '@angular/core';
import { MedalTable } from '@trendency/kesma-ui/lib/components/olimpia/olimpia-medal-table/olimpia-medal-table.definitions';
import { OlympicsMedalTableService } from '../services/olympics-medal-table.service';

export function OlympicsMedalTableResolver(): Observable<{ medalTable: MedalTable }> {
  const YEAR = 2024;
  return forkJoin({
    medalTable: inject(OlympicsMedalTableService).getMedalTable(YEAR),
  });
}
