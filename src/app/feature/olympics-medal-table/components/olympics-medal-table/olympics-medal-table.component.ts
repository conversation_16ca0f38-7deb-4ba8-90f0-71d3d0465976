import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import {
  ALL_BANNER_LIST,
  Advertisement,
  AdvertisementAdoceanStoreService,
  AdvertisementsByMedium,
  LayoutPageType,
  OlimpicPortalEnum,
  PAGE_TYPES,
  SecondaryFilterAdvertType,
  OlimpiaMedalTableComponent,
  AdvertisementAdoceanComponent,
} from '@trendency/kesma-ui';
import { MedalTable } from '@trendency/kesma-ui/lib/components/olimpia/olimpia-medal-table/olimpia-medal-table.definitions';
import { Observable, Subject, map, takeUntil } from 'rxjs';
import { AsyncPipe, NgIf } from '@angular/common';

@Component({
  selector: 'app-olympics-medal-table',
  templateUrl: './olympics-medal-table.component.html',
  styleUrl: './olympics-medal-table.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, AsyncPipe, OlimpiaMedalTableComponent, AdvertisementAdoceanComponent],
})
export class OlympicsMedalTableComponent implements OnInit, OnDestroy {
  adPageType = PAGE_TYPES.column_sport_all_articles_and_sub_pages;
  adverts?: AdvertisementsByMedium;

  readonly OlimpicPortalEnum = OlimpicPortalEnum;
  readonly LayoutPageType = LayoutPageType;
  readonly unsubscribe$: Subject<boolean> = new Subject();

  constructor(
    private readonly route: ActivatedRoute,
    private readonly adStoreAdo: AdvertisementAdoceanStoreService,
    protected readonly cdr: ChangeDetectorRef
  ) {}

  medalTable$: Observable<MedalTable> = this.route.data.pipe(map(({ data }) => data['medalTable']['data']));

  ngOnInit(): void {
    this.initAds();
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next(true);
    this.unsubscribe$.complete();
    this.adStoreAdo.setArticleParentCategory('');
  }

  initAds(): void {
    this.resetAds();
    this.adStoreAdo.advertisemenets$.pipe(takeUntil(this.unsubscribe$)).subscribe((ads: Advertisement[]) => {
      this.adverts = this.adStoreAdo.separateAdsByMedium(
        ads,
        this.adPageType,
        ALL_BANNER_LIST,
        SecondaryFilterAdvertType.REPLACEABLE,
        PAGE_TYPES.column_sport_all_articles_and_sub_pages
      );

      this.cdr.detectChanges();
    });
  }

  private resetAds(): void {
    this.adverts = undefined;
    this.cdr.detectChanges();
  }
}
