@use 'shared' as *;

.olympic-important-sidebar {
  ::ng-deep {
    .layout-element {
      margin-bottom: 0 !important;
    }
  }
}

.left-column {
  row-gap: 24px;

  .table-title {
    font-family: var(--kui-font-saira-condensed);
    color: #009ce0;
    font-size: 48px;
    font-style: normal;
    font-weight: 700;
    line-height: 52.8px; /* 110% */
    letter-spacing: -2px;
    text-transform: uppercase;
    margin: 24px 0;
  }

  kesma-olimpia-page-banner {
    width: 100%;
  }
}

kesma-olimpia-medal-table {
  margin-bottom: 24px;
}

kesma-olimpia-navigator {
  margin-top: 16px;
  margin-bottom: 40px;
}

kesma-advertisement-adocean {
  margin-top: 24px;
  margin-bottom: 40px;
}
