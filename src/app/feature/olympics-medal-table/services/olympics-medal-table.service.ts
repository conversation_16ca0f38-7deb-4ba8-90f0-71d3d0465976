import { Injectable } from '@angular/core';
import { ReqService } from '@trendency/kesma-core';
import { MedalTable } from '@trendency/kesma-ui/lib/components/olimpia/olimpia-medal-table/olimpia-medal-table.definitions';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class OlympicsMedalTableService {
  constructor(private readonly reqService: ReqService) {}

  getMedalTable(year: number): Observable<MedalTable> {
    return this.reqService.get(`/olympics/country-medallist/${year}?gold_order[0]=desc&silver_order[1]=desc&bronze_order[2]=desc`);
  }
}
