import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import { defaultMetaInfo } from 'src/app/shared/constants/meta.consts';
import { RadioService } from 'src/app/shared/services/radio.service';
import { createNSOTitle } from '../../../../shared';
import { createCanonicalUrlForPageablePage } from '@trendency/kesma-ui';

@Component({
  selector: 'app-sport-radio',
  templateUrl: './sport-radio.component.html',
  styleUrls: ['./sport-radio.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SportRadioComponent implements OnInit {
  constructor(
    public readonly radioService: RadioService,
    private readonly seo: SeoService
  ) {}

  ngOnInit(): void {
    this.setMetaData();
  }

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage('sport-radio');
    if (canonical) this.seo.updateCanonicalUrl(canonical);
    const title = createNSOTitle('Sportrádió');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    };
    this.seo.setMetaData(metaData);
  }
}
