import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { SeoService } from '@trendency/kesma-core';
import { forkJoin, Observable, throwError } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import { TagsPageData } from './tags-page.definitions';
import { TagsPageService } from './tags-page.service';

import { ArticleSearchResult, RedirectService } from '@trendency/kesma-ui';
import { searchResultToArticleCard } from '../../shared';

@Injectable()
export class TagsPageResolver {
  constructor(
    private readonly tagsPage: TagsPageService,
    private readonly router: Router,
    private readonly redirectService: RedirectService,
    private readonly seoService: SeoService
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<TagsPageData> {
    const tagSlug = route.queryParams['cimke'] ?? route.params['tag'];
    const maxResultsPerPage = 20;
    const currentPage = route.queryParams['page'] ? parseInt(route.queryParams['page'], 10) - 1 : 0;

    const tagObservable$ = this.tagsPage.getTag(tagSlug);
    const articlesObservable$ = this.tagsPage.searchArticleByTags([tagSlug], currentPage, maxResultsPerPage);

    return forkJoin([tagObservable$, articlesObservable$]).pipe(
      tap(([, articlesResponse]) => {
        const meta = articlesResponse.meta;
        const urlSegments = this.seoService.currentUrl.split('/');
        urlSegments.pop();

        // if tag is from old url
        if (route.queryParams['cimke']) {
          this.redirectService.redirectOldUrl(`cimke/${decodeURI(route.queryParams['cimke'])}`);
        }

        // if tag is "merged tag"
        if (meta?.['redirect']?.tag?.slug) {
          this.redirectService.redirectOldUrl(`cimke/${decodeURI(meta?.['redirect'].tag.slug)}`);

          // if the given part of URL contains "tag" instead of "cimke"
        } else if (urlSegments[urlSegments.length - 1] === 'tag' || urlSegments[urlSegments.length - 2] === 'tag') {
          this.redirectService.redirectOldUrl(`cimke/${decodeURI(route.url[0].toString())}`);
        }
      }),
      map(([tagResponse, articlesResponse]) => {
        if (this.redirectService.shouldBeRedirect(currentPage, articlesResponse?.data)) {
          this.redirectService.redirectOldUrl(`cimke/${decodeURI(tagSlug)}`, false, 302);
        }

        return {
          tag: tagResponse?.data,
          articles: articlesResponse?.data?.map((sr: ArticleSearchResult) => searchResultToArticleCard(sr)),
          limitable: articlesResponse?.meta?.limitable,
        };
      }),
      catchError((err) => {
        this.router
          .navigate(['/', '404'], {
            skipLocationChange: true,
          })
          .then(null);
        return throwError(() => err);
      })
    );
  }
}
