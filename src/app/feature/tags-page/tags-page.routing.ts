import { Routes } from '@angular/router';
import { TagsPageComponent } from './tags-page.component';
import { TagsPageResolver } from './tags-page.resolver';
import { PageValidatorGuard } from '@trendency/kesma-ui';

export const tagsPageRoutes: Routes = [
  {
    path: ':tag/:tagname',
    component: TagsPageComponent,
    runGuardsAndResolvers: 'paramsOrQueryParamsChange',
    resolve: { data: TagsPageResolver },
    canActivate: [PageValidatorGuard],
    providers: [TagsPageResolver],
  },
  {
    path: ':tag',
    component: TagsPageComponent,
    runGuardsAndResolvers: 'paramsOrQueryParamsChange',
    resolve: { data: TagsPageResolver },
    canActivate: [PageValidatorGuard],
    providers: [TagsPageResolver],
  },
  {
    path: '',
    component: TagsPageComponent,
    runGuardsAndResolvers: 'paramsOrQueryParamsChange',
    resolve: { data: TagsPageResolver },
    canActivate: [PageValidatorGuard],
    providers: [TagsPageResolver],
  },
];
