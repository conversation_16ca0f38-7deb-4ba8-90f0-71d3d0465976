import { Injectable } from '@angular/core';
import { buildPhpArrayParam, ReqService } from '@trendency/kesma-core';
import { ApiResponseMetaList, ApiResult, ArticleSearchResult, Tag } from '@trendency/kesma-ui';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { backendArticlesSearchResultsToArticleSearchResArticles } from '../../shared';

@Injectable({
  providedIn: 'root',
})
export class TagsPageService {
  constructor(private readonly reqService: ReqService) {}

  searchArticleByTags(tags: string[], page = 0, itemsPerPage = 16, orderByAsc = true): Observable<ApiResult<ArticleSearchResult[], ApiResponseMetaList>> {
    return this.reqService
      .get(`/content-page/search`, {
        params: {
          ...buildPhpArrayParam(tags, 'tags'),
          rowCount_limit: itemsPerPage.toString(),
          page_limit: page.toString(),
          'date_order[0]': orderByAsc ? 'asc' : 'desc',
        },
      })
      .pipe(
        map(({ data, meta }: any /*ApiResult<BackendArticleSearchResult[], ApiResponseMetaList>*/) => ({
          data: data.map(backendArticlesSearchResultsToArticleSearchResArticles),
          meta,
        }))
      );
  }

  public getTag(slug: string): Observable<ApiResult<Tag>> {
    return this.reqService.get(`content-group/tags/${slug}`, {});
  }
}
