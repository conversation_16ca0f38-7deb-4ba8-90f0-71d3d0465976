<section class="tags-page" [class.mobile-app-news-page]="!showCompletePage">
  <div class="wrapper" [class.with-aside]="showCompletePage">
    <div class="left-column" *ngIf="articles$ | async as articles">
      <h1 class="tag-title" *ngIf="tag$ | async as tag">
        TALÁLATOK A(Z) <span class="highlight">{{ tag?.title }}</span> CÍMKÉRE
      </h1>

      <div *ngIf="limitables$ | async as limitables" class="result-count">{{ limitables.rowAllCount }} találat</div>

      <div class="article-list" id="cimke_tartalom">
        <ng-container *ngFor="let article of articles; let i = index">
          <nso-article-card class="article-card" [data]="article" [styleID]="ArticleCardType.FeaturedColumnTitleLeadTags"></nso-article-card>
        </ng-container>
      </div>
      <ng-container *ngIf="limitables$ | async as limitable">
        <nso-pager
          *ngIf="limitable?.pageMax! > 0"
          [rowAllCount]="limitable?.rowAllCount!"
          [rowOnPageCount]="limitable?.rowOnPageCount!"
          [hasFirstLastButton]="true"
          [showTotalPagesPositionAtRight]="true"
          [isCountPager]="true"
        >
        </nso-pager>
      </ng-container>
    </div>
    @if (showCompletePage) {
      <aside>
        <app-sidebar [hasAds]="true" [excludedIds]="sidebarExcludedIds"></app-sidebar>
      </aside>
    }
  </div>
</section>
