import { ChangeDetectionStrategy, Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import { ArticleCard, createCanonicalUrlForPageablePage, LimitableMeta, Tag } from '@trendency/kesma-ui';
import { Observable } from 'rxjs';
import { map, share, tap } from 'rxjs/operators';
import { ArticleCardType, createNSOTitle, defaultMetaInfo, NsoArticleCardComponent, NsoPagerComponent } from '../../shared';
import { AsyncPipe, NgForOf, NgIf } from '@angular/common';
import { SidebarComponent } from '../layout/components/sidebar/sidebar.component';

@Component({
  selector: 'app-tags-page',
  templateUrl: './tags-page.component.html',
  styleUrls: ['./tags-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, AsyncPipe, NgForOf, NsoArticleCardComponent, NsoPagerComponent, SidebarComponent],
})
export class TagsPageComponent {
  readonly ArticleCardType = ArticleCardType;

  tag$: Observable<Tag> = this.route.data.pipe(
    map((res) => res?.['data']['tag']),
    tap((tag) => this.setPageMeta(tag))
  );
  articles$: Observable<ArticleCard[]> = this.route.data.pipe(
    map((res) => res?.['data']?.articles),
    tap((articles) => this.populateSidebarExcludedIds(articles))
  );
  limitables$: Observable<LimitableMeta> = this.route.data.pipe(map((res) => res['data'].limitable));

  sidebarExcludedIds: Array<string> = [];

  showCompletePage$: Observable<boolean> = this.route.data.pipe(
    map((data) => data['isMobileApp']),
    map((onlyBody) => !onlyBody),
    share()
  );
  get showCompletePage(): boolean {
    return !this.route.snapshot.data?.['isMobileApp'];
  }

  constructor(
    private readonly seo: SeoService,
    private readonly route: ActivatedRoute
  ) {}

  setPageMeta(tag: Tag): void {
    const canonical = createCanonicalUrlForPageablePage('cimke', this.route.snapshot);
    canonical && this.seo.updateCanonicalUrl(canonical);
    const title = createNSOTitle(`${this.capitalize(tag?.title ?? '')} címke oldal`);
    const metaData: IMetaData = { ...defaultMetaInfo, title: title, ogTitle: title };
    this.seo.setMetaData(metaData);
  }

  capitalize(txt: string): string {
    return txt.charAt(0).toUpperCase() + txt.slice(1);
  }

  populateSidebarExcludedIds(articles: ArticleCard[]): void {
    this.sidebarExcludedIds = articles?.map((item) => item.id!) ?? [];
  }
}
