@use 'shared' as *;

.tags-page {
  .tag-title {
    font-size: 38px;
    font-family: var(--kui-font-primary);
    color: var(--kui-gray-500);
    font-style: normal;
    font-weight: 700;
    line-height: 110%;
    letter-spacing: -0.76px;
    text-transform: uppercase;

    &-top {
      font-weight: 700;
      font-size: 16px;
      line-height: 19px;
    }
  }
}

section {
  &.mobile-app-news-page {
    margin: 15px;

    .wrapper {
      width: 100%;
      max-width: none;
    }
  }
}

.highlight {
  color: var(--kui-red-400);
}

.article-card {
  border-bottom: 1px solid var(--kui-gray-325);
  padding-bottom: 12px;

  &:last-child {
    border-bottom: none;
  }
}

.result-count {
  font-size: 16px;
  font-style: normal;
  font-weight: 700;
  line-height: 120%;
  letter-spacing: 0.16px;
  color: var(--kui-gray-550);

  margin: 16px 0 40px 0;
  padding-bottom: 8px;
  border-bottom: var(--kui-gray-325) 1px solid;
}
