// Lok<PERSON><PERSON> fejlesztői környezet
import { NsoEnvironment } from './environment.definitions';

export const environment: NsoEnvironment = {
  production: false,
  type: 'local',
  apiUrl: 'https://kozponti-api.dev.trendency.hu/publicapi/hu', // for prod proxy: '/publicapi/hu' then: npm
  secureApiUrl: 'https://kozponti-api.dev.trendency.hu/secureapi/hu',
  personalizedRecommendationApiUrl: 'https://terelo.mediaworks.hu/api',
  vivicittaApi: 'https://api-vivicitta2025.mito.dev/api',
  facebookAppId: '1312351833007970',
  googleClientId: '328054098226-odkspr250v7bdfg3crmr66ovqgc55l8e.apps.googleusercontent.com',
  siteUrl: 'http://localhost:4200',
  googleSiteKey: '6LdOdtgaAAAAADOpTzcEuDkf-oSP16hxYrVwhHR1', // use this key on all site in dev mode
  googleTagManager: 'GTM-MR7SNN',
  gemiusId: '.RpF.SM6IUbOnR3JbCyle2aD7FnBGrhWHCQzGFi3k_H.I7',
  httpReqTimeout: 30, // second
  sentry: {
    dsn: '',
    tracingOrigins: [],
    sampleRate: 0.1,
  },
};
