import { NsoEnvironment } from './environment.definitions';

// <PERSON><PERSON>
export const environment: NsoEnvironment = {
  production: true,
  type: 'prod',
  apiUrl: {
    clientApiUrl: 'https://api.nemzetisport.hu/publicapi/hu',
    serverApiUrl: 'http://nsofeapi.app.content.private/publicapi/hu',
  },
  secureApiUrl: {
    clientApiUrl: 'https://api.nemzetisport.hu/secureapi/hu',
    serverApiUrl: 'http://nsofeapi.app.content.private/secureapi/hu',
  },
  personalizedRecommendationApiUrl: 'https://terelo.mediaworks.hu/api',
  vivicittaApi: {
    serverApiUrl: 'http://localhost:30070/vivicitta-api',
    clientApiUrl: 'https://api-vivicitta2025.nemzetisport.hu/api',
  },
  ssrProxyConfig: [
    {
      path: '/vivicitta-api',
      target: 'https://api-vivicitta2025.nemzetisport.hu/api',
    },
  ],
  facebookAppId: '1312351833007970',
  googleClientId: '328054098226-odkspr250v7bdfg3crmr66ovqgc55l8e.apps.googleusercontent.com',
  siteUrl: 'https://www.nemzetisport.hu',
  googleSiteKey: '6Ld4BIknAAAAANn0GWjgjWilLldQmE5IzOe0aY_j',
  googleTagManager: 'GTM-MR7SNN',
  gemiusId: '.RpF.SM6IUbOnR3JbCyle2aD7FnBGrhWHCQzGFi3k_H.I7',
  httpReqTimeout: 30, // second
  sentry: {
    dsn: '',
    tracingOrigins: [],
    sampleRate: 0.1,
  },
};
