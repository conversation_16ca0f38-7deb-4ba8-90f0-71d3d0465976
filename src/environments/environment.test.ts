// UAT teszt környezet
import { NsoEnvironment } from './environment.definitions';

export const environment: NsoEnvironment = {
  production: true,
  type: 'beta',
  apiUrl: 'http://nsofe.apptest.content.private/publicapi/hu',
  secureApiUrl: 'http://nsofe.apptest.content.private/secureapi/hu',
  personalizedRecommendationApiUrl: 'https://terelo.mediaworks.hu/api',
  vivicittaApi: {
    serverApiUrl: 'http://localhost:30070/vivicitta-api',
    clientApiUrl: 'https://api-vivicitta2025.mito.dev/api',
  },
  ssrProxyConfig: [
    {
      path: '/vivicitta-api',
      target: 'https://api-vivicitta2025.mito.dev/api',
    },
  ],
  facebookAppId: '1312351833007970',
  googleClientId: '328054098226-odkspr250v7bdfg3crmr66ovqgc55l8e.apps.googleusercontent.com',
  siteUrl: 'http://nsofe.apptest.content.private',
  googleSiteKey: '6LdOdtgaAAAAADOpTzcEuDkf-oSP16hxYrVwhHR1', // use this key on all site in dev mode
  googleTagManager: 'GTM-MR7SNN',
  gemiusId: '.RpF.SM6IUbOnR3JbCyle2aD7FnBGrhWHCQzGFi3k_H.I7',
  httpReqTimeout: 30, // second
  sentry: {
    dsn: '',
    tracingOrigins: [],
    sampleRate: 0.1,
  },
};
