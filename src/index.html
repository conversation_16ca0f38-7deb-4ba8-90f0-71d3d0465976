<!doctype html>
<html lang="hu">
  <head>
    <base href="/" />
    <title>Nemzeti Sport Online</title>
    <meta charset="utf-8" />
    <meta content="index, follow, max-image-preview:large" name="robots" />
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <link href="/assets/favicon/favicon.ico" rel="icon" type="image/x-icon" />
    <link href="/assets/favicon/favicon-32x32.png" rel="icon" sizes="32x32" type="image/png" />
    <link href="/assets/favicon/favicon-16x16.png" rel="icon" sizes="16x16" type="image/png" />
    <link href="/assets/favicon/favicon.svg" rel="icon" type="image/svg" />
    <link href="/assets/favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180" />
    <link href="/manifest.json" rel="manifest" />
    <link color="#bd0004" href="/assets/favicon/safari-pinned-tab.svg" rel="mask-icon" />
    <meta content="#bd0004" name="msapplication-TileColor" />
    <meta content="#bd0004" name="theme-color" />

    <link href="https://fonts.gstatic.com" rel="preconnect" />
    <link href="https://fonts.googleapis.com" rel="preconnect" />
    <link href="https://www.nemzetisport.hu/publicapi/hu/rss/nso/articles" rel="alternate" type="application/rss+xml" />

    <script class="structured-data" type="application/ld+json"></script>

    <!-- (C)2000-2024 Gemius SA - gemiusPrism  / nemzetisport.hu/Fooldal -->
    <script data-skip-conditionally type="text/javascript">
      var pp_gemius_identifier = '.RpF.SM6IUbOnR3JbCyle2aD7FnBGrhWHCQzGFi3k_H.I7';
      var pp_gemius_use_cmp = true;
      var pp_gemius_cmp_timeout = Infinity;
      var pp_gemius_extraparameters = new Array('gprism_title=' + document.title, 'page=' + window.location.pathname);

      function go_gemius_hit() {
        // lines below shouldn't be edited
        function gemius_pending(i) {
          window =
            window ||
            function () {
              var x = (window[i + '_pdata'] = window[i + '_pdata'] || []);
              x[x.length] = arguments;
            };
        }

        gemius_pending('gemius_hit');
        gemius_pending('gemius_event');
        gemius_pending('pp_gemius_hit');
        gemius_pending('pp_gemius_event');
        (function (d, t) {
          try {
            var gt = d.createElement(t),
              s = d.getElementsByTagName(t)[0],
              l = 'http' + (location.protocol == 'https:' ? 's' : '');
            gt.setAttribute('async', 'async');
            gt.setAttribute('defer', 'defer');
            gt.src = l + '://gahu.hit.gemius.pl/xgemius.js';
            s.parentNode.insertBefore(gt, s);
          } catch (e) {}
        })(document, 'script');
      }

      function check_tcfapi_for_gemius() {
        if (typeof __tcfapi !== 'undefined') {
          go_gemius_hit();
        } else {
          setTimeout(check_tcfapi_for_gemius, 500);
        }
      }

      check_tcfapi_for_gemius();
    </script>

    <!-- Natív hirdetés -->
    <script async src="//ex1tp.com/js/ep/v2/ep.js"></script>
    <script type="text/javascript">
      window._extpp = window._extpp || [];
      _extpp.push({ prop: 'nemzetisport.hu' });
    </script>
    <!-- Natív hirdetés -->

    <!-- Google Tag Manager -->
    <script data-skip-conditionally>
      (function (w, d, s, l, i) {
        w[l] = w[l] || [];
        w[l].push({
          'gtm.start': new Date().getTime(),
          event: 'gtm.js',
        });
        var f = d.getElementsByTagName(s)[0],
          j = d.createElement(s),
          dl = l != 'dataLayer' ? '&l=' + l : '';
        j.async = true;
        j.src = 'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
        f.parentNode.insertBefore(j, f);
      })(window, document, 'script', 'dataLayer', 'GTM-MR7SNN');
    </script>
    <!-- End Google Tag Manager -->

    <!-- Hotjar Tracking Code for http://www.nemzetisport.hu/ -->
    <script data-skip-conditionally>
      (function (h, o, t, j, a, r) {
        h.hj =
          h.hj ||
          function () {
            (h.hj.q = h.hj.q || []).push(arguments);
          };
        h._hjSettings = { hjid: 1838275, hjsv: 6 };
        a = o.getElementsByTagName('head')[0];
        r = o.createElement('script');
        r.async = 1;
        r.src = t + h._hjSettings.hjid + j + h._hjSettings.hjsv;
        a.appendChild(r);
      })(window, document, 'https://static.hotjar.com/c/hotjar-', '.js?sv=');
    </script>

    <script async data-skip-conditionally src="https://adat.nemzetisport.hu/script/index.min.v2.js"></script>
  </head>

  <body>
    <div class="trendency-fullscreen-loader" id="init-loader"></div>

    <app-root></app-root>
    <script src="/assets/scripts/init-loader.js"></script>
    <script async defer src="/assets/scripts/version.js"></script>

    <!-- Google Tag Manager (noscript) -->
    <noscript>
      <iframe src="https://www.googletagmanager.com/ns.html?id=GTM-MR7SNN" height="0" width="0" style="display: none; visibility: hidden"></iframe>
    </noscript>
    <!-- End Google Tag Manager (noscript) -->
  </body>
</html>
