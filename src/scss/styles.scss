@use 'abstracts/var-exports' as *;

//kesma-ui
@use 'node_modules/@trendency/kesma-ui/src/scss/kesma-ui-export.scss' as *;
@use 'node_modules/@trendency/kesma-ui/src/scss/abstracts/_kesma-ui-variables.scss' as *;

//base
@use 'base/reset' as *;
@use 'base/typography' as *;
@use 'base/icons' as *;
@use 'base/fonts' as *;
@use 'base/helper' as *;

//layout
@use 'layout' as *;

//components
@use 'components' as *;

//abstracts
@use 'abstracts/variables' as *;

:root {
  @include kesma-ui-variables;
  @include export-custom-vars;
  --nso-gutter-width: $grid-gutter-width;
}
