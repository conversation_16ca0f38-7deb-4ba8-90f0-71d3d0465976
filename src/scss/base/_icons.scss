@use 'shared' as *;

%icon {
  display: inline-block;
  min-width: 5px;
  min-height: 5px;
  background-position: center;
  background-size: contain;
  background-repeat: no-repeat;
}

$images-path: '/assets/images/';

@mixin icon($name) {
  @extend %icon;
  background-image: url($images-path + $name);
}

// Global icons
*[class*='icon-'] {
  &.icon-logo {
    @include icon('icons/logo.svg');
  }

  &.icon-audience-logo {
    @include icon('icons/audience-logo.svg');
  }

  &.icon-play {
    @include icon('icons/nso-icon-play.svg');
  }

  &.icon-podcast {
    @include icon('icons/nso-icon-podcast.svg');
  }

  &.icon-logo-red {
    @include icon('icons/logo-red.svg');
  }

  &.icon-social-twitter {
    @include icon('icons/social-twitter.svg');
  }

  &.icon-social-link {
    @include icon('icons/social-link.svg');
  }

  &.icon-social-facebook {
    @include icon('icons/social-facebook.svg');
  }

  &.icon-social-twitter-yellow {
    @include icon('icons/social-twitter-yellow.svg');
  }

  &.icon-social-link-yellow {
    @include icon('icons/social-link-yellow.svg');
  }

  &.icon-social-facebook-yellow {
    @include icon('icons/social-facebook-yellow.svg');
  }

  &.icon-social-facebook-mini {
    @include icon('icons/icon-facebook-mini.svg');
  }

  &.icon-social-youtube-mini {
    @include icon('icons/icon-youtube-mini.svg');
  }

  &.icon-social-insta-mini {
    @include icon('icons/icon-insta-mini.svg');
  }

  &.nso-icon-search {
    @include icon('icons/nso-icon-search.svg');
  }

  &.icon-chevron {
    @include icon('icons/icon-chevron.svg');
  }

  &.icon-chevron2 {
    @include icon('icons/icon-chevron2.svg');
  }

  &.icon-change-colorful {
    @include icon('icons/icon-change.svg');
  }

  &.icon-help {
    @include icon('icons/help-circle.svg');
  }

  &.icon-like {
    @include icon('icons/like.svg');
  }

  &.icon-message {
    @include icon('icons/message-icon.svg');
  }

  &.icon-more {
    @include icon('icons/icon-more.svg');
  }

  &.icon-menu {
    @include icon('icons/icon-menu.svg');
  }

  &.icon-close {
    @include icon('icons/icon-close.svg');
  }

  &.icon-external {
    @include icon('icons/external.svg');
  }

  &.icon-comment {
    @include icon('icons/comment.svg');
  }

  &.icon-user {
    @include icon('icons/icon-user.svg');
  }

  &.icon-arrow-right {
    @include icon('icons/arrow-right.svg');
  }

  &.icon-arrow-right-red {
    @include icon('icons/arrow-right-red.svg');
  }

  &.icon-arrow-left {
    @include icon('icons/arrow-left.svg');
  }

  &.icon-arrow-down-red {
    @include icon('icons/arrow-down-red.svg');
  }

  &.icon-arrow-right-red {
    @include icon('icons/icon-red-right.svg');
  }

  &.icon-arrow-left-red {
    @include icon('icons/left-arrow-red.svg');
  }

  &.img-stadion {
    @include icon('icons/stadion.svg');
  }

  &.icon-ball-404 {
    @include icon('icons/ball404.svg');
  }

  &.right-arrow {
    @include icon('icons/right-arrow.svg');
  }

  &.left-arrow {
    @include icon('icons/left-arrow.svg');
  }

  &.goal {
    @include icon('icons/gool.svg');
  }

  &.icon-yellowcard {
    @include icon('icons/yellowcard.svg');
  }

  &.icon-redcard {
    @include icon('icons/redcard.svg');
  }

  &.icon-left-gallery {
    @include icon('icons/icon-left-gallery.svg');
  }

  &.icon-right-gallery {
    @include icon('icons/icon-right-gallery.svg');
  }

  &.icon-close-gallery {
    @include icon('icons/icon-close.svg');
  }

  &.icon-left-white {
    @include icon('icons/gallery-left-white.svg');
  }

  &.icon-right-white {
    @include icon('icons/gallery-right-white.svg');
  }

  &.icon-red {
    @include icon('icons/red.svg');
  }

  &.icon-yellow {
    @include icon('icons/yellow.svg');
  }

  &.icon-change {
    @include icon('icons/csere.svg');
  }

  &.icon-goal {
    @include icon('icons/ball.svg');
  }

  &.icon-live {
    @include icon('icons/icon-live.svg');
  }

  &.icon-action-question {
    @include icon('icons/action-info.svg');
  }

  &.icon-check-circle {
    @include icon('icons/icon-check-circle.svg');
  }

  &.icon-x-circle {
    @include icon('icons/icon-x-circle.svg');
  }

  &.icon-play-simple {
    @include icon('icons/play.svg');
  }

  &.icon-pause {
    @include icon('icons/pause.svg');
  }

  &.icon-sound {
    @include icon('icons/sound.svg');
  }

  &.icon-mute {
    @include icon('icons/mute.svg');
  }

  &.icon-datepicker {
    @include icon('icons/icon-datepicker.svg');
  }

  &.icon-chevron-grey {
    @include icon('icons/chevron-grey.svg');
  }

  &.icon-checkmark-grey {
    @include icon('icons/checkmark-grey.svg');
  }

  &.icon-video-play {
    @include icon('icons/icon-video-play.svg');
  }

  &.icon-gallery {
    @include icon('icons/icon-gallery.svg');
  }
}
