@use 'abstracts/var-exports' as *;

// Typography
body {
  font-family: var(--kui-font-primary);
  color: var(--kui-gray-550);
  margin: 0;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--kui-font-condensed);
  font-weight: 700;
}
// Cikk cime
h1 {
  font-size: 48px;
  line-height: 56px;
  //tag/kereses listazo focime (maga a tag)
  &.taglist-title {
    font-size: 44px;
    line-height: 44px;
    text-transform: uppercase;
  }
}

h2 {
  // Foldali fo hir, amelynek pl fekete/kek a hattere vagy termeklistazo oldalon a termekek cime
  font-size: 40px;
  line-height: 47px;
  // tag/kereso listazoban a cikk cime
  &.tag-title {
    font-size: 32px;
    line-height: 36px;
    text-decoration: underline;
  }
  // szolgaltatasok aloldal ( orvosok, szolgaltatok a keruletben)
  &.service-title {
    font-size: 24px;
    line-height: 44px;
    text-transform: uppercase;
  }
}

h3 {
  font-size: 30px;
  line-height: 30px;
}

h4 {
  font-size: 18px;
  text-decoration: underline;
  line-height: 24px;
}

p {
  font-family: var(--kui-font-primary);
  font-size: 20px;
  line-height: 30px;
}

html,
body {
  height: fit-content;
}
