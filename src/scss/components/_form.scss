@use 'shared' as *;

.nso-form {
  &-row {
    margin-bottom: 10px;
  }

  &-label {
    font-weight: normal;
    font-size: 16px;
    line-height: 26px;
    margin-bottom: 15px;
    color: var(--kui-gray-550);

    strong {
      font-weight: 700;
    }
  }

  &-input {
    border: 1px solid var(--kui-gray-350);
    color: var(--kui-gray-550);
    border-radius: 5px;
    display: block;
    width: 100%;
    height: 60px;
    padding: 22px 24px;
    margin-bottom: 30px;
    font-weight: normal;
    font-size: 16px;
    line-height: 16px;

    &-password {
      position: relative;

      &-img {
        position: absolute;
        right: 23px;
        bottom: 18px;
        cursor: pointer;
      }
    }
  }

  &-small {
    display: block;
    font-weight: 400;
    font-size: 12px;
    line-height: 16px;
    background-color: var(--kui-gray-100);
    border-radius: 5px;
    padding: 20px 24px;
    margin-bottom: 30px;
  }

  &-general-error {
    color: var(--kui-red-400);
    font-size: 16px;
    line-height: 20px;
    margin-bottom: 20px;
  }

  &-checkbox,
  &-radio {
    display: flex;
    align-items: center;
    position: relative;
    cursor: pointer;
    font-family: var(--kui-font-primary);
    font-size: 14px;
    line-height: 18px;
    color: var(--kui-gray-550);
    padding-left: 40px;
    min-height: 20px;
    margin-bottom: 25px;

    &-link {
      color: var(--kui-gray-550);
      text-decoration: underline;

      &:hover {
        text-decoration: underline;
      }
    }

    input {
      display: none;
    }

    &:before {
      content: '';
      display: block;
      width: 20px;
      height: 20px;
      border: 2px solid var(--kui-gray-350);
      border-radius: 5px;
      background-color: var(--kui-white);
      position: absolute;
      top: 0;
      left: 0;
    }

    input:checked {
      & + span:before {
        content: '';
        display: block;
        width: 20px;
        height: 20px;
        border-radius: 5px;
        background-color: var(--kui-red-400);
        border-color: var(--kui-red-400);
        position: absolute;
        top: 0;
        left: 0;
      }

      & + span:after {
        content: '';
        display: block;
        width: 10.5px;
        height: 9px;
        background-image: url(/assets/images/icons/icon-checkmark.svg);
        position: absolute;
        top: 5.5px;
        left: 4.75px;
      }
    }
  }

  &-radio {
    margin-bottom: 10px;

    &::before {
      border-radius: 50%;
    }

    input:checked {
      & + span::before {
        border: 1px solid var(--kui-red-400);
        border-radius: 50%;
        background-color: transparent;
      }

      & + span::after {
        background-color: var(--kui-red-400);
        background-image: none;
        border-radius: 50%;
        width: 12px;
        height: 12px;
        top: 4px;
        left: 4px;
      }
    }
  }
}

.form-error {
  top: initial !important;
  right: initial !important;
  bottom: -18px;
  left: 24px;
  padding-top: 2px;
}

.checkbox .form-error {
  bottom: -20px;
  top: initial !important;
  left: 40px !important;
}

::placeholder {
  color: var(--kui-gray-350);
}
