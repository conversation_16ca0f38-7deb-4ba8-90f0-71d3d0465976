@use 'shared' as *;

.custom-checkbox {
  input[type='checkbox'],
  input[type='radio'] {
    position: absolute;
    opacity: 0;
    z-index: -1;
  }

  label {
    position: relative;
    display: inline-block;
    padding: 0 0 0 50px;
    cursor: pointer;
    color: var(--kui-white);
    font-weight: bold;
    font-size: 16px;
    line-height: 160%;
    letter-spacing: 0.01em;
  }

  label::before,
  label::after {
    position: absolute;
    top: calc(50% - 9px);
    left: 0;
    display: block;
    width: 24px;
    height: 24px;
  }

  label::before {
    content: ' ';
    background-color: var(--kui-white);
    box-shadow:
      0 100px 138px rgba(37, 177, 129, 0.07),
      0 30.1471px 41.603px rgba(37, 177, 129, 0.0382914),
      0 12.5216px 17.2797px rgba(37, 177, 129, 0.0243855),
      0 4.5288px 6.24974px rgba(37, 177, 129, 0.0126201);
    border-radius: 4px;
  }

  /* Checkbox */
  input[type='checkbox'] + label::after {
    //@include icon('icons/check.png');
    background-size: 90%;
    content: ' ';
  }

  /* Radio */
  input[type='radio'] + label::before {
    border-radius: 50%;
  }

  input[type='radio'] + label::after {
    content: ' ';
    top: calc(50% - 9px);
    left: 0;
    width: 18px;
    height: 18px;
    background: var(--kui-white);
    border: 1px solid var(--kui-white);
    border-radius: 50%;
  }

  /* :checked */
  input[type='checkbox']:checked + label::before,
  input[type='radio']:checked + label::before {
    border-color: var(--kui-white);
  }

  input[type='checkbox'] + label::after,
  input[type='radio'] + label::after {
    -webkit-transform: scale(0);
    -ms-transform: scale(0);
    -o-transform: scale(0);
    transform: scale(0);
  }

  input[type='checkbox']:checked + label::after,
  input[type='radio']:checked + label::after {
    -webkit-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1);
  }

  /* Transition */
  label::before,
  label::after {
    -webkit-transition: 0.25s all ease;
    -o-transition: 0.25s all ease;
    transition: 0.25s all ease;
  }
}

.nso-input.nso-input-group {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-items: center;
  justify-content: space-between;
  gap: 10px;
  width: 100%;
  padding: 0;
  border: 1px solid var(--kui-gray-350);
  border-radius: 5px;
  margin: 10px 0;
  border-collapse: collapse;
  perspective: 1px;
  overflow: hidden;
  color: var(--kui-gray-350);

  & + .errors {
    color: var(--kui-red-400);
    font-size: 0.8rem;
  }
  & > :first-child {
    padding-left: 24px;
  }
  & > :last-child {
    padding-right: 24px;
  }
  input {
    padding: 18px 0;
    font-size: 16px;
    flex-grow: 1;
  }

  ::placeholder {
    /* Chrome, Firefox, Opera, Safari 10.1+ */
    color: var(--kui-gray-350);
    opacity: 1; /* Firefox */
  }

  ::-ms-input-placeholder {
    /* Microsoft Edge */
    color: var(--kui-gray-350);
  }
}
.nso-input-group.ng-dirty.ng-invalid {
  border-color: var(--kui-red-400);
  color: var(--kui-red-400);
  .nso-input {
    color: var(--kui-red-400);
  }
  svg {
    color: var(--kui-red-400);
  }
  ::placeholder {
    /* Chrome, Firefox, Opera, Safari 10.1+ */
    color: var(--kui-red-400);
    opacity: 1; /* Firefox */
  }

  ::-ms-input-placeholder {
    /* Microsoft Edge */
    color: var(--kui-red-400);
  }
}

.nso-input .ng-dirty.ng-invalid {
  border-color: var(--kui-red-400);
  color: var(--kui-red-400);
  input {
    color: var(--kui-red-400);
  }
}
.nso-input {
  display: block;
  width: 100%;
  border: 1px solid var(--kui-gray-350);
  padding: 18px 24px;
  font-size: 16px;
  margin: 10px 0;
  border-radius: 5px;
  transition:
    0.2s border-color,
    0.2s color;
}
.nso-password-input {
  &:after {
    content: '';
  }
}
