@use 'shared' as *;

.tags-container {
  width: calc(100% - 8px);
  margin-left: -4px;
  margin-right: -4px;

  .tag {
    font-weight: bold;
    font-size: 16px;
    line-height: 120%;
    letter-spacing: 0.01em;
    margin: 4px;
    padding: 6px 16px;
    background: var(--kui-gray-550);
    color: var(--kui-white);
    border-radius: 18px;
    border: 2px solid transparent;

    &:hover {
      background-color: rgba(44, 44, 44, 0.8); // var(--kui-gray-550)
    }

    &:focus {
      border-color: var(--kui-gray-200);
    }
  }

  .share-button {
    margin: 4px;
    background: var(--kui-red-400);
    border: 2px solid transparent;
    border-radius: 50%;
    padding: 0;
    line-height: 0;

    .icon {
      width: 44px;
      height: 44px;
    }

    &:hover {
      opacity: 0.8;
    }

    &:focus {
      border-color: var(--kui-gray-200);
    }
  }
}
