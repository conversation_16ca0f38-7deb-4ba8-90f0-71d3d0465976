@use 'shared' as *;

.button {
  background-color: var(--kui-red-400);
  color: var(--kui-white);
  cursor: pointer;
  text-decoration: none;
  border: 1px solid transparent;

  &:hover {
    background-color: rgba(189, 0, 4, 0.8); // var(--kui-red-400)
  }

  &:focus {
    border-color: var(--kui-gray-200);
  }

  &.simple {
    font-weight: bold;
    font-size: 16px;
    line-height: 120%;
    padding: 8px 32px;
    border-radius: 5px;
  }

  &.with-icon-small {
    padding: 10px 17px;
    font-weight: normal;
    font-size: 16px;
    line-height: 160%;
    border-radius: 5px;

    .icon {
      width: 12px;
      height: 12px;
      margin-left: 10px;
    }
  }

  &.with-icon-big {
    border-radius: 5px;
    font-weight: bold;
    letter-spacing: 0.01em;
    font-size: 16px;
    line-height: 120%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 13px 32px;

    .icon {
      width: 24px;
      height: 24px;
      margin-right: 19px;
    }
  }

  &.full-width {
    width: 100%;
    padding: 16px;
    display: flex;
    justify-content: center;
    align-items: center;
    text-transform: uppercase;
    font-family: var(--kui-font-condensed);
    font-style: normal;
    font-weight: bold;
    font-size: 24px;
    line-height: 120%;

    .icon {
      width: 18px;
      height: 18px;
      margin-right: 19px;
    }
  }
}
