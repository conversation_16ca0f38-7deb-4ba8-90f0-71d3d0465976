@use 'shared' as *;

.inputs-trend {
  .password-show-hide {
    width: 23px;
    height: 23px;
    bottom: 20px;
    background-color: var(--kui-white);
    @include icon('icons/eye.svg');
  }

  /* ---------------- BUTTON ---------------- */

  button,
  a.button {
    background-color: var(--kui-red-200);
    color: var(--kui-white);
  }

  /* ---------------- CHECKBOX AND RADIO ---------------- */

  input[type='checkbox'][type],
  input[type='radio'][type] {
    & + label {
      padding: 3px 0 0 40px;
      font-weight: 400;
      font-size: 12px;
      line-height: 16px;
    }
  }

  input[type='checkbox'][type]:not(.toggler) {
    & + label::before,
    & + label::after {
      border-radius: 5px;
      border: 1px solid var(--kui-gray-350);
    }

    & + label::before {
      background-color: var(--kui-white);
    }

    & + label::after {
      background-color: var(--kui-white);
      @include icon('icons/redcheck.svg');
    }
  }

  input[type='radio'][type] {
    & + label::before {
      border: 1px solid var(--kui-gray-350);
      background-color: var(--kui-white);
    }

    & + label::after {
      transform: scale(0);
      background-color: var(--kui-red-400);
    }

    &:checked + label::after {
      transform: scale(0.7);
    }
  }

  /* ---------------- CHECKBOX TOGGLER ---------------- */

  input[type='checkbox'][type].toggler {
    & + label::before,
    & + label::after {
      width: 40px;
      height: 20px;
      top: 0.1em;
      border-radius: 10px;
      border-width: 0;
    }

    & + label::after {
      top: 5px;
      width: 14px;
      height: 14px;
      background-color: var(--kui-white);
      transform: translateX(-23px);
    }

    & + label::before {
      background-color: var(--kui-gray-350);
    }

    &:checked + label::before {
      background-color: var(--kui-green-400);
    }

    &:checked + label::after {
      transform: translateX(-3px);
    }
  }

  /* ---------------- SELECT ---------------- */

  .select {
    // Custom arrow
    &:not(.select--multiple)::after {
      content: '';
      justify-self: end;
      width: 0.8em;
      height: 0.5em;
      background-color: var(--kui-gray-350);
      clip-path: polygon(100% 0%, 0 0%, 50% 100%);
    }
  }
}
