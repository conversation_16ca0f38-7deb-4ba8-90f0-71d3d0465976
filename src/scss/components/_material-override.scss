@use 'shared' as *;

.mat-form-field {
  .mat-form-field-wrapper {
    .mat-form-field-flex {
      .mat-form-field-infix {
        .mat-form-field-label {
          font-weight: 400;
          color: var(--kui-gray-350);
        }
      }
    }
  }
}

.simple-red {
  &.mat-form-field {
    .mat-form-field-wrapper {
      .mat-form-field-flex {
        .mat-form-field-outline {
          color: transparent;
        }

        .mat-form-field-infix {
          padding: 0.5em 0 0.5em 0;
          .mat-form-field-label {
            font-weight: 700;
          }
        }
      }
    }
    .mat-select-value {
      color: var(--kui-red-400);
      font-size: 16px;
      font-weight: normal;
    }
    .mat-select-arrow-wrapper {
      .mat-select-arrow {
        padding: 0;
        border: 0;
        margin: 0;
        color: transparent;
        width: 16px;
        height: 8px;
        @include icon('arrow-down-red.svg');
      }
    }
  }
  &.mat-option {
    .mat-option-text {
      font-size: 16px;
      color: var(--kui-red-400);
      font-weight: bold;
    }
  }
}

.white-rounded {
  &.mat-form-field {
    .mat-select-value {
      color: var(--kui-white);
      font-size: 16px;
      font-weight: 700;
    }
    .mat-select-arrow-wrapper {
      .mat-select-arrow {
        position: relative;
        top: 3px;
        padding: 0;
        border: 0;
        margin: 0;
        color: transparent;
        width: 16px;
        height: 8px;
        @include icon('arrow-down-white.svg');
      }
    }
    .mat-form-field-wrapper {
      margin: 0;
      padding: 0;
      .mat-form-field-flex {
        margin: 0;
        padding: 10px 20px;
        .mat-form-field-infix {
          margin: 0;
          border: 0;
          padding: 0;
          .mat-form-field-label {
            display: none;
          }
        }
      }
      .mat-form-field-outline {
        top: 0;
        color: var(--kui-white);
        .mat-form-field-outline-start {
          border-radius: 20px 0 0 20px;
          width: 20px !important;
          border-width: 2px;
        }
        .mat-form-field-outline-gap {
          border-width: 2px;
        }
        .mat-form-field-outline-end {
          border-radius: 0 20px 20px 0;
          width: 20px !important;
          border-width: 2px;
        }
      }
    }
  }
}
.mat-select-arrow {
  opacity: 0 !important;
  border-left: unset !important;
  border-right: unset !important;
}
.mat-select-arrow-wrapper {
  background-position: center;
  background-repeat: no-repeat;
  width: 24px;
  height: 24px;
}
