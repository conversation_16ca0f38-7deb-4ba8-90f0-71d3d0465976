@use 'shared' as *;

.modal {
  --kui-modal-padding: 20px;
  --kui-modal-header-height: 114px;

  position: fixed;
  top: 0;
  left: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  z-index: 2001;
  padding: var(--kui-modal-padding) 0;

  @include media-breakpoint-down(sm) {
    --kui-modal-header-height: 134px;
  }

  &-backdrop {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 2000;
    background-color: rgba(37, 48, 53, 0.8);
  }

  &-wrapper {
    color: var(--kui-text-color);
    background-color: var(--kui-white);
    max-width: 680px;
    max-height: calc(100vh - 2 * var(--kui-modal-padding));
    z-index: 2002;
    position: relative;
  }

  &-header {
    padding: 40px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    @include media-breakpoint-down(sm) {
      padding: 30px;
    }

    .icon-x-circle {
      width: 34px;
      height: 34px;
      cursor: pointer;
      margin-left: 30px;
      transition: opacity ease-in-out 300ms;

      &:hover {
        opacity: 0.8;
      }
    }

    .title {
      font-weight: 700;
      font-size: 32px;
      line-height: 34px;
      text-align: left;
      font-family: var(--kui-font-condensed);
      text-transform: uppercase;

      @include media-breakpoint-down(sm) {
        font-size: 25px;
        line-height: 27px;
      }
    }
  }

  &-body {
    margin: auto;
    padding: 0 40px 40px 40px;
    overflow-y: auto;
    overflow-x: hidden;
    max-height: calc(100vh - var(--kui-modal-header-height) - 2 * var(--kui-modal-padding));
    scrollbar-color: var(--kui-red-200) var(--kui-gray-200);
    scrollbar-width: auto;
    -webkit-overflow-scrolling: auto;

    @include media-breakpoint-down(sm) {
      padding: 0 30px 30px 30px;
    }

    &::-webkit-scrollbar {
      width: 6px;
      background-color: var(--kui-gray-200);
      border-radius: 26px;

      &-thumb {
        border-radius: 26px;
        background-color: var(--kui-red-200);
      }
    }
  }
}

html.modal-open,
body.modal-open {
  overflow: hidden;
}
