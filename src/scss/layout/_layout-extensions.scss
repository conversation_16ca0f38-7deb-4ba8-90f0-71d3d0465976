@use 'abstracts/variables' as *;
@use 'abstracts/breakpoints' as *;

.wrapper.with-aside {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  margin-top: 40px;

  @include media-breakpoint-down(md) {
    margin-top: 15px;
  }

  > .left-column {
    display: block;
    width: calc(100% - #{$aside-width} - 40px);
    @include media-breakpoint-down(md) {
      width: 100%;
    }
  }

  > aside {
    display: block;
    width: $aside-width;
    margin-bottom: 30px;
    @include media-breakpoint-down(md) {
      width: 100%;
    }

    > * {
      margin-bottom: $block-bottom-margin;
    }

    .aside-box {
      .heading-line {
        margin-bottom: 18px;
      }

      nso-article-card {
        margin-bottom: 30px;
      }
    }

    kesma-advertisement {
      display: block;

      @include media-breakpoint-down(md) {
        margin-left: -15px;
        margin-right: -15px;
        width: calc(100% + 30px);
      }
    }
  }
}
