// Wrapper width
$global-wrapper-width: 1284px;
$aside-width: 303px;
$block-bottom-margin: 35px;

// Images path
$images-path: '/assets/images/';

// Breakpoints
$grid-breakpoints: (
  xs: 0,
  sm: 576px,
  md: 768px,
  lg: 992px,
  xl: 1200px,
);

// Grid columns
//
// Set the number of columns and specify the width of the gutters.
$grid-columns: 12;
$grid-gutter-width: 24px; // Design: 16px gutter

$container-max-widths: (
  sm: 540px,
  md: 720px,
  lg: 960px,
  xl: 1140px,
);

// Sizes
$header-mobile-height: 49px;
$header-desktop-height: 80px;
$pager-icon-size: 40px;
$input-height: 50px;
$header-max-width: 1284px;

$aside-gap: 16px;
$aside-width: 300px;
$block-bottom-margin: 30px;
