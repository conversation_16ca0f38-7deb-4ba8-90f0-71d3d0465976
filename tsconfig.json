/* To learn more about this file see: https://angular.io/config/tsconfig. */
{
  "compileOnSave": false,
  "compilerOptions": {
    "baseUrl": "./",
    "outDir": "./dist/out-tsc",
    "forceConsistentCasingInFileNames": true,
    "strict": true,
    "noImplicitOverride": true,
    "esModuleInterop": true,
    "noPropertyAccessFromIndexSignature": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "sourceMap": true,
    "declaration": false,
    "experimentalDecorators": true,
    "moduleResolution": "node",
    "importHelpers": true,
    "target": "ES2022",
    "module": "ES2022",
    "lib": [
      "ES2022",
      "dom"
    ],
    "noUnusedLocals": false,
    "noUnusedParameters": true,
    "alwaysStrict": true,
    "strictPropertyInitialization": false,
    "preserveSymlinks": true,
    "useDefineForClassFields": false,
    "skipLibCheck": true,
    "paths": {
      "@angular/*": [ "./node_modules/@angular/*" ],
      "@trendency/*": [
        "./node_modules/@trendecy/*"
      ],
      "rxjs": ["./node_modules/rxjs"],
      "rxjs/*": ["./node_modules/rxjs/*"],
      "ngx-date-fns": ["./node_modules/ngx-date-fns"],
      "ngx-date-fns/*": ["./node_modules/ngx-date-fns/*"],
      "@ng-select": ["./node_modules/@ng-select"],
      "@ng-select/*": ["./node_modules/@ng-select/*"],
    }
  },
  "angularCompilerOptions": {
    "enableI18nLegacyMessageIdFormat": false,
    "strictInjectionParameters": true,
    "strictInputAccessModifiers": true,
    "fullTemplateTypeCheck": false,
    "strictTemplates": false
  }
}
